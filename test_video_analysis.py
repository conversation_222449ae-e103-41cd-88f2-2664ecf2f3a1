#!/usr/bin/env python3
"""
Test script for the AI-powered video analysis functionality.
This script tests the video analysis service without requiring a full video file.
"""

import sys
import os
import tempfile
import json
from unittest.mock import Mock, patch

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set a dummy API key to avoid authentication errors during testing
os.environ['API_KEY'] = 'test_api_key_for_testing'

def test_video_analyzer_initialization():
    """Test VideoAnalyzer initialization"""
    print("Testing VideoAnalyzer initialization...")
    
    try:
        from services.v1.video.video_analysis import VideoAnalyzer
        
        # Test with minimal parameters
        analyzer = VideoAnalyzer(
            video_path="/tmp/test_video.mp4",
            transcription_text="This is a test transcription with amazing content.",
            job_id="test_job_123"
        )
        
        assert analyzer.video_path == "/tmp/test_video.mp4"
        assert analyzer.transcription_text == "This is a test transcription with amazing content."
        assert analyzer.job_id == "test_job_123"
        
        print("✓ VideoAnalyzer initialization test passed")
        return True
        
    except Exception as e:
        print(f"✗ VideoAnalyzer initialization test failed: {str(e)}")
        return False

def test_equal_parts_segmentation():
    """Test equal parts segmentation logic"""
    print("Testing equal parts segmentation...")
    
    try:
        from services.v1.video.video_analysis import VideoAnalyzer
        
        # Mock VideoFileClip to avoid needing actual video files
        with patch('services.v1.video.video_analysis.VideoFileClip') as mock_clip:
            mock_clip.return_value.duration = 300  # 5 minutes
            
            analyzer = VideoAnalyzer(
                video_path="/tmp/test_video.mp4",
                transcription_text="Test transcription",
                job_id="test_job"
            )
            analyzer.video_clip = mock_clip.return_value
            
            # Test single segment
            segments = analyzer._get_equal_parts_segments(num_segments=1, segment_duration=60)
            assert len(segments) == 1
            assert segments[0]['start_time'] >= 0
            assert segments[0]['end_time'] <= 300
            assert segments[0]['score'] == 1.0
            
            # Test multiple segments
            segments = analyzer._get_equal_parts_segments(num_segments=3, segment_duration=60)
            assert len(segments) == 3
            
            print("✓ Equal parts segmentation test passed")
            return True
            
    except Exception as e:
        print(f"✗ Equal parts segmentation test failed: {str(e)}")
        return False

def test_transcription_analysis():
    """Test transcription keyword analysis"""
    print("Testing transcription analysis...")
    
    try:
        from services.v1.video.video_analysis import VideoAnalyzer
        
        # Mock VideoFileClip
        with patch('services.v1.video.video_analysis.VideoFileClip') as mock_clip:
            mock_clip.return_value.duration = 180  # 3 minutes
            
            # Test with engaging transcription
            engaging_text = """
            This is amazing content! You won't believe what happens next.
            The results are shocking and will change everything you know.
            This incredible discovery is the best thing ever found.
            """
            
            analyzer = VideoAnalyzer(
                video_path="/tmp/test_video.mp4",
                transcription_text=engaging_text,
                job_id="test_job"
            )
            analyzer.video_clip = mock_clip.return_value
            
            segments = analyzer._analyze_transcription_keywords()
            
            # Should find segments with engaging keywords
            assert len(segments) > 0
            
            # Check that segments have proper structure
            for segment in segments:
                assert 'start_time' in segment
                assert 'end_time' in segment
                assert 'score' in segment
                assert 'reason' in segment
                assert segment['score'] > 0
            
            print("✓ Transcription analysis test passed")
            return True
            
    except Exception as e:
        print(f"✗ Transcription analysis test failed: {str(e)}")
        return False

def test_segment_merging():
    """Test segment merging logic"""
    print("Testing segment merging...")
    
    try:
        from services.v1.video.video_analysis import VideoAnalyzer
        
        with patch('services.v1.video.video_analysis.VideoFileClip') as mock_clip:
            mock_clip.return_value.duration = 300
            
            analyzer = VideoAnalyzer(
                video_path="/tmp/test_video.mp4",
                job_id="test_job"
            )
            analyzer.video_clip = mock_clip.return_value
            
            # Create test segments with overlaps
            test_segments = [
                {
                    'start_time': 10,
                    'end_time': 30,
                    'audio_score': 0.8,
                    'transcription_score': 0.6,
                    'scene_score': 0.4
                },
                {
                    'start_time': 25,  # Overlaps with previous
                    'end_time': 45,
                    'audio_score': 0.7,
                    'transcription_score': 0.9,
                    'scene_score': 0.3
                },
                {
                    'start_time': 100,  # No overlap
                    'end_time': 120,
                    'audio_score': 0.5,
                    'transcription_score': 0.7,
                    'scene_score': 0.8
                }
            ]
            
            merged = analyzer._merge_overlapping_segments(test_segments, target_duration=60)
            
            # Should merge overlapping segments
            assert len(merged) == 2  # Two non-overlapping segments
            
            # Check combined scores
            for segment in merged:
                assert 'score' in segment
                assert segment['score'] > 0
                assert segment['score'] <= 1.0
            
            print("✓ Segment merging test passed")
            return True
            
    except Exception as e:
        print(f"✗ Segment merging test failed: {str(e)}")
        return False

def test_convenience_function():
    """Test the convenience function"""
    print("Testing convenience function...")
    
    try:
        from services.v1.video.video_analysis import analyze_video_segments
        
        # Mock the VideoAnalyzer context manager
        with patch('services.v1.video.video_analysis.VideoAnalyzer') as mock_analyzer_class:
            mock_analyzer = Mock()
            mock_analyzer.analyze_segments.return_value = [
                {
                    'start_time': 10,
                    'end_time': 70,
                    'score': 0.85,
                    'reason': 'Test segment'
                }
            ]
            mock_analyzer_class.return_value.__enter__.return_value = mock_analyzer
            mock_analyzer_class.return_value.__exit__.return_value = None
            
            segments = analyze_video_segments(
                video_path="/tmp/test.mp4",
                transcription_text="Test transcription",
                segment_method="highlights",
                num_segments=1,
                segment_duration=60,
                job_id="test_job"
            )
            
            assert len(segments) == 1
            assert segments[0]['score'] == 0.85
            
            print("✓ Convenience function test passed")
            return True
            
    except Exception as e:
        print(f"✗ Convenience function test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("Running AI-powered video analysis tests...\n")
    
    tests = [
        test_video_analyzer_initialization,
        test_equal_parts_segmentation,
        test_transcription_analysis,
        test_segment_merging,
        test_convenience_function
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The AI-powered video analysis is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
