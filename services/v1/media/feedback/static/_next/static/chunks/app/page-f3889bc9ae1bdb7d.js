(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{542:()=>{},2088:(e,t,r)=>{Promise.resolve().then(r.bind(r,7684))},7684:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var n=r(5155),s=r(2115),a=r(1935);r(6368);var l=r(4787);let o=()=>{let[e,t]=(0,s.useState)({width:0,height:0}),[r,n]=(0,s.useState)(null),a=(0,s.useCallback)(e=>{null!==e&&n(e)},[]);return(0,s.useEffect)(()=>{if(!r)return;(()=>{let{width:e,height:n}=r.getBoundingClientRect();t({width:e,height:n})})();let e=new ResizeObserver(e=>{for(let r of e){let{width:e,height:n}=r.contentRect;t({width:e,height:n})}});return e.observe(r),()=>{e.disconnect()}},[r]),[a,e]},i=()=>{let[e,t]=(0,s.useState)({width:0,height:0}),[r,n]=(0,s.useState)(null),[a,l]=(0,s.useState)({width:0,height:0}),o=(0,s.useCallback)(e=>{null!==e&&n(e)},[]);return(0,s.useEffect)(()=>{let e=()=>{l({width:window.innerWidth,height:window.innerHeight})};return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[]),(0,s.useEffect)(()=>{if(!r)return;(()=>{let{width:e,height:n}=r.getBoundingClientRect();t({width:e,height:n})})();let e=new ResizeObserver(e=>{for(let r of e){let{width:e,height:n}=r.contentRect;t({width:e,height:n})}});return e.observe(r),()=>{e.disconnect()}},[r]),(0,s.useEffect)(()=>{if(!r||0===e.width||0===e.height)return;let t=a.width-320,n=a.height/e.height,s=e.width*n;s>t?(r.style.width="100%",r.style.marginLeft="auto",r.style.marginRight="auto"):r.style.width="".concat(s,"px")},[a,e]),[o,e]};var c=r(2651);let d=(e,t,r)=>{let[n,a]=(0,s.useState)({data:null,loading:!0,error:null}),l=async()=>{if(e)try{a(e=>({...e,loading:!0}));let{data:n}=await (0,c.A)({method:"post",url:e,data:{action:"load",id:t,type:r},headers:{"Content-Type":"application/json"}});a({data:n,loading:!1,error:null})}catch(e){a({data:null,loading:!1,error:e})}finally{a(e=>({...e,loading:!1}))}};return(0,s.useEffect)(()=>{l()},[e]),{...n,handleFetch:l}},u=(e,t,r)=>{let[n,a]=(0,s.useState)({data:null,loading:!0,error:null,success:!1});return[async n=>{if(e)try{a(e=>({...e,loading:!0,success:!1})),n.id=t,r?n.type=r:n.type="";let{data:s}=await (0,c.A)({method:"post",url:e,data:n,headers:{"Content-Type":"application/json"}});a({data:s,loading:!1,error:null,success:!0})}catch(e){throw Error("Error en post")}finally{a(e=>({...e,loading:!1}))}},{...n}]},m={no_status:{label:"No Status",color:"gray",bgColor:"bg-gray-500/50",textColor:"text-gray-200"},in_progress:{label:"In Progress",color:"blue",bgColor:"bg-blue-500/50",textColor:"text-blue-200"},needs_review:{label:"Needs Review",color:"orange",bgColor:"bg-orange-500/50",textColor:"text-orange-200"},approved:{label:"Approved",color:"green",bgColor:"bg-green-500/50",textColor:"text-green-200"}},h=e=>{let{active:t,message:r,setStateMessage:a}=e;return(0,s.useEffect)(()=>{let e;return t&&(e=setTimeout(()=>{a({show:!1,message:""})},5e3)),()=>{e&&clearTimeout(e)}},[t,a]),(0,n.jsxs)("div",{className:"\n            fixed bottom-4 left-1/2 -translate-x-1/2 \n            transform transition-all duration-300 ease-in-out\n            ".concat(t?"translate-y-0 opacity-100":"translate-y-20 opacity-0 pointer-events-none","\n            z-50 min-w-[300px] max-w-md\n        "),children:[(0,n.jsxs)("div",{className:"bg-red-500/90 backdrop-blur-sm text-white px-6 py-4 rounded-lg shadow-lg  flex items-center justify-between gap-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{d:"M12 4.5C7.85786 4.5 4.5 7.85786 4.5 12C4.5 16.1421 7.85786 19.5 12 19.5C16.1421 19.5 19.5 16.1421 19.5 12C19.5 7.85786 16.1421 4.5 12 4.5ZM3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M12 7.5C12.4142 7.5 12.75 7.83579 12.75 8.25V12.75C12.75 13.1642 12.4142 13.5 12 13.5C11.5858 13.5 11.25 13.1642 11.25 12.75V8.25C11.25 7.83579 11.5858 7.5 12 7.5Z",fill:"currentColor"}),(0,n.jsx)("path",{d:"M12 16.5C12.4142 16.5 12.75 16.1642 12.75 15.75C12.75 15.3358 12.4142 15 12 15C11.5858 15 11.25 15.3358 11.25 15.75C11.25 16.1642 11.5858 16.5 12 16.5Z",fill:"currentColor"})]})}),(0,n.jsx)("p",{className:"text-sm font-medium",children:r})]}),(0,n.jsx)("button",{onClick:()=>a({show:!1,message:""}),className:"flex-shrink-0 hover:bg-white/20 rounded-lg p-1.5 transition-colors duration-200",children:(0,n.jsxs)("svg",{className:"w-4 h-4",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{d:"M6.75 6.75L17.25 17.25",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,n.jsx)("path",{d:"M17.25 6.75L6.75 17.25",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})})]}),(0,n.jsx)("div",{className:"absolute bottom-0 left-0 w-full h-1 bg-white/20 rounded-b-lg overflow-hidden",children:(0,n.jsx)("div",{className:"h-full bg-white/40 transition-all duration-[5000ms] ease-linear",style:{width:t?"0%":"100%",transitionProperty:"width"}})})]})},g=e=>{let{isVideoLoaded:t,isImage:r,imageRef:a,comments:l,playerRef:o,fabricCanvasRef:i,canva:c,dimensions:d,setIsDrawingMode:m,updateHandlerRef:h,queryParams:g,toggleDrawingMode:p,setComments:x,isDrawingMode:v,data:w,clearCanvas:f,handleFetch:y,setVideoStatus:b,successPostVideoStatus:j,setStateMessage:C,dataComments:k,setDataComments:N,optimisticComments:S,setOptimisticComments:E,lastUpdatedComments:D,setLastUpdatedComments:M}=e,[R,q]=(0,s.useState)(null),[L,O]=(0,s.useState)(!1);(0,s.useEffect)(()=>{N(l)},[l]);let _=(0,s.useRef)(k);(0,s.useEffect)(()=>{M(l)},[]);let[W,T]=(0,s.useTransition)(),z=()=>{if(!o.current||!l.length||r)return;o.current.pause(),document.querySelectorAll(".vjs-marker").forEach(e=>e.remove());let e=o.current.el().querySelector(".vjs-progress-control"),t=null==e?void 0:e.querySelector(".vjs-progress-holder");t&&l.forEach(e=>{let r=document.createElement("div");r.className="vjs-marker";let n=o.current.duration(),s=e.timestamp/n*100;r.style.left="".concat(s,"%"),r.addEventListener("click",t=>{t.stopPropagation(),J(e.timestamp,e.drawing,e.canvasWidth,e.canvasHeight)}),r.addEventListener("mouseenter",()=>{r.style.width="1rem",r.style.height="400%"}),r.addEventListener("mouseleave",()=>{r.style.width="0.5rem",r.style.height="200%"}),r.title="".concat(I(e.timestamp),": ").concat(e.text),t.appendChild(r)})};(0,s.useEffect)(()=>{if(o.current&&!r)return o.current.on("loadedmetadata",z),z(),()=>{o.current&&o.current.off("loadedmetadata",z)}},[l,t,d]);let A=e=>{let[t,r,n]=I(e.timestamp).split(":");q({...e,editText:e.text,hours:t,minutes:r,seconds:n})},B=(e,t)=>{q(r=>({...r,[e]:t}))},H=(e,t)=>{"Enter"===e.key&&t()},P=async e=>{let t=D.map(t=>t.id===e?{...t,commentDone:!t.commentDone}:t);M(t),O(!1);let r={action:"save",lastUpdated:Date.now(),comments:t};O(!1),T(async()=>{try{M(t),E({type:"check",updatedComments:t}),await Y(r),N(t),x(t),O(!0)}catch(e){console.error("Error updating comments:",e),C({show:!0,message:"Error checking/unchecking comment"})}})},I=e=>{let t=new Date(0);return t.setSeconds(e),t.toISOString().substring(11,19)},U=(e,t,r)=>3600*e+60*t+r,J=(e,t,r,n)=>{o.current&&(v&&p(),o.current.pause(),o.current.one("seeked",()=>{if(i.current&&t){let s=document.querySelector(".canvas-container"),a=document.querySelector(".canvi"),l=document.querySelector(".upper-canvas");s&&(s.style.display="block",s.style.pointerEvents="none"),a&&l&&(a.style.pointerEvents="none",l.style.pointerEvents="none"),i.current.clear();let c=JSON.parse(JSON.stringify(t)),u=d.width/r,m=d.height/n;c.objects&&c.objects.forEach(e=>{let t=e.strokeWidth;e.left=e.left*u,e.top=e.top*m,e.scaleX=e.scaleX*u,e.scaleY=e.scaleY*m,e.strokeWidth=t/((u+m)/2)}),i.current.loadFromJSON(c,()=>{i.current.renderAll(),setTimeout(()=>{i.current.renderAll()},50)});let g=()=>{.1>Math.abs(o.current.currentTime()-e)?(s&&(s.style.display="block"),i.current.renderAll()):s&&f()};h.current&&o.current.off("timeupdate",h.current),g(),o.current.on("timeupdate",g),h.current=g}}),o.current.currentTime(e))},F=(e,t,r,n)=>{let s=document.querySelector(".canvas-container"),a=document.querySelector(".canvi"),l=document.querySelector(".upper-canvas");s&&(s.style.display="block",s.style.pointerEvents="none"),a&&l&&(a.style.pointerEvents="none",l.style.pointerEvents="none"),m(!1);let o=d.width/r,i=d.height/n;c.clear();let u=JSON.parse(JSON.stringify(t));u.objects.forEach(e=>{let t=e.strokeWidth;e.left=e.left*o,e.top=e.top*i,e.scaleX=e.scaleX*o,e.scaleY=e.scaleY*i,e.strokeWidth=t/((o+i)/2)}),c.loadFromJSON(u,()=>{})},V=async()=>{if(!R)return;let e=i.current?i.current.toJSON():null,t=parseInt(R.hours)||0,r=parseInt(R.minutes)||0,n=parseInt(R.seconds)||0,s=U(t=Math.min(Math.max(t,0),23),r=Math.min(Math.max(r,0),59),n=Math.min(Math.max(n,0),59)),a=Date.now(),l={...R,id:a,text:R.editText,timestamp:s,formattedTime:I(s),drawing:e,canvasWidth:d.width,canvasHeight:d.height};try{let e=D.map(e=>e.id===R.id?l:e),t={action:"save",lastUpdated:Date.now(),comments:e};_.current=k,M(e),T(async()=>{try{v&&p(),M(e),E({type:"update",updatedComments:e}),await Y(t),N(e),x(e),q(null)}catch(e){console.error("Error updating comments:",e),C({show:!0,message:"Error updating comment"})}})}catch(e){console.error("Error updating comments:",e)}},X=async()=>{if(!R)return;v&&p();let e=l.filter(e=>e.id!==R.id),t={action:"save",lastUpdated:Date.now(),comments:e};_.current=k,T(async()=>{try{E({type:"delete",updatedComments:e}),await Y(t),N(e),M(e)}catch(e){N(_.current),E(),C({show:!0,message:"Error deleting comment"})}}),f(),x(e),q(null)},[Y,{data:Z,loading:K,error:$,success:G}]=u(g.webhook_url,g.id,g.type);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"flex items-center gap-2 mb-4",children:(0,n.jsx)("h3",{className:"text-sm font-medium text-gray-200 py-2",children:"Comments"})}),(0,n.jsx)("div",{className:"flex-grow overflow-y-auto space-y-2 text-sm scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent",children:(null==S?void 0:S.length)>0?S.map(e=>R&&R.id===e.id?(0,n.jsxs)("div",{className:"p-2.5 bg-gray-700/50 backdrop-blur-sm rounded-lg",children:[!r&&(0,n.jsxs)("div",{className:"flex gap-1 mb-2",children:[(0,n.jsx)("input",{type:"text",className:"border border-gray-400 p-1 w-10 text-center text-white",value:R.hours,onChange:e=>B("hours",e.target.value)}),":",(0,n.jsx)("input",{type:"text",className:"border border-gray-400 p-1 w-10 text-center text-white",value:R.minutes,onChange:e=>B("minutes",e.target.value)}),":",(0,n.jsx)("input",{type:"text",className:"border border-gray-400 p-1 w-10 text-center text-white",value:R.seconds,onChange:e=>B("seconds",e.target.value)})]}),(0,n.jsx)("input",{type:"text",className:"border border-gray-400 p-2 w-full text-white",value:R.editText,onChange:e=>q({...R,editText:e.target.value}),onKeyPress:e=>H(e,V)}),(0,n.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,n.jsx)("button",{className:"bg-green-500 text-white px-4 py-1",onClick:V,children:"Save"}),(0,n.jsx)("button",{className:"bg-gray-500 text-white px-4 py-1",onClick:()=>{q(null),v&&p()},children:"Cancel"}),(0,n.jsx)("button",{className:"bg-red-500 text-white px-4 py-1",onClick:X,children:"Delete"})]})]},e.id):(0,n.jsxs)("div",{className:"p-2.5 bg-gray-700/50 backdrop-blur-sm rounded-lg hover:bg-gray-600/50 transition-all duration-200",onClick:()=>r?F(e.timestamp,e.drawing,e.canvasWidth,e.canvasHeight):J(e.timestamp,e.drawing,e.canvasWidth,e.canvasHeight),children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,n.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,n.jsx)("button",{onClick:()=>P(e.id),className:"relative w-4 h-4 flex items-center justify-center",children:(0,n.jsx)("div",{className:"\n                                w-4 h-4 border-2 rounded \n                                transition-all duration-200 ease-in-out\n                                ".concat(e.commentDone?"border-green-500 bg-green-500":"border-gray-400 bg-transparent","\n                              "),children:e.commentDone&&(0,n.jsx)("svg",{className:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})})}),!r&&(0,n.jsx)("span",{className:"text-blue-400 cursor-pointer hover:text-blue-300 text-xs ".concat(e.commentDone?"line-through text-gray-400":""),onClick:()=>J(e.timestamp,e.drawing,e.canvasWidth,e.canvasHeight),children:null==e?void 0:e.formattedTime})]}),(0,n.jsx)("button",{className:"text-gray-400 hover:text-white transition-colors duration-200 text-xs",onClick:()=>A(e),children:"✏️"})]}),(0,n.jsx)("div",{className:"text-gray-200 break-words text-xs pl-6 ".concat(e.commentDone?"line-through text-gray-400":""),children:e.text})]},e.id)):(0,n.jsx)("p",{className:"text-gray-400 text-center py-4 text-xs",children:"No comments yet"})})]})},p=e=>{let{isVideoLoaded:t,playerRef:r,fabricCanvasRef:a,dimensions:l,setComments:o,comments:i,clearCanvas:c,isDrawingMode:d,toggleDrawingMode:m,queryParams:h,setStateMessage:g,dataComments:p,setDataComments:x,optimisticComments:v,setOptimisticComments:w,canva:f,isImage:y,setIsDrawingMode:b,lastUpdatedComments:j,setLastUpdatedComments:C}=e,[k,N]=(0,s.useState)(""),S=(0,s.useRef)(null),[E,D]=(0,s.useTransition)(),[M,{data:R,loading:q,error:L,success:O}]=u(h.webhook_url,h.id,h.type),_=async()=>{let e;let t=a.current?a.current.toJSON():null;if(0===t.objects.length&&""===k)return;e=t.objects.length>0&&""===k?"*drawing only*":k;let n={id:Date.now(),text:e,timestamp:y?null:r.current.currentTime(),formattedTime:y?null:(e=>{let t=new Date(0);return t.setSeconds(e),t.toISOString().substring(11,19)})(r.current.currentTime()),drawing:t,canvasWidth:l.width,canvasHeight:l.height,commentDone:!1},s={action:"save",lastUpdated:Date.now(),comments:[...i,n]};N(""),b(!1),C(s.comments),D(async()=>{try{!y&&s.comments&&Array.isArray(s.comments)&&s.comments.sort((e,t)=>null===e.timestamp?1:null===t.timestamp?-1:e.timestamp-t.timestamp),w({type:"add",updatedComments:s.comments});let e=document.querySelector(".canvas-container");e&&(e.style.display="none");let t=document.querySelector(".canvi");t&&(t.style.pointerEvents="none");let n=document.querySelector(".upper-canvas");n&&(n.style.pointerEvents="none"),y||r.current.controls(!0),f.clear(),C(s.comments),await M(s),x(s.comments),o(s.comments)}catch(a){console.error("Error adding comment:",a),g({show:!0,message:"Error adding comment"}),N(s.comments[s.comments.length-1].text);let e=document.querySelector(".canvas-container");e&&(e.style.display="block");let t=document.querySelector(".canvi");t&&(t.style.pointerEvents="auto");let r=document.querySelector(".upper-canvas");r&&(r.style.pointerEvents="auto"),f.clear();let n=JSON.parse(JSON.stringify(s.comments[s.comments.length-1].drawing));f.loadFromJSON(n),b(!0)}}),S.current&&(S.current.style.height="auto")};return(0,n.jsx)("div",{children:t&&(0,n.jsx)("div",{className:"  mt-4 w-full flex justify-center",children:(0,n.jsxs)("div",{className:"relative w-[600px] max-w-full",children:[(0,n.jsx)("textarea",{ref:S,className:"w-full bg-gray-800/50 backdrop-blur-sm text-white px-4 py-3 pr-40 rounded-xl focus:ring-2 focus:ring-blue-500 focus:outline-none min-h-[44px] max-h-[200px] resize-y md:text-sm text-base shadow-lg",value:k,onChange:e=>N(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),_())},placeholder:"Add a comment...",style:{overflow:"hidden",lineHeight:"1.5",fontSize:"14px",WebkitTextSizeAdjust:"100%"},onInput:e=>{e.target.style.height="auto",e.target.style.height=e.target.scrollHeight+"px"}}),(0,n.jsxs)("div",{className:"absolute right-2 top-2 flex gap-2",children:[(0,n.jsx)("button",{className:"p-1.5 rounded-md transition-all duration-200 ".concat(d?"bg-red-500 hover:bg-red-600":"bg-gray-600 hover:bg-gray-500"),onClick:()=>m(),title:d?"Stop Drawing":"Start Drawing",children:d?(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-white",viewBox:"0 0 20 20",fill:"currentColor",children:[(0,n.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 002 0V8a1 1 0 00-1-1z",clipRule:"evenodd"}),(0,n.jsx)("path",{fillRule:"evenodd",d:"M12 7a1 1 0 00-1 1v4a1 1 0 002 0V8a1 1 0 00-1-1z",clipRule:"evenodd"})]}):(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-white",viewBox:"0 0 20 20",fill:"currentColor",children:(0,n.jsx)("path",{fillRule:"evenodd",d:"M13.293 3.293a1 1 0 011.414 0l2 2a1 1 0 010 1.414l-9 9a1 1 0 01-.39.242l-3 1a1 1 0 01-1.266-1.265l1-3a1 1 0 01.242-.391l9-9zM14 4l2 2-9 9-3 1 1-3 9-9z",clipRule:"evenodd"})})}),(0,n.jsx)("button",{className:"bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md transition-all duration-200 text-sm",onClick:_,children:"Add"})]})]})})})},x=()=>{var e,t;let[r,c]=(0,s.useState)({show:!1,message:"Hola muuundo"}),[x,v]=(0,s.useState)(""),[w,f]=(0,s.useState)([]),[y,b]=(0,s.useState)(!0),[j,C]=(0,s.useState)([]),[k,N]=(0,s.useState)(!1),[S,E]=(0,s.useState)(!1),[D,M]=(0,s.useState)(!1),[R,q]=(0,s.useState)([]),[L,O]=(0,s.useState)(!1),_=(0,s.useRef)(null),W=(0,s.useRef)(null),T=(0,s.useRef)(null),z=(0,s.useRef)(null),A=(0,s.useRef)(null),B=(0,s.useRef)(()=>{}),[H,P]=(0,s.useState)(null),[I,U]=o(),[J,F]=i(),[V,X]=(0,s.useState)(()=>{{let e=new URLSearchParams(window.location.search);return{webhook_url:e.get("webhook_url"),id:e.get("id"),type:e.get("type")}}}),[Y,Z]=(0,s.useOptimistic)(j,(e,t)=>{switch(null==t?void 0:t.type){case"delete":case"update":case"check":case"add":return t.updatedComments;default:return e}}),[K,$]=(0,s.useState)("no_status"),G=(0,s.useRef)(K),[Q,ee]=(0,s.useOptimistic)(K,(e,t)=>(null==t?void 0:t.type)==="save"?t.status:e),[et,er]=(0,s.useTransition)(),{data:en,loading:es,error:ea,handleFetch:el}=d(V.webhook_url,V.id,V.type),[eo,{data:ei,loading:ec,error:ed,success:eu}]=u(V.webhook_url,V.id,V.type);(0,s.useEffect)(()=>{en&&y&&b(!1)},[en]),(0,s.useEffect)(()=>{en&&f(en.comments)},[en]),(0,s.useEffect)(()=>{if(z.current.width=U.width,z.current.height=U.height,en&&!ev(en.video_url)&&W.current&&!T.current){T.current=(0,a.A)(W.current,{controls:!0,fluid:!0,bigPlayButton:!1,userActions:{hotkeys:!0,doubleClick:!0},controlBar:{progressControl:!0}});let e=document.querySelector(".vjs-control-bar");e&&(e.style.display="flex");let t=T.current;t.on("play",()=>{if(A.current){A.current.clear();let e=document.querySelector(".canvas-container");e&&(e.style.display="none")}}),t.on("seeking",()=>{}),t.src({src:null==en?void 0:en.video_url,type:"video/mp4"}),N(!0);try{if(z.current&&!A.current){let e=new l.Hl(z.current);A.current=e,P(e),e.freeDrawingBrush=new l.mW(e),e.freeDrawingBrush.width=4,e.freeDrawingBrush.color="#ff0000",e.isDrawingMode=!1,T.current.on("loadedmetadata",()=>{let t=T.current.el();t&&e&&(e.setDimensions({width:t.offsetWidth,height:t.offsetHeight},{cssOnly:!1}),e.renderAll());let r=document.querySelector(".canvas-container");r&&(r.style.display="none")})}}catch(e){console.error("Fabric canvas initialization error:",e)}}},[en]),(0,s.useEffect)(()=>{if(en&&ev(en.video_url)&&(O(ev(en.video_url)),_.current))try{if(z.current&&!A.current){let e=new l.Hl(z.current);A.current=e,P(e),e.freeDrawingBrush=new l.mW(e),e.freeDrawingBrush.width=4,e.freeDrawingBrush.color="#ff0000",e.isDrawingMode=!1;let t=_.current;e.setDimensions({width:t.naturalWidth,height:t.naturalHeight},{cssOnly:!1});let r=document.querySelector(".canvas-container");r&&(r.style.display="none"),e.renderAll()}}catch(e){console.error("Fabric canvas initialization error:",e)}},[en,_.current]),(0,s.useEffect)(()=>{if(H){let e=H.width,t=H.height,r=U.width/e,n=U.height/t;H.setDimensions({width:U.width,height:U.height}),H.getObjects().forEach(e=>{let t=e.strokeWidth;e.left=e.left*r,e.top=e.top*n,e.scaleX=e.scaleX*r,e.scaleY=e.scaleY*n,e.strokeWidth=t/((r+n)/2),e.setCoords()}),H.renderAll()}},[U]);let em=()=>{A.current&&(E(!S),A.current.isDrawingMode=!S,A.current.clear());let e=document.querySelector(".canvas-container");e&&(e.style.display=S?"none":"block");let t=document.querySelector(".canvi");t&&(t.style.pointerEvents=S?"none":"auto");let r=document.querySelector(".upper-canvas");r&&(r.style.pointerEvents=S?"none":"auto"),L||(S||T.current.pause(),document.querySelector(".video-js"),S?T.current.controls(!0):T.current.controls(!1))},eh=()=>{A.current&&A.current.clear()},eg=async(e,t)=>{try{let r={action:"save",lastUpdated:Date.now(),status:e,comments:t};er(async()=>{try{G.current=K,ee({type:"save",status:e}),await eo(r),$(e)}catch(e){$(G.current),ee(),c({show:!0,message:"Error saving the state"})}})}catch(e){console.error("Error al guardar el estado:",e)}};(0,s.useEffect)(()=>{let e=()=>{let e=new URLSearchParams(window.location.search);X({webhook_url:e.get("webhook_url"),id:e.get("id")})};return window.addEventListener("popstate",e),()=>window.removeEventListener("popstate",e)},[]);let[ep,ex]=(0,s.useState)(!0);(0,s.useEffect)(()=>{en&&en.status&&($(en.status),N(!0))},[en]);let ev=e=>/\.(jpg|jpeg|png|gif|bmp|webp|svg)(\?.*)?$/i.test(e);return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-900",children:[(0,n.jsxs)("div",{className:"flex flex-col lg:flex-row min-h-screen",children:[(0,n.jsx)("div",{className:"flex-1 transition-all duration-300 ease-in-out \n          ".concat(ep?"lg:pr-80":"lg:pr-0","\n          ").concat(ep?"mb-[50vh]":"mb-0"," lg:mb-0\n        "),children:(0,n.jsxs)("div",{className:"container-principal w-[100%] flex flex-col justify-center ",ref:J,children:[(0,n.jsxs)("div",{className:"video-container relative w-full mt-4 ",ref:I,children:[L?(0,n.jsx)("img",{ref:_,src:en.video_url,style:{width:"100%",height:"100%",objectFit:"contain",objectPosition:"center"}}):(0,n.jsx)("video",{ref:W,className:"video-js vjs-big-play-centered w-full vjs-big-play-button-hide",playsInline:!0,controls:!0}),(0,n.jsx)("canvas",{ref:z,className:"canvi absolute top-0 left-0 pointer-events-auto w-full h-full",style:{zIndex:1,width:"100%",height:"100%"}})]}),(0,n.jsx)(p,{setIsDrawingMode:E,isVideoLoaded:k,playerRef:T,fabricCanvasRef:A,dimensions:U,setComments:f,comments:w,handlePostComment:eo,clearCanvas:eh,isDrawingMode:S,toggleDrawingMode:em,queryParams:V,dataComments:j,setDataComments:C,setOptimisticComments:Z,setStateMessage:c,canva:H,isImage:L,lastUpdatedComments:R,setLastUpdatedComments:q})]})}),!ep&&(0,n.jsx)("button",{onClick:()=>ex(e=>!e),className:"fixed z-50 bg-gray-800/50 backdrop-blur-sm p-2 rounded-full hover:bg-gray-700/50 transition-all duration-200 right-4 bottom-4 lg:top-4 lg:bottom-auto",title:ep?"Hide Comments":"Show Comments",children:(0,n.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-6 w-6 text-white",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"})})}),(0,n.jsx)("div",{className:"fixed transition-all duration-300 ease-in-out\n          lg:w-80 w-full h-[min(50vh,300px)] lg:h-screen\n          lg:right-0 right-0\n          lg:top-0 bottom-0\n          bg-gray-800/50 backdrop-blur-sm\n          ".concat(ep?"translate-y-0 lg:translate-x-0":"translate-y-full lg:translate-x-full","\n        "),children:k&&(0,n.jsxs)("div",{className:"h-full p-4 flex flex-col",children:[(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsx)("div",{className:"flex items-center gap-2",children:(0,n.jsx)("label",{className:"text-sm font-medium text-gray-200",children:"Video Status"})}),(0,n.jsx)("div",{className:"flex justify-end items-center",children:(0,n.jsx)("a",{href:"https://www.skool.com/no-code-architects",target:"_blank",rel:"noopener noreferrer",className:"cursor-pointer hover:opacity-80 transition-opacity",children:(0,n.jsx)("img",{src:"/logo.png",alt:"logo",className:"w-10 h-10"})})})]}),(0,n.jsx)("select",{value:Q,onChange:e=>{eg(e.target.value,w)},className:"w-full px-3 py-2 rounded-lg transition-colors duration-200 text-sm ".concat(null===(e=m[K])||void 0===e?void 0:e.bgColor," ").concat(null===(t=m[K])||void 0===t?void 0:t.textColor),style:{appearance:"none",backgroundImage:"url(\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e\")",backgroundRepeat:"no-repeat",backgroundPosition:"right 0.75rem center",backgroundSize:"1rem",paddingRight:"2.5rem"},children:Object.entries(m).map(e=>{let[t,{label:r,bgColor:s,textColor:a}]=e;return(0,n.jsx)("option",{value:t,className:"".concat(s," ").concat(a),style:{backgroundColor:"rgba(0,0,0,0.5)"},children:r},t)})})]}),(0,n.jsx)(g,{isVideoLoaded:k,comments:w,setComments:f,handlePostComment:eo,handleFetch:el,playerRef:T,fabricCanvasRef:A,canva:H,dimensions:U,setIsDrawingMode:E,updateHandlerRef:B,queryParams:V,toggleDrawingMode:em,isDrawingMode:S,data:en,clearCanvas:eh,setVideoStatus:$,setStateMessage:c,dataComments:j,setDataComments:C,optimisticComments:Y,setOptimisticComments:Z,imageRef:_,isImage:L,lastUpdatedComments:R,setLastUpdatedComments:q})]})})]}),y&&(0,n.jsx)("div",{className:"fixed inset-0 bg-gray-900 z-50 flex items-center justify-center",children:(0,n.jsxs)("div",{className:"flex flex-col items-center",children:[(0,n.jsx)("img",{src:"/logo.png",alt:"logo",className:"w-20 h-20 animate-pulse"}),(0,n.jsxs)("div",{className:"mt-4 flex items-center gap-2",children:[(0,n.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0s"}}),(0,n.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}}),(0,n.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-bounce",style:{animationDelay:"0.4s"}})]})]})}),(0,n.jsx)(h,{active:r.show,message:r.message,setStateMessage:c})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[953,776,742,821,441,587,358],()=>t(2088)),_N_E=e.O()}]);