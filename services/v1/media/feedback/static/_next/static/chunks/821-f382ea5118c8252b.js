(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[821],{364:(e,t,r)=>{var n=r(8202),i=e.exports={WebVTT:r(7219),VTTCue:r(5666),VTTRegion:r(379)};n.vttjs=i,n.WebVTT=i.WebVTT;var a=i.VTTCue,o=i.VTTRegion,s=n.VTTCue,u=n.VTTRegion;i.shim=function(){n.VTTCue=a,n.VTTRegion=o},i.restore=function(){n.VTTCue=s,n.VTTRegion=u},n.VTTCue||i.shim()},379:e=>{var t={"":!0,up:!0};function r(e){return"number"==typeof e&&e>=0&&e<=100}e.exports=function(){var e=100,n=3,i=0,a=100,o=0,s=100,u="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return e},set:function(t){if(!r(t))throw Error("Width must be between 0 and 100.");e=t}},lines:{enumerable:!0,get:function(){return n},set:function(e){if("number"!=typeof e)throw TypeError("Lines must be set to a number.");n=e}},regionAnchorY:{enumerable:!0,get:function(){return a},set:function(e){if(!r(e))throw Error("RegionAnchorX must be between 0 and 100.");a=e}},regionAnchorX:{enumerable:!0,get:function(){return i},set:function(e){if(!r(e))throw Error("RegionAnchorY must be between 0 and 100.");i=e}},viewportAnchorY:{enumerable:!0,get:function(){return s},set:function(e){if(!r(e))throw Error("ViewportAnchorY must be between 0 and 100.");s=e}},viewportAnchorX:{enumerable:!0,get:function(){return o},set:function(e){if(!r(e))throw Error("ViewportAnchorX must be between 0 and 100.");o=e}},scroll:{enumerable:!0,get:function(){return u},set:function(e){var r="string"==typeof e&&!!t[e.toLowerCase()]&&e.toLowerCase();!1===r?console.warn("Scroll: an invalid or illegal string was specified."):u=r}}})}},400:e=>{"use strict";function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var r=function(){function e(){this.typeToInterceptorsMap_=new Map,this.enabled_=!1}var r=e.prototype;return r.getIsEnabled=function(){return this.enabled_},r.enable=function(){this.enabled_=!0},r.disable=function(){this.enabled_=!1},r.reset=function(){this.typeToInterceptorsMap_=new Map,this.enabled_=!1},r.addInterceptor=function(e,t){this.typeToInterceptorsMap_.has(e)||this.typeToInterceptorsMap_.set(e,new Set);var r=this.typeToInterceptorsMap_.get(e);return!r.has(t)&&(r.add(t),!0)},r.removeInterceptor=function(e,t){var r=this.typeToInterceptorsMap_.get(e);return!!(r&&r.has(t))&&(r.delete(t),!0)},r.clearInterceptorsByType=function(e){return!!this.typeToInterceptorsMap_.get(e)&&(this.typeToInterceptorsMap_.delete(e),this.typeToInterceptorsMap_.set(e,new Set),!0)},r.clear=function(){return!!this.typeToInterceptorsMap_.size&&(this.typeToInterceptorsMap_=new Map,!0)},r.getForType=function(e){return this.typeToInterceptorsMap_.get(e)||new Set},r.execute=function(e,r){for(var n,i=this.getForType(e),a=function(e,r){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,r){if(e){if("string"==typeof e)return t(e,void 0);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return t(e,r)}}(e))){n&&(e=n);var i=0;return function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(i);!(n=a()).done;){var o=n.value;try{r=o(r)}catch(e){}}return r},e}();e.exports=r},559:(e,t,r)=>{"use strict";r.d(t,{Tt:()=>S,Ze:()=>M,qg:()=>eO,i6:()=>ex});var n=r(9512),i=r(8202),a=r.n(i),o=function(e,t,r){t.forEach(function(t){for(var n in e.mediaGroups[t])for(var i in e.mediaGroups[t][n])r(e.mediaGroups[t][n][i],t,n,i)})},s=r(9410),u=r(6002);let l=e=>!!e&&"object"==typeof e,c=(...e)=>e.reduce((e,t)=>("object"!=typeof t||Object.keys(t).forEach(r=>{Array.isArray(e[r])&&Array.isArray(t[r])?e[r]=e[r].concat(t[r]):l(e[r])&&l(t[r])?e[r]=c(e[r],t[r]):e[r]=t[r]}),e),{}),f=e=>Object.keys(e).map(t=>e[t]),p=(e,t)=>{let r=[];for(let n=e;n<t;n++)r.push(n);return r},h=e=>e.reduce((e,t)=>e.concat(t),[]),d=e=>{if(!e.length)return[];let t=[];for(let r=0;r<e.length;r++)t.push(e[r]);return t},g=(e,t)=>e.reduce((e,r,n)=>(r[t]&&e.push(n),e),[]),m=(e,t)=>f(e.reduce((e,r)=>(r.forEach(r=>{e[t(r)]=r}),e),{}));var b={INVALID_NUMBER_OF_PERIOD:"INVALID_NUMBER_OF_PERIOD",DASH_EMPTY_MANIFEST:"DASH_EMPTY_MANIFEST",DASH_INVALID_XML:"DASH_INVALID_XML",NO_BASE_URL:"NO_BASE_URL",SEGMENT_TIME_UNSPECIFIED:"SEGMENT_TIME_UNSPECIFIED",UNSUPPORTED_UTC_TIMING_SCHEME:"UNSUPPORTED_UTC_TIMING_SCHEME"};let y=({baseUrl:e="",source:t="",range:r="",indexRange:i=""})=>{let o={uri:t,resolvedUri:(0,n.A)(e||"",t)};if(r||i){let e;let t=(r||i).split("-"),n=a().BigInt?a().BigInt(t[0]):parseInt(t[0],10),s=a().BigInt?a().BigInt(t[1]):parseInt(t[1],10);n<Number.MAX_SAFE_INTEGER&&"bigint"==typeof n&&(n=Number(n)),s<Number.MAX_SAFE_INTEGER&&"bigint"==typeof s&&(s=Number(s)),"bigint"==typeof(e="bigint"==typeof s||"bigint"==typeof n?a().BigInt(s)-a().BigInt(n)+a().BigInt(1):s-n+1)&&e<Number.MAX_SAFE_INTEGER&&(e=Number(e)),o.byterange={length:e,offset:n}}return o},E=e=>{let t;return t="bigint"==typeof e.offset||"bigint"==typeof e.length?a().BigInt(e.offset)+a().BigInt(e.length)-a().BigInt(1):e.offset+e.length-1,`${e.offset}-${t}`},v=e=>(e&&"number"!=typeof e&&(e=parseInt(e,10)),isNaN(e))?null:e,T={static(e){let{duration:t,timescale:r=1,sourceDuration:n,periodDuration:i}=e,a=v(e.endNumber),o=t/r;return"number"==typeof a?{start:0,end:a}:"number"==typeof i?{start:0,end:i/o}:{start:0,end:n/o}},dynamic(e){let{NOW:t,clientOffset:r,availabilityStartTime:n,timescale:i=1,duration:a,periodStart:o=0,minimumUpdatePeriod:s=0,timeShiftBufferDepth:u=1/0}=e,l=v(e.endNumber),c=(t+r)/1e3,f=n+o,p=Math.ceil((c+s-f)*i/a),h=Math.floor((c-f-u)*i/a),d=Math.floor((c-f)*i/a);return{start:Math.max(0,h),end:"number"==typeof l?l:Math.min(p,d)}}},w=e=>t=>{let{duration:r,timescale:n=1,periodStart:i,startNumber:a=1}=e;return{number:a+t,duration:r/n,timeline:i,time:t*r}},D=e=>{let{type:t,duration:r,timescale:n=1,periodDuration:i,sourceDuration:a}=e,{start:o,end:s}=T[t](e),u=p(o,s).map(w(e));if("static"===t){let e=u.length-1;u[e].duration=("number"==typeof i?i:a)-r/n*e}return u},A=e=>{let{baseUrl:t,initialization:r={},sourceDuration:n,indexRange:i="",periodStart:a,presentationTime:o,number:s=0,duration:u}=e;if(!t)throw Error(b.NO_BASE_URL);let l=y({baseUrl:t,source:r.sourceURL,range:r.range}),c=y({baseUrl:t,source:t,indexRange:i});if(c.map=l,u){let t=D(e);t.length&&(c.duration=t[0].duration,c.timeline=t[0].timeline)}else n&&(c.duration=n,c.timeline=a);return c.presentationTime=o||a,c.number=s,[c]},S=(e,t,r)=>{let n;let i=e.sidx.map?e.sidx.map:null,o=e.sidx.duration,s=e.timeline||0,u=e.sidx.byterange,l=u.offset+u.length,c=t.timescale,f=t.references.filter(e=>1!==e.referenceType),p=[],h=e.endList?"static":"dynamic",d=e.sidx.timeline,g=d,m=e.mediaSequence||0;n="bigint"==typeof t.firstOffset?a().BigInt(l)+t.firstOffset:l+t.firstOffset;for(let e=0;e<f.length;e++){let u;let l=t.references[e],f=l.referencedSize,b=l.subsegmentDuration;u="bigint"==typeof n?n+a().BigInt(f)-a().BigInt(1):n+f-1;let y={baseUrl:r,timescale:c,timeline:s,periodStart:d,presentationTime:g,number:m,duration:b,sourceDuration:o,indexRange:`${n}-${u}`,type:h},E=A(y)[0];i&&(E.map=i),p.push(E),"bigint"==typeof n?n+=a().BigInt(f):n+=f,g+=b/c,m++}return e.segments=p,e},N=["AUDIO","SUBTITLES"],R=1/60,O=e=>m(e,({timeline:e})=>e).sort((e,t)=>e.timeline>t.timeline?1:-1),x=(e,t)=>{for(let r=0;r<e.length;r++)if(e[r].attributes.NAME===t)return e[r];return null},I=e=>{let t=[];return o(e,N,(e,r,n,i)=>{t=t.concat(e.playlists||[])}),t},C=({playlist:e,mediaSequence:t})=>{e.mediaSequence=t,e.segments.forEach((t,r)=>{t.number=e.mediaSequence+r})},L=({oldPlaylists:e,newPlaylists:t,timelineStarts:r})=>{t.forEach(t=>{t.discontinuitySequence=r.findIndex(function({timeline:e}){return e===t.timeline});let n=x(e,t.attributes.NAME);if(!n||t.sidx)return;let i=t.segments[0],a=n.segments.findIndex(function(e){return Math.abs(e.presentationTime-i.presentationTime)<R});if(-1===a){C({playlist:t,mediaSequence:n.mediaSequence+n.segments.length}),t.segments[0].discontinuity=!0,t.discontinuityStarts.unshift(0),(!n.segments.length&&t.timeline>n.timeline||n.segments.length&&t.timeline>n.segments[n.segments.length-1].timeline)&&t.discontinuitySequence--;return}n.segments[a].discontinuity&&!i.discontinuity&&(i.discontinuity=!0,t.discontinuityStarts.unshift(0),t.discontinuitySequence--),C({playlist:t,mediaSequence:n.segments[a].number})})},U=({oldManifest:e,newManifest:t})=>{let r=e.playlists.concat(I(e)),n=t.playlists.concat(I(t));return t.timelineStarts=O([e.timelineStarts,t.timelineStarts]),L({oldPlaylists:r,newPlaylists:n,timelineStarts:t.timelineStarts}),t},M=e=>e&&e.uri+"-"+E(e.byterange),_=e=>{let t=e.reduce(function(e,t){return e[t.attributes.baseUrl]||(e[t.attributes.baseUrl]=[]),e[t.attributes.baseUrl].push(t),e},{}),r=[];return Object.values(t).forEach(e=>{let t=f(e.reduce((e,t)=>{let r=t.attributes.id+(t.attributes.lang||"");return e[r]?(t.segments&&(t.segments[0]&&(t.segments[0].discontinuity=!0),e[r].segments.push(...t.segments)),t.attributes.contentProtection&&(e[r].attributes.contentProtection=t.attributes.contentProtection)):(e[r]=t,e[r].attributes.timelineStarts=[]),e[r].attributes.timelineStarts.push({start:t.attributes.periodStart,timeline:t.attributes.periodStart}),e},{}));r=r.concat(t)}),r.map(e=>(e.discontinuityStarts=g(e.segments||[],"discontinuity"),e))},P=(e,t)=>{let r=M(e.sidx),n=r&&t[r]&&t[r].sidx;return n&&S(e,n,e.sidx.resolvedUri),e},B=(e,t={})=>{if(!Object.keys(t).length)return e;for(let r in e)e[r]=P(e[r],t);return e},q=({attributes:e,segments:t,sidx:r,mediaSequence:n,discontinuitySequence:i,discontinuityStarts:a},o)=>{let s={attributes:{NAME:e.id,BANDWIDTH:e.bandwidth,CODECS:e.codecs,"PROGRAM-ID":1},uri:"",endList:"static"===e.type,timeline:e.periodStart,resolvedUri:e.baseUrl||"",targetDuration:e.duration,discontinuitySequence:i,discontinuityStarts:a,timelineStarts:e.timelineStarts,mediaSequence:n,segments:t};return e.contentProtection&&(s.contentProtection=e.contentProtection),e.serviceLocation&&(s.attributes.serviceLocation=e.serviceLocation),r&&(s.sidx=r),o&&(s.attributes.AUDIO="audio",s.attributes.SUBTITLES="subs"),s},k=({attributes:e,segments:t,mediaSequence:r,discontinuityStarts:n,discontinuitySequence:i})=>{void 0===t&&(t=[{uri:e.baseUrl,timeline:e.periodStart,resolvedUri:e.baseUrl||"",duration:e.sourceDuration,number:0}],e.duration=e.sourceDuration);let a={NAME:e.id,BANDWIDTH:e.bandwidth,"PROGRAM-ID":1};e.codecs&&(a.CODECS=e.codecs);let o={attributes:a,uri:"",endList:"static"===e.type,timeline:e.periodStart,resolvedUri:e.baseUrl||"",targetDuration:e.duration,timelineStarts:e.timelineStarts,discontinuityStarts:n,discontinuitySequence:i,mediaSequence:r,segments:t};return e.serviceLocation&&(o.attributes.serviceLocation=e.serviceLocation),o},F=(e,t={},r=!1)=>{let n;let i=e.reduce((e,i)=>{let a=i.attributes.role&&i.attributes.role.value||"",o=i.attributes.lang||"",s=i.attributes.label||"main";if(o&&!i.attributes.label){let e=a?` (${a})`:"";s=`${i.attributes.lang}${e}`}e[s]||(e[s]={language:o,autoselect:!0,default:"main"===a,playlists:[],uri:""});let u=P(q(i,r),t);return e[s].playlists.push(u),void 0===n&&"main"===a&&((n=i).default=!0),e},{});if(!n){let e=Object.keys(i)[0];i[e].default=!0}return i},X=(e,t={})=>e.reduce((e,r)=>{let n=r.attributes.label||r.attributes.lang||"text",i=r.attributes.lang||"und";return e[n]||(e[n]={language:i,default:!1,autoselect:!1,playlists:[],uri:""}),e[n].playlists.push(P(k(r),t)),e},{}),j=e=>e.reduce((e,t)=>(t&&t.forEach(t=>{let{channel:r,language:n}=t;e[n]={autoselect:!1,default:!1,instreamId:r,language:n},t.hasOwnProperty("aspectRatio")&&(e[n].aspectRatio=t.aspectRatio),t.hasOwnProperty("easyReader")&&(e[n].easyReader=t.easyReader),t.hasOwnProperty("3D")&&(e[n]["3D"]=t["3D"])}),e),{}),V=({attributes:e,segments:t,sidx:r,discontinuityStarts:n})=>{let i={attributes:{NAME:e.id,AUDIO:"audio",SUBTITLES:"subs",RESOLUTION:{width:e.width,height:e.height},CODECS:e.codecs,BANDWIDTH:e.bandwidth,"PROGRAM-ID":1},uri:"",endList:"static"===e.type,timeline:e.periodStart,resolvedUri:e.baseUrl||"",targetDuration:e.duration,discontinuityStarts:n,timelineStarts:e.timelineStarts,segments:t};return e.frameRate&&(i.attributes["FRAME-RATE"]=e.frameRate),e.contentProtection&&(i.contentProtection=e.contentProtection),e.serviceLocation&&(i.attributes.serviceLocation=e.serviceLocation),r&&(i.sidx=r),i},H=({attributes:e})=>"video/mp4"===e.mimeType||"video/webm"===e.mimeType||"video"===e.contentType,G=({attributes:e})=>"audio/mp4"===e.mimeType||"audio/webm"===e.mimeType||"audio"===e.contentType,z=({attributes:e})=>"text/vtt"===e.mimeType||"text"===e.contentType,$=(e,t)=>{e.forEach(e=>{e.mediaSequence=0,e.discontinuitySequence=t.findIndex(function({timeline:t}){return t===e.timeline}),e.segments&&e.segments.forEach((e,t)=>{e.number=t})})},W=e=>e?Object.keys(e).reduce((t,r)=>{let n=e[r];return t.concat(n.playlists)},[]):[],Y=({dashPlaylists:e,locations:t,contentSteering:r,sidxMapping:n={},previousManifest:i,eventStream:a})=>{if(!e.length)return{};let{sourceDuration:o,type:s,suggestedPresentationDelay:u,minimumUpdatePeriod:l}=e[0].attributes,c=_(e.filter(H)).map(V),f=_(e.filter(G)),p=_(e.filter(z)),h=e.map(e=>e.attributes.captionServices).filter(Boolean),d={allowCache:!0,discontinuityStarts:[],segments:[],endList:!0,mediaGroups:{AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},uri:"",duration:o,playlists:B(c,n)};l>=0&&(d.minimumUpdatePeriod=1e3*l),t&&(d.locations=t),r&&(d.contentSteering=r),"dynamic"===s&&(d.suggestedPresentationDelay=u),a&&a.length>0&&(d.eventStream=a);let g=0===d.playlists.length,m=f.length?F(f,n,g):null,b=p.length?X(p,n):null,y=c.concat(W(m),W(b)),E=y.map(({timelineStarts:e})=>e);return(d.timelineStarts=O(E),$(y,d.timelineStarts),m&&(d.mediaGroups.AUDIO.audio=m),b&&(d.mediaGroups.SUBTITLES.subs=b),h.length&&(d.mediaGroups["CLOSED-CAPTIONS"].cc=j(h)),i)?U({oldManifest:i,newManifest:d}):d},K=(e,t,r)=>{let{NOW:n,clientOffset:i,availabilityStartTime:a,timescale:o=1,periodStart:s=0,minimumUpdatePeriod:u=0}=e;return Math.ceil((((n+i)/1e3+u-(a+s))*o-t)/r)},J=(e,t)=>{let{type:r,minimumUpdatePeriod:n=0,media:i="",sourceDuration:a,timescale:o=1,startNumber:s=1,periodStart:u}=e,l=[],c=-1;for(let f=0;f<t.length;f++){let p;let h=t[f],d=h.d,g=h.r||0,m=h.t||0;if(c<0&&(c=m),m&&m>c&&(c=m),g<0){let s=f+1;p=s===t.length?"dynamic"===r&&n>0&&i.indexOf("$Number$")>0?K(e,c,d):(a*o-c)/d:(t[s].t-c)/d}else p=g+1;let b=s+l.length+p,y=s+l.length;for(;y<b;)l.push({number:y,duration:d/o,time:c,timeline:u}),c+=d,y++}return l},Z=/\$([A-z]*)(?:(%0)([0-9]+)d)?\$/g,Q=e=>(t,r,n,i)=>{if("$$"===t)return"$";if(void 0===e[r])return t;let a=""+e[r];return"RepresentationID"===r?a:(i=n?parseInt(i,10):1,a.length>=i)?a:`${Array(i-a.length+1).join("0")}${a}`},ee=(e,t)=>e.replace(Z,Q(t)),et=(e,t)=>e.duration||t?e.duration?D(e):J(e,t):[{number:e.startNumber||1,duration:e.sourceDuration,time:0,timeline:e.periodStart}],er=(e,t)=>{let r={RepresentationID:e.id,Bandwidth:e.bandwidth||0},{initialization:i={sourceURL:"",range:""}}=e,a=y({baseUrl:e.baseUrl,source:ee(i.sourceURL,r),range:i.range});return et(e,t).map(t=>{r.Number=t.number,r.Time=t.time;let i=ee(e.media||"",r),o=e.timescale||1,s=e.presentationTimeOffset||0,u=e.periodStart+(t.time-s)/o;return{uri:i,timeline:t.timeline,duration:t.duration,resolvedUri:(0,n.A)(e.baseUrl||"",i),map:a,number:t.number,presentationTime:u}})},en=(e,t)=>{let{baseUrl:r,initialization:n={}}=e,i=y({baseUrl:r,source:n.sourceURL,range:n.range}),a=y({baseUrl:r,source:t.media,range:t.mediaRange});return a.map=i,a},ei=(e,t)=>{let r;let{duration:n,segmentUrls:i=[],periodStart:a}=e;if(!n&&!t||n&&t)throw Error(b.SEGMENT_TIME_UNSPECIFIED);let o=i.map(t=>en(e,t));return n&&(r=D(e)),t&&(r=J(e,t)),r.map((t,r)=>{if(o[r]){let n=o[r],i=e.timescale||1,s=e.presentationTimeOffset||0;return n.timeline=t.timeline,n.duration=t.duration,n.number=t.number,n.presentationTime=a+(t.time-s)/i,n}}).filter(e=>e)},ea=({attributes:e,segmentInfo:t})=>{let r,n;t.template?(n=er,r=c(e,t.template)):t.base?(n=A,r=c(e,t.base)):t.list&&(n=ei,r=c(e,t.list));let i={attributes:e};if(!n)return i;let a=n(r,t.segmentTimeline);if(r.duration){let{duration:e,timescale:t=1}=r;r.duration=e/t}else a.length?r.duration=a.reduce((e,t)=>Math.max(e,Math.ceil(t.duration)),0):r.duration=0;return i.attributes=r,i.segments=a,t.base&&r.indexRange&&(i.sidx=a[0],i.segments=[]),i},eo=e=>e.map(ea),es=(e,t)=>d(e.childNodes).filter(({tagName:e})=>e===t),eu=e=>e.textContent.trim(),el=e=>parseFloat(e.split("/").reduce((e,t)=>e/t)),ec=e=>{let t=/P(?:(\d*)Y)?(?:(\d*)M)?(?:(\d*)D)?(?:T(?:(\d*)H)?(?:(\d*)M)?(?:([\d.]*)S)?)?/.exec(e);if(!t)return 0;let[r,n,i,a,o,s]=t.slice(1);return 31536e3*parseFloat(r||0)+2592e3*parseFloat(n||0)+86400*parseFloat(i||0)+3600*parseFloat(a||0)+60*parseFloat(o||0)+parseFloat(s||0)},ef=e=>(/^\d+-\d+-\d+T\d+:\d+:\d+(\.\d+)?$/.test(e)&&(e+="Z"),Date.parse(e)),ep={mediaPresentationDuration:e=>ec(e),availabilityStartTime:e=>ef(e)/1e3,minimumUpdatePeriod:e=>ec(e),suggestedPresentationDelay:e=>ec(e),type:e=>e,timeShiftBufferDepth:e=>ec(e),start:e=>ec(e),width:e=>parseInt(e,10),height:e=>parseInt(e,10),bandwidth:e=>parseInt(e,10),frameRate:e=>el(e),startNumber:e=>parseInt(e,10),timescale:e=>parseInt(e,10),presentationTimeOffset:e=>parseInt(e,10),duration(e){let t=parseInt(e,10);return isNaN(t)?ec(e):t},d:e=>parseInt(e,10),t:e=>parseInt(e,10),r:e=>parseInt(e,10),presentationTime:e=>parseInt(e,10),DEFAULT:e=>e},eh=e=>e&&e.attributes?d(e.attributes).reduce((e,t)=>{let r=ep[t.name]||ep.DEFAULT;return e[t.name]=r(t.value),e},{}):{},ed={"urn:uuid:1077efec-c0b2-4d02-ace3-3c1e52e2fb4b":"org.w3.clearkey","urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed":"com.widevine.alpha","urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95":"com.microsoft.playready","urn:uuid:f239e769-efa3-4850-9c16-a903c6932efb":"com.adobe.primetime","urn:mpeg:dash:mp4protection:2011":"mp4protection"},eg=(e,t)=>t.length?h(e.map(function(e){return t.map(function(t){let r=eu(t),i=(0,n.A)(e.baseUrl,r),a=c(eh(t),{baseUrl:i});return i!==r&&!a.serviceLocation&&e.serviceLocation&&(a.serviceLocation=e.serviceLocation),a})})):e,em=e=>{let t=es(e,"SegmentTemplate")[0],r=es(e,"SegmentList")[0],n=r&&es(r,"SegmentURL").map(e=>c({tag:"SegmentURL"},eh(e))),i=es(e,"SegmentBase")[0],a=r||t,o=a&&es(a,"SegmentTimeline")[0],s=r||i||t,u=s&&es(s,"Initialization")[0],l=t&&eh(t);l&&u?l.initialization=u&&eh(u):l&&l.initialization&&(l.initialization={sourceURL:l.initialization});let f={template:l,segmentTimeline:o&&es(o,"S").map(e=>eh(e)),list:r&&c(eh(r),{segmentUrls:n,initialization:eh(u)}),base:i&&c(eh(i),{initialization:eh(u)})};return Object.keys(f).forEach(e=>{f[e]||delete f[e]}),f},eb=(e,t,r)=>n=>{let i=eg(t,es(n,"BaseURL")),a=c(e,eh(n)),o=em(n);return i.map(e=>({segmentInfo:c(r,o),attributes:c(a,e)}))},ey=e=>e.reduce((e,t)=>{let r=eh(t);r.schemeIdUri&&(r.schemeIdUri=r.schemeIdUri.toLowerCase());let n=ed[r.schemeIdUri];if(n){e[n]={attributes:r};let i=es(t,"cenc:pssh")[0];if(i){let t=eu(i);e[n].pssh=t&&(0,s.A)(t)}}return e},{}),eE=e=>"urn:scte:dash:cc:cea-608:2015"===e.schemeIdUri?("string"!=typeof e.value?[]:e.value.split(";")).map(e=>{let t,r;return r=e,/^CC\d=/.test(e)?[t,r]=e.split("="):/^CC\d$/.test(e)&&(t=e),{channel:t,language:r}}):"urn:scte:dash:cc:cea-708:2015"===e.schemeIdUri?("string"!=typeof e.value?[]:e.value.split(";")).map(e=>{let t={channel:void 0,language:void 0,aspectRatio:1,easyReader:0,"3D":0};if(/=/.test(e)){let[r,n=""]=e.split("=");t.channel=r,t.language=e,n.split(",").forEach(e=>{let[r,n]=e.split(":");"lang"===r?t.language=n:"er"===r?t.easyReader=Number(n):"war"===r?t.aspectRatio=Number(n):"3D"===r&&(t["3D"]=Number(n))})}else t.language=e;return t.channel&&(t.channel="SERVICE"+t.channel),t}):void 0,ev=e=>h(es(e.node,"EventStream").map(t=>{let r=eh(t),n=r.schemeIdUri;return es(t,"Event").map(t=>{let i=eh(t),a=i.presentationTime||0,o=r.timescale||1,s=i.duration||0,u=a/o+e.attributes.start;return{schemeIdUri:n,value:r.value,id:i.id,start:u,end:u+s/o,messageData:eu(t)||i.messageData,contentEncoding:r.contentEncoding,presentationTimeOffset:r.presentationTimeOffset||0}})})),eT=(e,t,r)=>n=>{let i=eh(n),a=eg(t,es(n,"BaseURL")),o=c(e,i,{role:eh(es(n,"Role")[0])}),s=eE(eh(es(n,"Accessibility")[0]));s&&(o=c(o,{captionServices:s}));let u=es(n,"Label")[0];u&&u.childNodes.length&&(o=c(o,{label:u.childNodes[0].nodeValue.trim()}));let l=ey(es(n,"ContentProtection"));Object.keys(l).length&&(o=c(o,{contentProtection:l}));let f=em(n),p=es(n,"Representation"),d=c(r,f);return h(p.map(eb(o,a,d)))},ew=(e,t)=>(r,n)=>{let i=eg(t,es(r.node,"BaseURL")),a=c(e,{periodStart:r.attributes.start});"number"==typeof r.attributes.duration&&(a.periodDuration=r.attributes.duration);let o=es(r.node,"AdaptationSet"),s=em(r.node);return h(o.map(eT(a,i,s)))},eD=(e,t)=>{if(e.length>1&&t({type:"warn",message:"The MPD manifest should contain no more than one ContentSteering tag"}),!e.length)return null;let r=c({serverURL:eu(e[0])},eh(e[0]));return r.queryBeforeStart="true"===r.queryBeforeStart,r},eA=({attributes:e,priorPeriodAttributes:t,mpdType:r})=>"number"==typeof e.start?e.start:t&&"number"==typeof t.start&&"number"==typeof t.duration?t.start+t.duration:t||"static"!==r?null:0,eS=(e,t={})=>{let{manifestUri:r="",NOW:n=Date.now(),clientOffset:i=0,eventHandler:a=function(){}}=t,o=es(e,"Period");if(!o.length)throw Error(b.INVALID_NUMBER_OF_PERIOD);let s=es(e,"Location"),u=eh(e),l=eg([{baseUrl:r}],es(e,"BaseURL")),c=es(e,"ContentSteering");u.type=u.type||"static",u.sourceDuration=u.mediaPresentationDuration||0,u.NOW=n,u.clientOffset=i,s.length&&(u.locations=s.map(eu));let f=[];return o.forEach((e,t)=>{let r=eh(e),n=f[t-1];r.start=eA({attributes:r,priorPeriodAttributes:n?n.attributes:null,mpdType:u.type}),f.push({node:e,attributes:r})}),{locations:u.locations,contentSteeringInfo:eD(c,a),representationInfo:h(f.map(ew(u,l))),eventStream:h(f.map(ev))}},eN=e=>{let t,r;if(""===e)throw Error(b.DASH_EMPTY_MANIFEST);let n=new u.DOMParser;try{r=(t=n.parseFromString(e,"application/xml"))&&"MPD"===t.documentElement.tagName?t.documentElement:null}catch(e){}if(!r||r&&r.getElementsByTagName("parsererror").length>0)throw Error(b.DASH_INVALID_XML);return r},eR=e=>{let t=es(e,"UTCTiming")[0];if(!t)return null;let r=eh(t);switch(r.schemeIdUri){case"urn:mpeg:dash:utc:http-head:2014":case"urn:mpeg:dash:utc:http-head:2012":r.method="HEAD";break;case"urn:mpeg:dash:utc:http-xsdate:2014":case"urn:mpeg:dash:utc:http-iso:2014":case"urn:mpeg:dash:utc:http-xsdate:2012":case"urn:mpeg:dash:utc:http-iso:2012":r.method="GET";break;case"urn:mpeg:dash:utc:direct:2014":case"urn:mpeg:dash:utc:direct:2012":r.method="DIRECT",r.value=Date.parse(r.value);break;default:throw Error(b.UNSUPPORTED_UTC_TIMING_SCHEME)}return r},eO=(e,t={})=>{let r=eS(eN(e),t);return Y({dashPlaylists:eo(r.representationInfo),locations:r.locations,contentSteering:r.contentSteeringInfo,sidxMapping:t.sidxMapping,previousManifest:t.previousManifest,eventStream:r.eventStream})},ex=e=>eR(eN(e))},770:(e,t,r)=>{var n=r(7925),i=n.find,a=n.NAMESPACE;function o(e){return""!==e}function s(e,t){return e.hasOwnProperty(t)||(e[t]=!0),e}function u(e){return e?Object.keys((e?e.split(/[\t\n\f\r ]+/).filter(o):[]).reduce(s,{})):[]}function l(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}function c(e,t){var r=e.prototype;if(!(r instanceof t)){function n(){}n.prototype=t.prototype,l(r,n=new n),e.prototype=r=n}r.constructor!=e&&("function"!=typeof e&&console.error("unknown Class:"+e),r.constructor=e)}var f={},p=f.ELEMENT_NODE=1,h=f.ATTRIBUTE_NODE=2,d=f.TEXT_NODE=3,g=f.CDATA_SECTION_NODE=4,m=f.ENTITY_REFERENCE_NODE=5,b=f.ENTITY_NODE=6,y=f.PROCESSING_INSTRUCTION_NODE=7,E=f.COMMENT_NODE=8,v=f.DOCUMENT_NODE=9,T=f.DOCUMENT_TYPE_NODE=10,w=f.DOCUMENT_FRAGMENT_NODE=11,D=f.NOTATION_NODE=12,A={},S={};A.INDEX_SIZE_ERR=(S[1]="Index size error",1),A.DOMSTRING_SIZE_ERR=(S[2]="DOMString size error",2);var N=A.HIERARCHY_REQUEST_ERR=(S[3]="Hierarchy request error",3);A.WRONG_DOCUMENT_ERR=(S[4]="Wrong document",4),A.INVALID_CHARACTER_ERR=(S[5]="Invalid character",5),A.NO_DATA_ALLOWED_ERR=(S[6]="No data allowed",6),A.NO_MODIFICATION_ALLOWED_ERR=(S[7]="No modification allowed",7);var R=A.NOT_FOUND_ERR=(S[8]="Not found",8);A.NOT_SUPPORTED_ERR=(S[9]="Not supported",9);var O=A.INUSE_ATTRIBUTE_ERR=(S[10]="Attribute in use",10);function x(e,t){if(t instanceof Error)var r=t;else r=this,Error.call(this,S[e]),this.message=S[e],Error.captureStackTrace&&Error.captureStackTrace(this,x);return r.code=e,t&&(this.message=this.message+": "+t),r}function I(){}function C(e,t){this._node=e,this._refresh=t,L(this)}function L(e){var t=e._node._inc||e._node.ownerDocument._inc;if(e._inc!==t){var r=e._refresh(e._node);if(eg(e,"length",r.length),!e.$$length||r.length<e.$$length)for(var n=r.length;n in e;n++)Object.prototype.hasOwnProperty.call(e,n)&&delete e[n];l(r,e),e._inc=t}}function U(){}function M(e,t){for(var r=e.length;r--;)if(e[r]===t)return r}function _(e,t,r,n){if(n?t[M(t,n)]=r:t[t.length++]=r,e){r.ownerElement=e;var i,o,s,u=e.ownerDocument;u&&(n&&j(u,e,n),i=u,o=e,s=r,i&&i._inc++,s.namespaceURI===a.XMLNS&&(o._nsMap[s.prefix?s.localName:""]=s.value))}}function P(e,t,r){var n=M(t,r);if(n>=0){for(var i=t.length-1;n<i;)t[n]=t[++n];if(t.length=i,e){var a=e.ownerDocument;a&&(j(a,e,r),r.ownerElement=null)}}else throw new x(R,Error(e.tagName+"@"+r))}function B(){}function q(){}function k(e){return"<"==e&&"&lt;"||">"==e&&"&gt;"||"&"==e&&"&amp;"||'"'==e&&"&quot;"||"&#"+e.charCodeAt()+";"}function F(e,t){if(t(e))return!0;if(e=e.firstChild)do if(F(e,t))return!0;while(e=e.nextSibling)}function X(){this.ownerDocument=this}function j(e,t,r,n){e&&e._inc++,r.namespaceURI===a.XMLNS&&delete t._nsMap[r.prefix?r.localName:""]}function V(e,t,r){if(e&&e._inc){e._inc++;var n=t.childNodes;if(r)n[n.length++]=r;else{for(var i=t.firstChild,a=0;i;)n[a++]=i,i=i.nextSibling;n.length=a,delete n[n.length]}}}function H(e,t){var r=t.previousSibling,n=t.nextSibling;return r?r.nextSibling=n:e.firstChild=n,n?n.previousSibling=r:e.lastChild=r,t.parentNode=null,t.previousSibling=null,t.nextSibling=null,V(e.ownerDocument,e),t}function G(e){return e&&e.nodeType===q.DOCUMENT_TYPE_NODE}function z(e){return e&&e.nodeType===q.ELEMENT_NODE}function $(e){return e&&e.nodeType===q.TEXT_NODE}function W(e,t){var r=e.childNodes||[];if(i(r,z)||G(t))return!1;var n=i(r,G);return!(t&&n&&r.indexOf(n)>r.indexOf(t))}function Y(e,t){var r=e.childNodes||[];if(i(r,function(e){return z(e)&&e!==t}))return!1;var n=i(r,G);return!(t&&n&&r.indexOf(n)>r.indexOf(t))}function K(e,t,r){var n=e.childNodes||[],a=t.childNodes||[];if(t.nodeType===q.DOCUMENT_FRAGMENT_NODE){var o=a.filter(z);if(o.length>1||i(a,$))throw new x(N,"More than one element or text in fragment");if(1===o.length&&!Y(e,r))throw new x(N,"Element in fragment can not be inserted before doctype")}if(z(t)&&!Y(e,r))throw new x(N,"Only one element can be added and only after doctype");if(G(t)){if(i(n,function(e){return G(e)&&e!==r}))throw new x(N,"Only one doctype is allowed");var s=i(n,z);if(r&&n.indexOf(s)<n.indexOf(r))throw new x(N,"Doctype can only be inserted before an element")}}function J(e,t,r,n){!function(e,t,r){if(!(e&&(e.nodeType===q.DOCUMENT_NODE||e.nodeType===q.DOCUMENT_FRAGMENT_NODE||e.nodeType===q.ELEMENT_NODE)))throw new x(N,"Unexpected parent node type "+e.nodeType);if(r&&r.parentNode!==e)throw new x(R,"child not in parent");if(!(t&&(z(t)||$(t)||G(t)||t.nodeType===q.DOCUMENT_FRAGMENT_NODE||t.nodeType===q.COMMENT_NODE||t.nodeType===q.PROCESSING_INSTRUCTION_NODE))||G(t)&&e.nodeType!==q.DOCUMENT_NODE)throw new x(N,"Unexpected node type "+t.nodeType+" for parent node type "+e.nodeType)}(e,t,r),e.nodeType===q.DOCUMENT_NODE&&(n||function(e,t,r){var n=e.childNodes||[],a=t.childNodes||[];if(t.nodeType===q.DOCUMENT_FRAGMENT_NODE){var o=a.filter(z);if(o.length>1||i(a,$))throw new x(N,"More than one element or text in fragment");if(1===o.length&&!W(e,r))throw new x(N,"Element in fragment can not be inserted before doctype")}if(z(t)&&!W(e,r))throw new x(N,"Only one element can be added and only after doctype");if(G(t)){if(i(n,G))throw new x(N,"Only one doctype is allowed");var s=i(n,z);if(r&&n.indexOf(s)<n.indexOf(r))throw new x(N,"Doctype can only be inserted before an element");if(!r&&s)throw new x(N,"Doctype can not be appended since element is present")}})(e,t,r);var a=t.parentNode;if(a&&a.removeChild(t),t.nodeType===w){var o=t.firstChild;if(null==o)return t;var s=t.lastChild}else o=s=t;var u=r?r.previousSibling:e.lastChild;o.previousSibling=u,s.nextSibling=r,u?u.nextSibling=o:e.firstChild=o,null==r?e.lastChild=s:r.previousSibling=s;do o.parentNode=e;while(o!==s&&(o=o.nextSibling));return V(e.ownerDocument||e,e),t.nodeType==w&&(t.firstChild=t.lastChild=null),t}function Z(){this._nsMap={}}function Q(){}function ee(){}function et(){}function er(){}function en(){}function ei(){}function ea(){}function eo(){}function es(){}function eu(){}function el(){}function ec(){}function ef(e,t){var r=[],n=9==this.nodeType&&this.documentElement||this,i=n.prefix,a=n.namespaceURI;if(a&&null==i){var i=n.lookupPrefix(a);if(null==i)var o=[{namespace:a,prefix:null}]}return ed(this,r,e,t,o),r.join("")}function ep(e,t,r){var n=e.prefix||"",i=e.namespaceURI;if(!i||"xml"===n&&i===a.XML||i===a.XMLNS)return!1;for(var o=r.length;o--;){var s=r[o];if(s.prefix===n)return s.namespace!==i}return!0}function eh(e,t,r){e.push(" ",t,'="',r.replace(/[<>&"\t\n\r]/g,k),'"')}function ed(e,t,r,n,i){if(i||(i=[]),n){if(!(e=n(e)))return;if("string"==typeof e){t.push(e);return}}switch(e.nodeType){case p:var o,s=e.attributes,u=s.length,l=e.firstChild,c=e.tagName;r=a.isHTML(e.namespaceURI)||r;var f=c;if(!r&&!e.prefix&&e.namespaceURI){for(var b=0;b<s.length;b++)if("xmlns"===s.item(b).name){o=s.item(b).value;break}if(!o)for(var D=i.length-1;D>=0;D--){var A=i[D];if(""===A.prefix&&A.namespace===e.namespaceURI){o=A.namespace;break}}if(o!==e.namespaceURI)for(var D=i.length-1;D>=0;D--){var A=i[D];if(A.namespace===e.namespaceURI){A.prefix&&(f=A.prefix+":"+c);break}}}t.push("<",f);for(var S=0;S<u;S++){var N=s.item(S);"xmlns"==N.prefix?i.push({prefix:N.localName,namespace:N.value}):"xmlns"==N.nodeName&&i.push({prefix:"",namespace:N.value})}for(var S=0;S<u;S++){var N=s.item(S);if(ep(N,r,i)){var R=N.prefix||"",O=N.namespaceURI;eh(t,R?"xmlns:"+R:"xmlns",O),i.push({prefix:R,namespace:O})}ed(N,t,r,n,i)}if(c===f&&ep(e,r,i)){var R=e.prefix||"",O=e.namespaceURI;eh(t,R?"xmlns:"+R:"xmlns",O),i.push({prefix:R,namespace:O})}if(l||r&&!/^(?:meta|link|img|br|hr|input)$/i.test(c)){if(t.push(">"),r&&/^script$/i.test(c))for(;l;)l.data?t.push(l.data):ed(l,t,r,n,i.slice()),l=l.nextSibling;else for(;l;)ed(l,t,r,n,i.slice()),l=l.nextSibling;t.push("</",f,">")}else t.push("/>");return;case v:case w:for(var l=e.firstChild;l;)ed(l,t,r,n,i.slice()),l=l.nextSibling;return;case h:return eh(t,e.name,e.value);case d:return t.push(e.data.replace(/[<&>]/g,k));case g:return t.push("<![CDATA[",e.data,"]]>");case E:return t.push("\x3c!--",e.data,"--\x3e");case T:var x=e.publicId,I=e.systemId;if(t.push("<!DOCTYPE ",e.name),x)t.push(" PUBLIC ",x),I&&"."!=I&&t.push(" ",I),t.push(">");else if(I&&"."!=I)t.push(" SYSTEM ",I,">");else{var C=e.internalSubset;C&&t.push(" [",C,"]"),t.push(">")}return;case y:return t.push("<?",e.target," ",e.data,"?>");case m:return t.push("&",e.nodeName,";");default:t.push("??",e.nodeName)}}function eg(e,t,r){e[t]=r}A.INVALID_STATE_ERR=(S[11]="Invalid state",11),A.SYNTAX_ERR=(S[12]="Syntax error",12),A.INVALID_MODIFICATION_ERR=(S[13]="Invalid modification",13),A.NAMESPACE_ERR=(S[14]="Invalid namespace",14),A.INVALID_ACCESS_ERR=(S[15]="Invalid access",15),x.prototype=Error.prototype,l(A,x),I.prototype={length:0,item:function(e){return e>=0&&e<this.length?this[e]:null},toString:function(e,t){for(var r=[],n=0;n<this.length;n++)ed(this[n],r,e,t);return r.join("")},filter:function(e){return Array.prototype.filter.call(this,e)},indexOf:function(e){return Array.prototype.indexOf.call(this,e)}},C.prototype.item=function(e){return L(this),this[e]||null},c(C,I),U.prototype={length:0,item:I.prototype.item,getNamedItem:function(e){for(var t=this.length;t--;){var r=this[t];if(r.nodeName==e)return r}},setNamedItem:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new x(O);var r=this.getNamedItem(e.nodeName);return _(this._ownerElement,this,e,r),r},setNamedItemNS:function(e){var t,r=e.ownerElement;if(r&&r!=this._ownerElement)throw new x(O);return t=this.getNamedItemNS(e.namespaceURI,e.localName),_(this._ownerElement,this,e,t),t},removeNamedItem:function(e){var t=this.getNamedItem(e);return P(this._ownerElement,this,t),t},removeNamedItemNS:function(e,t){var r=this.getNamedItemNS(e,t);return P(this._ownerElement,this,r),r},getNamedItemNS:function(e,t){for(var r=this.length;r--;){var n=this[r];if(n.localName==t&&n.namespaceURI==e)return n}return null}},B.prototype={hasFeature:function(e,t){return!0},createDocument:function(e,t,r){var n=new X;if(n.implementation=this,n.childNodes=new I,n.doctype=r||null,r&&n.appendChild(r),t){var i=n.createElementNS(e,t);n.appendChild(i)}return n},createDocumentType:function(e,t,r){var n=new ei;return n.name=e,n.nodeName=e,n.publicId=t||"",n.systemId=r||"",n}},q.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,t){return J(this,e,t)},replaceChild:function(e,t){J(this,e,t,K),t&&this.removeChild(t)},removeChild:function(e){return H(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return function e(t,r,n){var i=new r.constructor;for(var a in r)if(Object.prototype.hasOwnProperty.call(r,a)){var o=r[a];"object"!=typeof o&&o!=i[a]&&(i[a]=o)}switch(r.childNodes&&(i.childNodes=new I),i.ownerDocument=t,i.nodeType){case p:var s=r.attributes,u=i.attributes=new U,l=s.length;u._ownerElement=i;for(var c=0;c<l;c++)i.setAttributeNode(e(t,s.item(c),!0));break;case h:n=!0}if(n)for(var f=r.firstChild;f;)i.appendChild(e(t,f,n)),f=f.nextSibling;return i}(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var t=e.nextSibling;t&&t.nodeType==d&&e.nodeType==d?(this.removeChild(t),e.appendData(t.data)):(e.normalize(),e=t)}},isSupported:function(e,t){return this.ownerDocument.implementation.hasFeature(e,t)},hasAttributes:function(){return this.attributes.length>0},lookupPrefix:function(e){for(var t=this;t;){var r=t._nsMap;if(r){for(var n in r)if(Object.prototype.hasOwnProperty.call(r,n)&&r[n]===e)return n}t=t.nodeType==h?t.ownerDocument:t.parentNode}return null},lookupNamespaceURI:function(e){for(var t=this;t;){var r=t._nsMap;if(r&&Object.prototype.hasOwnProperty.call(r,e))return r[e];t=t.nodeType==h?t.ownerDocument:t.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)}},l(f,q),l(f,q.prototype),X.prototype={nodeName:"#document",nodeType:v,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,t){if(e.nodeType==w){for(var r=e.firstChild;r;){var n=r.nextSibling;this.insertBefore(r,t),r=n}return e}return J(this,e,t),e.ownerDocument=this,null===this.documentElement&&e.nodeType===p&&(this.documentElement=e),e},removeChild:function(e){return this.documentElement==e&&(this.documentElement=null),H(this,e)},replaceChild:function(e,t){J(this,e,t,K),e.ownerDocument=this,t&&this.removeChild(t),z(e)&&(this.documentElement=e)},importNode:function(e,t){return function e(t,r,n){var i;switch(r.nodeType){case p:(i=r.cloneNode(!1)).ownerDocument=t;case w:break;case h:n=!0}if(i||(i=r.cloneNode(!1)),i.ownerDocument=t,i.parentNode=null,n)for(var a=r.firstChild;a;)i.appendChild(e(t,a,n)),a=a.nextSibling;return i}(this,e,t)},getElementById:function(e){var t=null;return F(this.documentElement,function(r){if(r.nodeType==p&&r.getAttribute("id")==e)return t=r,!0}),t},getElementsByClassName:function(e){var t=u(e);return new C(this,function(r){var n=[];return t.length>0&&F(r.documentElement,function(i){if(i!==r&&i.nodeType===p){var a=i.getAttribute("class");if(a){var o=e===a;if(!o){var s=u(a);o=t.every(function(e){return s&&-1!==s.indexOf(e)})}o&&n.push(i)}}}),n})},createElement:function(e){var t=new Z;return t.ownerDocument=this,t.nodeName=e,t.tagName=e,t.localName=e,t.childNodes=new I,(t.attributes=new U)._ownerElement=t,t},createDocumentFragment:function(){var e=new eu;return e.ownerDocument=this,e.childNodes=new I,e},createTextNode:function(e){var t=new et;return t.ownerDocument=this,t.appendData(e),t},createComment:function(e){var t=new er;return t.ownerDocument=this,t.appendData(e),t},createCDATASection:function(e){var t=new en;return t.ownerDocument=this,t.appendData(e),t},createProcessingInstruction:function(e,t){var r=new el;return r.ownerDocument=this,r.tagName=r.nodeName=r.target=e,r.nodeValue=r.data=t,r},createAttribute:function(e){var t=new Q;return t.ownerDocument=this,t.name=e,t.nodeName=e,t.localName=e,t.specified=!0,t},createEntityReference:function(e){var t=new es;return t.ownerDocument=this,t.nodeName=e,t},createElementNS:function(e,t){var r=new Z,n=t.split(":"),i=r.attributes=new U;return r.childNodes=new I,r.ownerDocument=this,r.nodeName=t,r.tagName=t,r.namespaceURI=e,2==n.length?(r.prefix=n[0],r.localName=n[1]):r.localName=t,i._ownerElement=r,r},createAttributeNS:function(e,t){var r=new Q,n=t.split(":");return r.ownerDocument=this,r.nodeName=t,r.name=t,r.namespaceURI=e,r.specified=!0,2==n.length?(r.prefix=n[0],r.localName=n[1]):r.localName=t,r}},c(X,q),Z.prototype={nodeType:p,hasAttribute:function(e){return null!=this.getAttributeNode(e)},getAttribute:function(e){var t=this.getAttributeNode(e);return t&&t.value||""},getAttributeNode:function(e){return this.attributes.getNamedItem(e)},setAttribute:function(e,t){var r=this.ownerDocument.createAttribute(e);r.value=r.nodeValue=""+t,this.setAttributeNode(r)},removeAttribute:function(e){var t=this.getAttributeNode(e);t&&this.removeAttributeNode(t)},appendChild:function(e){return e.nodeType===w?this.insertBefore(e,null):(e.parentNode&&e.parentNode.removeChild(e),e.parentNode=this,e.previousSibling=this.lastChild,e.nextSibling=null,e.previousSibling?e.previousSibling.nextSibling=e:this.firstChild=e,this.lastChild=e,V(this.ownerDocument,this,e),e)},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,t){var r=this.getAttributeNodeNS(e,t);r&&this.removeAttributeNode(r)},hasAttributeNS:function(e,t){return null!=this.getAttributeNodeNS(e,t)},getAttributeNS:function(e,t){var r=this.getAttributeNodeNS(e,t);return r&&r.value||""},setAttributeNS:function(e,t,r){var n=this.ownerDocument.createAttributeNS(e,t);n.value=n.nodeValue=""+r,this.setAttributeNode(n)},getAttributeNodeNS:function(e,t){return this.attributes.getNamedItemNS(e,t)},getElementsByTagName:function(e){return new C(this,function(t){var r=[];return F(t,function(n){n!==t&&n.nodeType==p&&("*"===e||n.tagName==e)&&r.push(n)}),r})},getElementsByTagNameNS:function(e,t){return new C(this,function(r){var n=[];return F(r,function(i){i!==r&&i.nodeType===p&&("*"===e||i.namespaceURI===e)&&("*"===t||i.localName==t)&&n.push(i)}),n})}},X.prototype.getElementsByTagName=Z.prototype.getElementsByTagName,X.prototype.getElementsByTagNameNS=Z.prototype.getElementsByTagNameNS,c(Z,q),Q.prototype.nodeType=h,c(Q,q),ee.prototype={data:"",substringData:function(e,t){return this.data.substring(e,e+t)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,t){this.replaceData(e,0,t)},appendChild:function(e){throw Error(S[N])},deleteData:function(e,t){this.replaceData(e,t,"")},replaceData:function(e,t,r){r=this.data.substring(0,e)+r+this.data.substring(e+t),this.nodeValue=this.data=r,this.length=r.length}},c(ee,q),et.prototype={nodeName:"#text",nodeType:d,splitText:function(e){var t=this.data,r=t.substring(e);t=t.substring(0,e),this.data=this.nodeValue=t,this.length=t.length;var n=this.ownerDocument.createTextNode(r);return this.parentNode&&this.parentNode.insertBefore(n,this.nextSibling),n}},c(et,ee),er.prototype={nodeName:"#comment",nodeType:E},c(er,ee),en.prototype={nodeName:"#cdata-section",nodeType:g},c(en,ee),ei.prototype.nodeType=T,c(ei,q),ea.prototype.nodeType=D,c(ea,q),eo.prototype.nodeType=b,c(eo,q),es.prototype.nodeType=m,c(es,q),eu.prototype.nodeName="#document-fragment",eu.prototype.nodeType=w,c(eu,q),el.prototype.nodeType=y,c(el,q),ec.prototype.serializeToString=function(e,t,r){return ef.call(e,t,r)},q.prototype.toString=ef;try{Object.defineProperty&&(Object.defineProperty(C.prototype,"length",{get:function(){return L(this),this.$$length}}),Object.defineProperty(q.prototype,"textContent",{get:function(){return function e(t){switch(t.nodeType){case p:case w:var r=[];for(t=t.firstChild;t;)7!==t.nodeType&&8!==t.nodeType&&r.push(e(t)),t=t.nextSibling;return r.join("");default:return t.nodeValue}}(this)},set:function(e){switch(this.nodeType){case p:case w:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),eg=function(e,t,r){e["$$"+t]=r})}catch(e){}t.DocumentType=ei,t.DOMException=x,t.DOMImplementation=B,t.Element=Z,t.Node=q,t.NodeList=I,t.XMLSerializer=ec},1082:e=>{var t,r,n,i,a,o,s;t=function(e){return 9e4*e},r=function(e,t){return e*t},n=function(e){return e/9e4},i=function(e,t){return e/t},a=function(e,r){return t(i(e,r))},o=function(e,t){return r(n(e),t)},s=function(e,t,r){return n(r?e:e-t)},e.exports={ONE_SECOND_IN_TS:9e4,secondsToVideoTs:t,secondsToAudioTs:r,videoTsToSeconds:n,audioTsToSeconds:i,audioTsToVideoTs:a,videoTsToAudioTs:o,metadataTsToSeconds:s}},2037:(e,t,r)=>{"use strict";var n=r(8202);e.exports=function(e,t){return void 0===t&&(t=!1),function(r,i,a){if(r){e(r);return}if(i.statusCode>=400&&i.statusCode<=599){var o=a;if(t){if(n.TextDecoder){var s,u=(void 0===(s=i.headers&&i.headers["content-type"])&&(s=""),s.toLowerCase().split(";").reduce(function(e,t){var r=t.split("="),n=r[0],i=r[1];return"charset"===n.trim()?i.trim():e},"utf-8"));try{o=new TextDecoder(u).decode(a)}catch(e){}}else o=String.fromCharCode.apply(null,new Uint8Array(a))}e({cause:o});return}e(null,a)}}},2638:(e,t,r)=>{"use strict";r.d(t,{Af:()=>h,Ar:()=>p,Bu:()=>d,L7:()=>g,Sk:()=>c,WG:()=>f,W_:()=>s,hc:()=>o});var n=r(8202),i=r.n(n),a=function(e,t){for(var r="";t--;)r+=e;return r},o=function(e){return"function"===ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer},s=function(e){return e instanceof Uint8Array?e:(!Array.isArray(e)&&!o(e)&&!(e instanceof ArrayBuffer)&&(e="number"!=typeof e||"number"==typeof e&&e!=e?0:[e]),new Uint8Array(e&&e.buffer||e,e&&e.byteOffset||0,e&&e.byteLength||0))},u=i().BigInt||Number,l=[u("0x1"),u("0x100"),u("0x10000"),u("0x1000000"),u("0x100000000"),u("0x10000000000"),u("0x1000000000000"),u("0x100000000000000"),u("0x10000000000000000")],c=(function(){var e=new Uint16Array([65484]),t=new Uint8Array(e.buffer,e.byteOffset,e.byteLength);255===t[0]?"big":204===t[0]?"little":"unknown"}(),function(e,t){var r=void 0===t?{}:t,n=r.signed,i=r.le,a=void 0!==i&&i;e=s(e);var o=a?"reduce":"reduceRight",c=(e[o]?e[o]:Array.prototype[o]).call(e,function(t,r,n){var i=a?n:Math.abs(n+1-e.length);return t+u(r)*l[i]},u(0));if(void 0!==n&&n){var f=l[e.length]/u(2)-u(1);(c=u(c))>f&&(c-=f,c-=f,c-=u(2))}return Number(c)}),f=function(e,t){var r=(void 0===t?{}:t).le,n=void 0!==r&&r;("bigint"!=typeof e&&"number"!=typeof e||"number"==typeof e&&e!=e)&&(e=0);for(var i=Math.ceil((e=u(e)).toString(2).length/8),a=new Uint8Array(new ArrayBuffer(i)),o=0;o<i;o++){var s=n?o:Math.abs(o+1-a.length);a[s]=Number(e/l[o]&u(255)),e<0&&(a[s]=Math.abs(~a[s]),a[s]-=0===o?1:2)}return a},p=function(e){if(!e)return"";e=Array.prototype.slice.call(e);var t=String.fromCharCode.apply(null,s(e));try{return decodeURIComponent(escape(t))}catch(e){}return t},h=function(e,t){if("string"!=typeof e&&e&&"function"==typeof e.toString&&(e=e.toString()),"string"!=typeof e)return new Uint8Array;t||(e=unescape(encodeURIComponent(e)));for(var r=new Uint8Array(e.length),n=0;n<e.length;n++)r[n]=e.charCodeAt(n);return r},d=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if((t=t.filter(function(e){return e&&(e.byteLength||e.length)&&"string"!=typeof e})).length<=1)return s(t[0]);var n=new Uint8Array(t.reduce(function(e,t,r){return e+(t.byteLength||t.length)},0)),i=0;return t.forEach(function(e){e=s(e),n.set(e,i),i+=e.byteLength}),n},g=function(e,t,r){var n=void 0===r?{}:r,i=n.offset,a=void 0===i?0:i,o=n.mask,u=void 0===o?[]:o;e=s(e);var l=(t=s(t)).every?t.every:Array.prototype.every;return t.length&&e.length-a>=t.length&&l.call(t,function(t,r){return t===(u[r]?u[r]&e[a+r]:e[a+r])})}},2651:(e,t,r)=>{"use strict";let n;r.d(t,{A:()=>ts});var i,a,o,s={};function u(e,t){return function(){return e.apply(t,arguments)}}r.r(s),r.d(s,{hasBrowserEnv:()=>ec,hasStandardBrowserEnv:()=>ep,hasStandardBrowserWebWorkerEnv:()=>eh,navigator:()=>ef,origin:()=>ed});var l=r(7358);let{toString:c}=Object.prototype,{getPrototypeOf:f}=Object,p=(e=>t=>{let r=c.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),h=e=>(e=e.toLowerCase(),t=>p(t)===e),d=e=>t=>typeof t===e,{isArray:g}=Array,m=d("undefined"),b=h("ArrayBuffer"),y=d("string"),E=d("function"),v=d("number"),T=e=>null!==e&&"object"==typeof e,w=e=>{if("object"!==p(e))return!1;let t=f(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},D=h("Date"),A=h("File"),S=h("Blob"),N=h("FileList"),R=h("URLSearchParams"),[O,x,I,C]=["ReadableStream","Request","Response","Headers"].map(h);function L(e,t,{allOwnKeys:r=!1}={}){let n,i;if(null!=e){if("object"!=typeof e&&(e=[e]),g(e))for(n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else{let i;let a=r?Object.getOwnPropertyNames(e):Object.keys(e),o=a.length;for(n=0;n<o;n++)i=a[n],t.call(null,e[i],i,e)}}}function U(e,t){let r;t=t.toLowerCase();let n=Object.keys(e),i=n.length;for(;i-- >0;)if(t===(r=n[i]).toLowerCase())return r;return null}let M="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,_=e=>!m(e)&&e!==M,P=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&f(Uint8Array)),B=h("HTMLFormElement"),q=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),k=h("RegExp"),F=(e,t)=>{let r=Object.getOwnPropertyDescriptors(e),n={};L(r,(r,i)=>{let a;!1!==(a=t(r,i,e))&&(n[i]=a||r)}),Object.defineProperties(e,n)},X=h("AsyncFunction"),j=(i="function"==typeof setImmediate,a=E(M.postMessage),i?setImmediate:a?((e,t)=>(M.addEventListener("message",({source:r,data:n})=>{r===M&&n===e&&t.length&&t.shift()()},!1),r=>{t.push(r),M.postMessage(e,"*")}))(`axios@${Math.random()}`,[]):e=>setTimeout(e)),V="undefined"!=typeof queueMicrotask?queueMicrotask.bind(M):void 0!==l&&l.nextTick||j,H={isArray:g,isArrayBuffer:b,isBuffer:function(e){return null!==e&&!m(e)&&null!==e.constructor&&!m(e.constructor)&&E(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||E(e.append)&&("formdata"===(t=p(e))||"object"===t&&E(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&b(e.buffer)},isString:y,isNumber:v,isBoolean:e=>!0===e||!1===e,isObject:T,isPlainObject:w,isReadableStream:O,isRequest:x,isResponse:I,isHeaders:C,isUndefined:m,isDate:D,isFile:A,isBlob:S,isRegExp:k,isFunction:E,isStream:e=>T(e)&&E(e.pipe),isURLSearchParams:R,isTypedArray:P,isFileList:N,forEach:L,merge:function e(){let{caseless:t}=_(this)&&this||{},r={},n=(n,i)=>{let a=t&&U(r,i)||i;w(r[a])&&w(n)?r[a]=e(r[a],n):w(n)?r[a]=e({},n):g(n)?r[a]=n.slice():r[a]=n};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&L(arguments[e],n);return r},extend:(e,t,r,{allOwnKeys:n}={})=>(L(t,(t,n)=>{r&&E(t)?e[n]=u(t,r):e[n]=t},{allOwnKeys:n}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},toFlatObject:(e,t,r,n)=>{let i,a,o;let s={};if(t=t||{},null==e)return t;do{for(a=(i=Object.getOwnPropertyNames(e)).length;a-- >0;)o=i[a],(!n||n(o,e,t))&&!s[o]&&(t[o]=e[o],s[o]=!0);e=!1!==r&&f(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},kindOf:p,kindOfTest:h,endsWith:(e,t,r)=>{e=String(e),(void 0===r||r>e.length)&&(r=e.length),r-=t.length;let n=e.indexOf(t,r);return -1!==n&&n===r},toArray:e=>{if(!e)return null;if(g(e))return e;let t=e.length;if(!v(t))return null;let r=Array(t);for(;t-- >0;)r[t]=e[t];return r},forEachEntry:(e,t)=>{let r;let n=(e&&e[Symbol.iterator]).call(e);for(;(r=n.next())&&!r.done;){let n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let r;let n=[];for(;null!==(r=e.exec(t));)n.push(r);return n},isHTMLForm:B,hasOwnProperty:q,hasOwnProp:q,reduceDescriptors:F,freezeMethods:e=>{F(e,(t,r)=>{if(E(e)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;if(E(e[r])){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},toObjectSet:(e,t)=>{let r={};return(e=>{e.forEach(e=>{r[e]=!0})})(g(e)?e:String(e).split(t)),r},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,r){return t.toUpperCase()+r}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e*=1)?e:t,findKey:U,global:M,isContextDefined:_,isSpecCompliantForm:function(e){return!!(e&&E(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{let t=Array(10),r=(e,n)=>{if(T(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[n]=e;let i=g(e)?[]:{};return L(e,(e,t)=>{let a=r(e,n+1);m(a)||(i[t]=a)}),t[n]=void 0,i}}return e};return r(e,0)},isAsyncFn:X,isThenable:e=>e&&(T(e)||E(e))&&E(e.then)&&E(e.catch),setImmediate:j,asap:V};function G(e,t,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}H.inherits(G,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:H.toJSONObject(this.config),code:this.code,status:this.status}}});let z=G.prototype,$={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{$[e]={value:e}}),Object.defineProperties(G,$),Object.defineProperty(z,"isAxiosError",{value:!0}),G.from=(e,t,r,n,i,a)=>{let o=Object.create(z);return H.toFlatObject(e,o,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),G.call(o,e.message,t,r,n,i),o.cause=e,o.name=e.name,a&&Object.assign(o,a),o};var W=r(5714).Buffer;function Y(e){return H.isPlainObject(e)||H.isArray(e)}function K(e){return H.endsWith(e,"[]")?e.slice(0,-2):e}function J(e,t,r){return e?e.concat(t).map(function(e,t){return e=K(e),!r&&t?"["+e+"]":e}).join(r?".":""):t}let Z=H.toFlatObject(H,{},null,function(e){return/^is[A-Z]/.test(e)}),Q=function(e,t,r){if(!H.isObject(e))throw TypeError("target must be an object");t=t||new FormData;let n=(r=H.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!H.isUndefined(t[e])})).metaTokens,i=r.visitor||l,a=r.dots,o=r.indexes,s=(r.Blob||"undefined"!=typeof Blob&&Blob)&&H.isSpecCompliantForm(t);if(!H.isFunction(i))throw TypeError("visitor must be a function");function u(e){if(null===e)return"";if(H.isDate(e))return e.toISOString();if(!s&&H.isBlob(e))throw new G("Blob is not supported. Use a Buffer instead.");return H.isArrayBuffer(e)||H.isTypedArray(e)?s&&"function"==typeof Blob?new Blob([e]):W.from(e):e}function l(e,r,i){let s=e;if(e&&!i&&"object"==typeof e){if(H.endsWith(r,"{}"))r=n?r:r.slice(0,-2),e=JSON.stringify(e);else{var l;if(H.isArray(e)&&(l=e,H.isArray(l)&&!l.some(Y))||(H.isFileList(e)||H.endsWith(r,"[]"))&&(s=H.toArray(e)))return r=K(r),s.forEach(function(e,n){H.isUndefined(e)||null===e||t.append(!0===o?J([r],n,a):null===o?r:r+"[]",u(e))}),!1}}return!!Y(e)||(t.append(J(i,r,a),u(e)),!1)}let c=[],f=Object.assign(Z,{defaultVisitor:l,convertValue:u,isVisitable:Y});if(!H.isObject(e))throw TypeError("data must be an object");return!function e(r,n){if(!H.isUndefined(r)){if(-1!==c.indexOf(r))throw Error("Circular reference detected in "+n.join("."));c.push(r),H.forEach(r,function(r,a){!0===(!(H.isUndefined(r)||null===r)&&i.call(t,r,H.isString(a)?a.trim():a,n,f))&&e(r,n?n.concat(a):[a])}),c.pop()}}(e),t};function ee(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function et(e,t){this._pairs=[],e&&Q(e,this,t)}let er=et.prototype;function en(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ei(e,t,r){let n;if(!t)return e;let i=r&&r.encode||en;H.isFunction(r)&&(r={serialize:r});let a=r&&r.serialize;if(n=a?a(t,r):H.isURLSearchParams(t)?t.toString():new et(t,r).toString(i)){let t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}er.append=function(e,t){this._pairs.push([e,t])},er.toString=function(e){let t=e?function(t){return e.call(this,t,ee)}:ee;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class ea{constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){H.forEach(this.handlers,function(t){null!==t&&e(t)})}}let eo={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},es="undefined"!=typeof URLSearchParams?URLSearchParams:et,eu="undefined"!=typeof FormData?FormData:null,el="undefined"!=typeof Blob?Blob:null,ec="undefined"!=typeof window&&"undefined"!=typeof document,ef="object"==typeof navigator&&navigator||void 0,ep=ec&&(!ef||0>["ReactNative","NativeScript","NS"].indexOf(ef.product)),eh="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,ed=ec&&window.location.href||"http://localhost",eg={...s,isBrowser:!0,classes:{URLSearchParams:es,FormData:eu,Blob:el},protocols:["http","https","file","blob","url","data"]},em=function(e){if(H.isFormData(e)&&H.isFunction(e.entries)){let t={};return H.forEachEntry(e,(e,r)=>{!function e(t,r,n,i){let a=t[i++];if("__proto__"===a)return!0;let o=Number.isFinite(+a),s=i>=t.length;return(a=!a&&H.isArray(n)?n.length:a,s)?H.hasOwnProp(n,a)?n[a]=[n[a],r]:n[a]=r:(n[a]&&H.isObject(n[a])||(n[a]=[]),e(t,r,n[a],i)&&H.isArray(n[a])&&(n[a]=function(e){let t,r;let n={},i=Object.keys(e),a=i.length;for(t=0;t<a;t++)n[r=i[t]]=e[r];return n}(n[a]))),!o}(H.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),r,t,0)}),t}return null},eb={transitional:eo,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){let r;let n=t.getContentType()||"",i=n.indexOf("application/json")>-1,a=H.isObject(e);if(a&&H.isHTMLForm(e)&&(e=new FormData(e)),H.isFormData(e))return i?JSON.stringify(em(e)):e;if(H.isArrayBuffer(e)||H.isBuffer(e)||H.isStream(e)||H.isFile(e)||H.isBlob(e)||H.isReadableStream(e))return e;if(H.isArrayBufferView(e))return e.buffer;if(H.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1){var o,s;return(o=e,s=this.formSerializer,Q(o,new eg.classes.URLSearchParams,Object.assign({visitor:function(e,t,r,n){return eg.isNode&&H.isBuffer(e)?(this.append(t,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},s))).toString()}if((r=H.isFileList(e))||n.indexOf("multipart/form-data")>-1){let t=this.env&&this.env.FormData;return Q(r?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||i?(t.setContentType("application/json",!1),function(e,t,r){if(H.isString(e))try{return(0,JSON.parse)(e),H.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let t=this.transitional||eb.transitional,r=t&&t.forcedJSONParsing,n="json"===this.responseType;if(H.isResponse(e)||H.isReadableStream(e))return e;if(e&&H.isString(e)&&(r&&!this.responseType||n)){let r=t&&t.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!r&&n){if("SyntaxError"===e.name)throw G.from(e,G.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:eg.classes.FormData,Blob:eg.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};H.forEach(["delete","get","head","post","put","patch"],e=>{eb.headers[e]={}});let ey=H.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),eE=e=>{let t,r,n;let i={};return e&&e.split("\n").forEach(function(e){n=e.indexOf(":"),t=e.substring(0,n).trim().toLowerCase(),r=e.substring(n+1).trim(),t&&(!i[t]||!ey[t])&&("set-cookie"===t?i[t]?i[t].push(r):i[t]=[r]:i[t]=i[t]?i[t]+", "+r:r)}),i},ev=Symbol("internals");function eT(e){return e&&String(e).trim().toLowerCase()}function ew(e){return!1===e||null==e?e:H.isArray(e)?e.map(ew):String(e)}let eD=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eA(e,t,r,n,i){if(H.isFunction(n))return n.call(this,t,r);if(i&&(t=r),H.isString(t)){if(H.isString(n))return -1!==t.indexOf(n);if(H.isRegExp(n))return n.test(t)}}class eS{constructor(e){e&&this.set(e)}set(e,t,r){let n=this;function i(e,t,r){let i=eT(t);if(!i)throw Error("header name must be a non-empty string");let a=H.findKey(n,i);a&&void 0!==n[a]&&!0!==r&&(void 0!==r||!1===n[a])||(n[a||t]=ew(e))}let a=(e,t)=>H.forEach(e,(e,r)=>i(e,r,t));if(H.isPlainObject(e)||e instanceof this.constructor)a(e,t);else if(H.isString(e)&&(e=e.trim())&&!eD(e))a(eE(e),t);else if(H.isHeaders(e))for(let[t,n]of e.entries())i(n,t,r);else null!=e&&i(t,e,r);return this}get(e,t){if(e=eT(e)){let r=H.findKey(this,e);if(r){let e=this[r];if(!t)return e;if(!0===t)return function(e){let t;let r=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;t=n.exec(e);)r[t[1]]=t[2];return r}(e);if(H.isFunction(t))return t.call(this,e,r);if(H.isRegExp(t))return t.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=eT(e)){let r=H.findKey(this,e);return!!(r&&void 0!==this[r]&&(!t||eA(this,this[r],r,t)))}return!1}delete(e,t){let r=this,n=!1;function i(e){if(e=eT(e)){let i=H.findKey(r,e);i&&(!t||eA(r,r[i],i,t))&&(delete r[i],n=!0)}}return H.isArray(e)?e.forEach(i):i(e),n}clear(e){let t=Object.keys(this),r=t.length,n=!1;for(;r--;){let i=t[r];(!e||eA(this,this[i],i,e,!0))&&(delete this[i],n=!0)}return n}normalize(e){let t=this,r={};return H.forEach(this,(n,i)=>{let a=H.findKey(r,i);if(a){t[a]=ew(n),delete t[i];return}let o=e?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,r)=>t.toUpperCase()+r):String(i).trim();o!==i&&delete t[i],t[o]=ew(n),r[o]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return H.forEach(this,(r,n)=>{null!=r&&!1!==r&&(t[n]=e&&H.isArray(r)?r.join(", "):r)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let r=new this(e);return t.forEach(e=>r.set(e)),r}static accessor(e){let t=(this[ev]=this[ev]={accessors:{}}).accessors,r=this.prototype;function n(e){let n=eT(e);t[n]||(!function(e,t){let r=H.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(e,r,i){return this[n].call(this,t,e,r,i)},configurable:!0})})}(r,e),t[n]=!0)}return H.isArray(e)?e.forEach(n):n(e),this}}function eN(e,t){let r=this||eb,n=t||r,i=eS.from(n.headers),a=n.data;return H.forEach(e,function(e){a=e.call(r,a,i.normalize(),t?t.status:void 0)}),i.normalize(),a}function eR(e){return!!(e&&e.__CANCEL__)}function eO(e,t,r){G.call(this,null==e?"canceled":e,G.ERR_CANCELED,t,r),this.name="CanceledError"}function ex(e,t,r){let n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new G("Request failed with status code "+r.status,[G.ERR_BAD_REQUEST,G.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}eS.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),H.reduceDescriptors(eS.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[r]=e}}}),H.freezeMethods(eS),H.inherits(eO,G,{__CANCEL__:!0});let eI=function(e,t){let r;let n=Array(e=e||10),i=Array(e),a=0,o=0;return t=void 0!==t?t:1e3,function(s){let u=Date.now(),l=i[o];r||(r=u),n[a]=s,i[a]=u;let c=o,f=0;for(;c!==a;)f+=n[c++],c%=e;if((a=(a+1)%e)===o&&(o=(o+1)%e),u-r<t)return;let p=l&&u-l;return p?Math.round(1e3*f/p):void 0}},eC=function(e,t){let r,n,i=0,a=1e3/t,o=(t,a=Date.now())=>{i=a,r=null,n&&(clearTimeout(n),n=null),e.apply(null,t)};return[(...e)=>{let t=Date.now(),s=t-i;s>=a?o(e,t):(r=e,n||(n=setTimeout(()=>{n=null,o(r)},a-s)))},()=>r&&o(r)]},eL=(e,t,r=3)=>{let n=0,i=eI(50,250);return eC(r=>{let a=r.loaded,o=r.lengthComputable?r.total:void 0,s=a-n,u=i(s);n=a,e({loaded:a,total:o,progress:o?a/o:void 0,bytes:s,rate:u||void 0,estimated:u&&o&&a<=o?(o-a)/u:void 0,event:r,lengthComputable:null!=o,[t?"download":"upload"]:!0})},r)},eU=(e,t)=>{let r=null!=e;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},eM=e=>(...t)=>H.asap(()=>e(...t)),e_=eg.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,eg.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(eg.origin),eg.navigator&&/(msie|trident)/i.test(eg.navigator.userAgent)):()=>!0,eP=eg.hasStandardBrowserEnv?{write(e,t,r,n,i,a){let o=[e+"="+encodeURIComponent(t)];H.isNumber(r)&&o.push("expires="+new Date(r).toGMTString()),H.isString(n)&&o.push("path="+n),H.isString(i)&&o.push("domain="+i),!0===a&&o.push("secure"),document.cookie=o.join("; ")},read(e){let t=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function eB(e,t,r){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&n||!1==r?t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e:t}let eq=e=>e instanceof eS?{...e}:e;function ek(e,t){t=t||{};let r={};function n(e,t,r,n){return H.isPlainObject(e)&&H.isPlainObject(t)?H.merge.call({caseless:n},e,t):H.isPlainObject(t)?H.merge({},t):H.isArray(t)?t.slice():t}function i(e,t,r,i){return H.isUndefined(t)?H.isUndefined(e)?void 0:n(void 0,e,r,i):n(e,t,r,i)}function a(e,t){if(!H.isUndefined(t))return n(void 0,t)}function o(e,t){return H.isUndefined(t)?H.isUndefined(e)?void 0:n(void 0,e):n(void 0,t)}function s(r,i,a){return a in t?n(r,i):a in e?n(void 0,r):void 0}let u={url:a,method:a,data:a,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:s,headers:(e,t,r)=>i(eq(e),eq(t),r,!0)};return H.forEach(Object.keys(Object.assign({},e,t)),function(n){let a=u[n]||i,o=a(e[n],t[n],n);H.isUndefined(o)&&a!==s||(r[n]=o)}),r}let eF=e=>{let t;let r=ek({},e),{data:n,withXSRFToken:i,xsrfHeaderName:a,xsrfCookieName:o,headers:s,auth:u}=r;if(r.headers=s=eS.from(s),r.url=ei(eB(r.baseURL,r.url),e.params,e.paramsSerializer),u&&s.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):""))),H.isFormData(n)){if(eg.hasStandardBrowserEnv||eg.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(t=s.getContentType())){let[e,...r]=t?t.split(";").map(e=>e.trim()).filter(Boolean):[];s.setContentType([e||"multipart/form-data",...r].join("; "))}}if(eg.hasStandardBrowserEnv&&(i&&H.isFunction(i)&&(i=i(r)),i||!1!==i&&e_(r.url))){let e=a&&o&&eP.read(o);e&&s.set(a,e)}return r},eX="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,r){let n,i,a,o,s;let u=eF(e),l=u.data,c=eS.from(u.headers).normalize(),{responseType:f,onUploadProgress:p,onDownloadProgress:h}=u;function d(){o&&o(),s&&s(),u.cancelToken&&u.cancelToken.unsubscribe(n),u.signal&&u.signal.removeEventListener("abort",n)}let g=new XMLHttpRequest;function m(){if(!g)return;let n=eS.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders());ex(function(e){t(e),d()},function(e){r(e),d()},{data:f&&"text"!==f&&"json"!==f?g.response:g.responseText,status:g.status,statusText:g.statusText,headers:n,config:e,request:g}),g=null}g.open(u.method.toUpperCase(),u.url,!0),g.timeout=u.timeout,"onloadend"in g?g.onloadend=m:g.onreadystatechange=function(){g&&4===g.readyState&&(0!==g.status||g.responseURL&&0===g.responseURL.indexOf("file:"))&&setTimeout(m)},g.onabort=function(){g&&(r(new G("Request aborted",G.ECONNABORTED,e,g)),g=null)},g.onerror=function(){r(new G("Network Error",G.ERR_NETWORK,e,g)),g=null},g.ontimeout=function(){let t=u.timeout?"timeout of "+u.timeout+"ms exceeded":"timeout exceeded",n=u.transitional||eo;u.timeoutErrorMessage&&(t=u.timeoutErrorMessage),r(new G(t,n.clarifyTimeoutError?G.ETIMEDOUT:G.ECONNABORTED,e,g)),g=null},void 0===l&&c.setContentType(null),"setRequestHeader"in g&&H.forEach(c.toJSON(),function(e,t){g.setRequestHeader(t,e)}),H.isUndefined(u.withCredentials)||(g.withCredentials=!!u.withCredentials),f&&"json"!==f&&(g.responseType=u.responseType),h&&([a,s]=eL(h,!0),g.addEventListener("progress",a)),p&&g.upload&&([i,o]=eL(p),g.upload.addEventListener("progress",i),g.upload.addEventListener("loadend",o)),(u.cancelToken||u.signal)&&(n=t=>{g&&(r(!t||t.type?new eO(null,e,g):t),g.abort(),g=null)},u.cancelToken&&u.cancelToken.subscribe(n),u.signal&&(u.signal.aborted?n():u.signal.addEventListener("abort",n)));let b=function(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(u.url);if(b&&-1===eg.protocols.indexOf(b)){r(new G("Unsupported protocol "+b+":",G.ERR_BAD_REQUEST,e));return}g.send(l||null)})},ej=(e,t)=>{let{length:r}=e=e?e.filter(Boolean):[];if(t||r){let r,n=new AbortController,i=function(e){if(!r){r=!0,o();let t=e instanceof Error?e:this.reason;n.abort(t instanceof G?t:new eO(t instanceof Error?t.message:t))}},a=t&&setTimeout(()=>{a=null,i(new G(`timeout ${t} of ms exceeded`,G.ETIMEDOUT))},t),o=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));let{signal:s}=n;return s.unsubscribe=()=>H.asap(o),s}},eV=function*(e,t){let r,n=e.byteLength;if(!t||n<t){yield e;return}let i=0;for(;i<n;)r=i+t,yield e.slice(i,r),i=r},eH=async function*(e,t){for await(let r of eG(e))yield*eV(r,t)},eG=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let t=e.getReader();try{for(;;){let{done:e,value:r}=await t.read();if(e)break;yield r}}finally{await t.cancel()}},ez=(e,t,r,n)=>{let i;let a=eH(e,t),o=0,s=e=>{!i&&(i=!0,n&&n(e))};return new ReadableStream({async pull(e){try{let{done:t,value:n}=await a.next();if(t){s(),e.close();return}let i=n.byteLength;if(r){let e=o+=i;r(e)}e.enqueue(new Uint8Array(n))}catch(e){throw s(e),e}},cancel:e=>(s(e),a.return())},{highWaterMark:2})},e$="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,eW=e$&&"function"==typeof ReadableStream,eY=e$&&("function"==typeof TextEncoder?(n=new TextEncoder,e=>n.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),eK=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},eJ=eW&&eK(()=>{let e=!1,t=new Request(eg.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),eZ=eW&&eK(()=>H.isReadableStream(new Response("").body)),eQ={stream:eZ&&(e=>e.body)};e$&&(o=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{eQ[e]||(eQ[e]=H.isFunction(o[e])?t=>t[e]():(t,r)=>{throw new G(`Response type '${e}' is not supported`,G.ERR_NOT_SUPPORT,r)})}));let e0=async e=>{if(null==e)return 0;if(H.isBlob(e))return e.size;if(H.isSpecCompliantForm(e)){let t=new Request(eg.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return H.isArrayBufferView(e)||H.isArrayBuffer(e)?e.byteLength:(H.isURLSearchParams(e)&&(e+=""),H.isString(e))?(await eY(e)).byteLength:void 0},e1=async(e,t)=>{let r=H.toFiniteNumber(e.getContentLength());return null==r?e0(t):r},e2={http:null,xhr:eX,fetch:e$&&(async e=>{let t,r,{url:n,method:i,data:a,signal:o,cancelToken:s,timeout:u,onDownloadProgress:l,onUploadProgress:c,responseType:f,headers:p,withCredentials:h="same-origin",fetchOptions:d}=eF(e);f=f?(f+"").toLowerCase():"text";let g=ej([o,s&&s.toAbortSignal()],u),m=g&&g.unsubscribe&&(()=>{g.unsubscribe()});try{if(c&&eJ&&"get"!==i&&"head"!==i&&0!==(r=await e1(p,a))){let e,t=new Request(n,{method:"POST",body:a,duplex:"half"});if(H.isFormData(a)&&(e=t.headers.get("content-type"))&&p.setContentType(e),t.body){let[e,n]=eU(r,eL(eM(c)));a=ez(t.body,65536,e,n)}}H.isString(h)||(h=h?"include":"omit");let o="credentials"in Request.prototype;t=new Request(n,{...d,signal:g,method:i.toUpperCase(),headers:p.normalize().toJSON(),body:a,duplex:"half",credentials:o?h:void 0});let s=await fetch(t),u=eZ&&("stream"===f||"response"===f);if(eZ&&(l||u&&m)){let e={};["status","statusText","headers"].forEach(t=>{e[t]=s[t]});let t=H.toFiniteNumber(s.headers.get("content-length")),[r,n]=l&&eU(t,eL(eM(l),!0))||[];s=new Response(ez(s.body,65536,r,()=>{n&&n(),m&&m()}),e)}f=f||"text";let b=await eQ[H.findKey(eQ,f)||"text"](s,e);return!u&&m&&m(),await new Promise((r,n)=>{ex(r,n,{data:b,headers:eS.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:t})})}catch(r){if(m&&m(),r&&"TypeError"===r.name&&/fetch/i.test(r.message))throw Object.assign(new G("Network Error",G.ERR_NETWORK,e,t),{cause:r.cause||r});throw G.from(r,r&&r.code,e,t)}})};H.forEach(e2,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}});let e5=e=>`- ${e}`,e3=e=>H.isFunction(e)||null===e||!1===e,e6={getAdapter:e=>{let t,r;let{length:n}=e=H.isArray(e)?e:[e],i={};for(let a=0;a<n;a++){let n;if(r=t=e[a],!e3(t)&&void 0===(r=e2[(n=String(t)).toLowerCase()]))throw new G(`Unknown adapter '${n}'`);if(r)break;i[n||"#"+a]=r}if(!r){let e=Object.entries(i).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new G("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(e5).join("\n"):" "+e5(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r}};function e8(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eO(null,e)}function e4(e){return e8(e),e.headers=eS.from(e.headers),e.data=eN.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),e6.getAdapter(e.adapter||eb.adapter)(e).then(function(t){return e8(e),t.data=eN.call(e,e.transformResponse,t),t.headers=eS.from(t.headers),t},function(t){return!eR(t)&&(e8(e),t&&t.response&&(t.response.data=eN.call(e,e.transformResponse,t.response),t.response.headers=eS.from(t.response.headers))),Promise.reject(t)})}let e7="1.8.2",e9={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{e9[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});let te={};e9.transitional=function(e,t,r){function n(e,t){return"[Axios v"+e7+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return(r,i,a)=>{if(!1===e)throw new G(n(i," has been removed"+(t?" in "+t:"")),G.ERR_DEPRECATED);return t&&!te[i]&&(te[i]=!0,console.warn(n(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,i,a)}},e9.spelling=function(e){return(t,r)=>(console.warn(`${r} is likely a misspelling of ${e}`),!0)};let tt={assertOptions:function(e,t,r){if("object"!=typeof e)throw new G("options must be an object",G.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),i=n.length;for(;i-- >0;){let a=n[i],o=t[a];if(o){let t=e[a],r=void 0===t||o(t,a,e);if(!0!==r)throw new G("option "+a+" must be "+r,G.ERR_BAD_OPTION_VALUE);continue}if(!0!==r)throw new G("Unknown option "+a,G.ERR_BAD_OPTION)}},validators:e9},tr=tt.validators;class tn{constructor(e){this.defaults=e,this.interceptors={request:new ea,response:new ea}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=Error();let r=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?r&&!String(e.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+r):e.stack=r}catch(e){}}throw e}}_request(e,t){let r,n;"string"==typeof e?(t=t||{}).url=e:t=e||{};let{transitional:i,paramsSerializer:a,headers:o}=t=ek(this.defaults,t);void 0!==i&&tt.assertOptions(i,{silentJSONParsing:tr.transitional(tr.boolean),forcedJSONParsing:tr.transitional(tr.boolean),clarifyTimeoutError:tr.transitional(tr.boolean)},!1),null!=a&&(H.isFunction(a)?t.paramsSerializer={serialize:a}:tt.assertOptions(a,{encode:tr.function,serialize:tr.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),tt.assertOptions(t,{baseUrl:tr.spelling("baseURL"),withXsrfToken:tr.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=o&&H.merge(o.common,o[t.method]);o&&H.forEach(["delete","get","head","post","put","patch","common"],e=>{delete o[e]}),t.headers=eS.concat(s,o);let u=[],l=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(t))&&(l=l&&e.synchronous,u.unshift(e.fulfilled,e.rejected))});let c=[];this.interceptors.response.forEach(function(e){c.push(e.fulfilled,e.rejected)});let f=0;if(!l){let e=[e4.bind(this),void 0];for(e.unshift.apply(e,u),e.push.apply(e,c),n=e.length,r=Promise.resolve(t);f<n;)r=r.then(e[f++],e[f++]);return r}n=u.length;let p=t;for(f=0;f<n;){let e=u[f++],t=u[f++];try{p=e(p)}catch(e){t.call(this,e);break}}try{r=e4.call(this,p)}catch(e){return Promise.reject(e)}for(f=0,n=c.length;f<n;)r=r.then(c[f++],c[f++]);return r}getUri(e){return ei(eB((e=ek(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}H.forEach(["delete","get","head","options"],function(e){tn.prototype[e]=function(t,r){return this.request(ek(r||{},{method:e,url:t,data:(r||{}).data}))}}),H.forEach(["post","put","patch"],function(e){function t(t){return function(r,n,i){return this.request(ek(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}tn.prototype[e]=t(),tn.prototype[e+"Form"]=t(!0)});class ti{constructor(e){let t;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){t=e});let r=this;this.promise.then(e=>{if(!r._listeners)return;let t=r._listeners.length;for(;t-- >0;)r._listeners[t](e);r._listeners=null}),this.promise.then=e=>{let t;let n=new Promise(e=>{r.subscribe(e),t=e}).then(e);return n.cancel=function(){r.unsubscribe(t)},n},e(function(e,n,i){!r.reason&&(r.reason=new eO(e,n,i),t(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){let e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new ti(function(t){e=t}),cancel:e}}}let ta={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ta).forEach(([e,t])=>{ta[t]=e});let to=function e(t){let r=new tn(t),n=u(tn.prototype.request,r);return H.extend(n,tn.prototype,r,{allOwnKeys:!0}),H.extend(n,r,null,{allOwnKeys:!0}),n.create=function(r){return e(ek(t,r))},n}(eb);to.Axios=tn,to.CanceledError=eO,to.CancelToken=ti,to.isCancel=eR,to.VERSION=e7,to.toFormData=Q,to.AxiosError=G,to.Cancel=to.CanceledError,to.all=function(e){return Promise.all(e)},to.spread=function(e){return function(t){return e.apply(null,t)}},to.isAxiosError=function(e){return H.isObject(e)&&!0===e.isAxiosError},to.mergeConfig=ek,to.AxiosHeaders=eS,to.formToJSON=e=>em(H.isHTMLForm(e)?new FormData(e):e),to.getAdapter=e6.getAdapter,to.HttpStatusCode=ta,to.default=to;let ts=to},2667:(e,t,r)=>{var n,i=void 0!==r.g?r.g:"undefined"!=typeof window?window:{},a=r(542);"undefined"!=typeof document?n=document:(n=i["__GLOBAL_DOCUMENT_CACHE@4"])||(n=i["__GLOBAL_DOCUMENT_CACHE@4"]=a),e.exports=n},2678:(e,t,r)=>{"use strict";r.d(t,{ne:()=>R,J2:()=>O});var n,i=r(2638);new Uint8Array([79,112,117,115,72,101,97,100]);var a=function(e){return"string"==typeof e?(0,i.Af)(e):e},o=function(e){e=(0,i.W_)(e);for(var t=[],r=0;e.length>r;){var a=e[r],o=0,s=0,u=e[++s];for(s++;128&u;)o=(127&u)<<7,u=e[s],s++;o+=127&u;for(var l=0;l<n.length;l++){var c=n[l],f=c.id,p=c.parser;if(a===f){t.push(p(e.subarray(s,s+o)));break}}r+=o+s}return t};n=[{id:3,parser:function(e){var t={tag:3,id:e[0]<<8|e[1],flags:e[2],size:3,dependsOnEsId:0,ocrEsId:0,descriptors:[],url:""};if(128&t.flags&&(t.dependsOnEsId=e[t.size]<<8|e[t.size+1],t.size+=2),64&t.flags){var r=e[t.size];t.url=(0,i.Ar)(e.subarray(t.size+1,t.size+1+r)),t.size+=r}return 32&t.flags&&(t.ocrEsId=e[t.size]<<8|e[t.size+1],t.size+=2),t.descriptors=o(e.subarray(t.size))||[],t}},{id:4,parser:function(e){return{tag:4,oti:e[0],streamType:e[1],bufferSize:e[2]<<16|e[3]<<8|e[4],maxBitrate:e[5]<<24|e[6]<<16|e[7]<<8|e[8],avgBitrate:e[9]<<24|e[10]<<16|e[11]<<8|e[12],descriptors:o(e.subarray(13))}}},{id:5,parser:function(e){return{tag:5,bytes:e}}},{id:6,parser:function(e){return{tag:6,bytes:e}}}];var s=function e(t,r,n){void 0===n&&(n=!1),r=Array.isArray(o=r)?o.map(function(e){return a(e)}):[a(o)],t=(0,i.W_)(t);var o,s=[];if(!r.length)return s;for(var u=0;u<t.length;){var l=(t[u]<<24|t[u+1]<<16|t[u+2]<<8|t[u+3])>>>0,c=t.subarray(u+4,u+8);if(0===l)break;var f=u+l;if(f>t.length){if(n)break;f=t.length}var p=t.subarray(u+8,f);(0,i.L7)(c,r[0])&&(1===r.length?s.push(p):s.push.apply(s,e(p,r.slice(1),n))),u=f}return s},u=function(e,t){if(!(t=a(t)).length)return e.subarray(e.length);for(var r=0;r<e.length;){if(bytesMatch(e.subarray(r,r+t.length),t)){var n=(e[r-4]<<24|e[r-3]<<16|e[r-2]<<8|e[r-1])>>>0,i=n>1?r+n:e.byteLength;return e.subarray(r+4,i)}r++}return e.subarray(e.length)},l=function(e,t,r){void 0===t&&(t=4),void 0===r&&(r=function(e){return bytesToNumber(e)});var n=[];if(!e||!e.length)return n;for(var i=bytesToNumber(e.subarray(4,8)),a=8;i;a+=t,i--)n.push(r(e.subarray(a,a+t)));return n},c={EBML:(0,i.W_)([26,69,223,163]),DocType:(0,i.W_)([66,130]),Segment:(0,i.W_)([24,83,128,103]),SegmentInfo:(0,i.W_)([21,73,169,102]),Tracks:(0,i.W_)([22,84,174,107]),Track:(0,i.W_)([174]),TrackNumber:(0,i.W_)([215]),DefaultDuration:(0,i.W_)([35,227,131]),TrackEntry:(0,i.W_)([174]),TrackType:(0,i.W_)([131]),FlagDefault:(0,i.W_)([136]),CodecID:(0,i.W_)([134]),CodecPrivate:(0,i.W_)([99,162]),VideoTrack:(0,i.W_)([224]),AudioTrack:(0,i.W_)([225]),Cluster:(0,i.W_)([31,67,182,117]),Timestamp:(0,i.W_)([231]),TimestampScale:(0,i.W_)([42,215,177]),BlockGroup:(0,i.W_)([160]),BlockDuration:(0,i.W_)([155]),Block:(0,i.W_)([161]),SimpleBlock:(0,i.W_)([163])},f=[128,64,32,16,8,4,2,1],p=function(e){for(var t=1,r=0;r<f.length&&!(e&f[r]);r++)t++;return t},h=function(e,t,r,n){void 0===r&&(r=!0),void 0===n&&(n=!1);var a=p(e[t]),o=e.subarray(t,t+a);return r&&(o=Array.prototype.slice.call(e,t,t+a),o[0]^=f[a-1]),{length:a,value:(0,i.Sk)(o,{signed:n}),bytes:o}},d=function e(t){return"string"==typeof t?t.match(/.{1,2}/g).map(function(t){return e(t)}):"number"==typeof t?(0,i.WG)(t):t},g=function e(t,r,n){if(n>=r.length)return r.length;var a=h(r,n,!1);if((0,i.L7)(t.bytes,a.bytes))return n;var o=h(r,n+a.length);return e(t,r,n+o.length+o.value+a.length)},m=function e(t,r){r=Array.isArray(n=r)?n.map(function(e){return d(e)}):[d(n)],t=(0,i.W_)(t);var n,a=[];if(!r.length)return a;for(var o=0;o<t.length;){var s=h(t,o,!1),u=h(t,o+s.length),l=o+s.length+u.length;127===u.value&&(u.value=g(s,t,l),u.value!==t.length&&(u.value-=l));var c=l+u.value>t.length?t.length:l+u.value,f=t.subarray(l,c);(0,i.L7)(r[0],s.bytes)&&(1===r.length?a.push(f):a=a.concat(e(f,r.slice(1)))),o+=s.length+u.length+f.length}return a},b=function(e){for(var t=0,r={};t<e.length;){var n=127&e[t],i=e[t+1],a=void 0;a=1===i?e[t+2]:e.subarray(t+2,t+2+i),1===n?r.profile=a:2===n?r.level=a:3===n?r.bitDepth=a:4===n?r.chromaSubsampling=a:r[n]=a,t+=2+i}return r},y=r(6450),E=(0,i.W_)([0,0,0,1]),v=(0,i.W_)([0,0,1]),T=(0,i.W_)([0,0,3]),w=function(e){for(var t=[],r=1;r<e.length-2;)(0,i.L7)(e.subarray(r,r+3),T)&&(t.push(r+2),r++),r++;if(0===t.length)return e;var n=e.length-t.length,a=new Uint8Array(n),o=0;for(r=0;r<n;o++,r++)o===t[0]&&(o++,t.shift()),a[r]=e[o];return a},D=function(e,t,r,n){void 0===n&&(n=1/0),e=(0,i.W_)(e),r=[].concat(r);for(var a,o=0,s=0;o<e.length&&(s<n||a);){var u=void 0;if((0,i.L7)(e.subarray(o),E)?u=4:(0,i.L7)(e.subarray(o),v)&&(u=3),!u){o++;continue}if(s++,a)return w(e.subarray(a,o));var l=void 0;"h264"===t?l=31&e[o+u]:"h265"===t&&(l=e[o+u]>>1&63),-1!==r.indexOf(l)&&(a=o+u),o+=u+("h264"===t?1:2)}return e.subarray(0,0)},A={webm:(0,i.W_)([119,101,98,109]),matroska:(0,i.W_)([109,97,116,114,111,115,107,97]),flac:(0,i.W_)([102,76,97,67]),ogg:(0,i.W_)([79,103,103,83]),ac3:(0,i.W_)([11,119]),riff:(0,i.W_)([82,73,70,70]),avi:(0,i.W_)([65,86,73]),wav:(0,i.W_)([87,65,86,69]),"3gp":(0,i.W_)([102,116,121,112,51,103]),mp4:(0,i.W_)([102,116,121,112]),fmp4:(0,i.W_)([115,116,121,112]),mov:(0,i.W_)([102,116,121,112,113,116]),moov:(0,i.W_)([109,111,111,118]),moof:(0,i.W_)([109,111,111,102])},S={aac:function(e){var t=(0,y.A)(e);return(0,i.L7)(e,[255,16],{offset:t,mask:[255,22]})},mp3:function(e){var t=(0,y.A)(e);return(0,i.L7)(e,[255,2],{offset:t,mask:[255,6]})},webm:function(e){var t=m(e,[c.EBML,c.DocType])[0];return(0,i.L7)(t,A.webm)},mkv:function(e){var t=m(e,[c.EBML,c.DocType])[0];return(0,i.L7)(t,A.matroska)},mp4:function(e){return!(S["3gp"](e)||S.mov(e))&&(!!((0,i.L7)(e,A.mp4,{offset:4})||(0,i.L7)(e,A.fmp4,{offset:4})||(0,i.L7)(e,A.moof,{offset:4})||(0,i.L7)(e,A.moov,{offset:4}))||void 0)},mov:function(e){return(0,i.L7)(e,A.mov,{offset:4})},"3gp":function(e){return(0,i.L7)(e,A["3gp"],{offset:4})},ac3:function(e){var t=(0,y.A)(e);return(0,i.L7)(e,A.ac3,{offset:t})},ts:function(e){if(e.length<189&&e.length>=1)return 71===e[0];for(var t=0;t+188<e.length&&t<188;){if(71===e[t]&&71===e[t+188])return!0;t+=1}return!1},flac:function(e){var t=(0,y.A)(e);return(0,i.L7)(e,A.flac,{offset:t})},ogg:function(e){return(0,i.L7)(e,A.ogg)},avi:function(e){return(0,i.L7)(e,A.riff)&&(0,i.L7)(e,A.avi,{offset:8})},wav:function(e){return(0,i.L7)(e,A.riff)&&(0,i.L7)(e,A.wav,{offset:8})},h264:function(e){return D(e,"h264",7,3).length},h265:function(e){return D(e,"h265",[32,33],3).length}},N=Object.keys(S).filter(function(e){return"ts"!==e&&"h264"!==e&&"h265"!==e}).concat(["ts","h264","h265"]);N.forEach(function(e){var t=S[e];S[e]=function(e){return t((0,i.W_)(e))}});var R=function(e){e=(0,i.W_)(e);for(var t=0;t<N.length;t++){var r=N[t];if(S[r](e))return r}return""},O=function(e){return s(e,["moof"]).length>0}},3280:(e,t,r)=>{var n=r(3778).getUint64;e.exports=function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),r={version:e[0],flags:new Uint8Array(e.subarray(1,4)),references:[],referenceId:t.getUint32(4),timescale:t.getUint32(8)},i=12;0===r.version?(r.earliestPresentationTime=t.getUint32(i),r.firstOffset=t.getUint32(i+4),i+=8):(r.earliestPresentationTime=n(e.subarray(i)),r.firstOffset=n(e.subarray(i+8)),i+=16),i+=2;var a=t.getUint16(i);for(i+=2;a>0;i+=12,a--)r.references.push({referenceType:(128&e[i])>>>7,referencedSize:0x7fffffff&t.getUint32(i),subsegmentDuration:t.getUint32(i+4),startsWithSap:!!(128&e[i+8]),sapType:(112&e[i+8])>>>4,sapDeltaTime:0xfffffff&t.getUint32(i+8)});return r}},3778:e=>{e.exports={getUint64:function(e){var t,r=new DataView(e.buffer,e.byteOffset,e.byteLength);return r.getBigUint64?(t=r.getBigUint64(0))<Number.MAX_SAFE_INTEGER?Number(t):t:0x100000000*r.getUint32(0)+r.getUint32(4)},MAX_UINT32:0x100000000}},4229:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},4388:(e,t,r)=>{"use strict";var n=r(8202),i=r(4229),a=r(7810),o=r(400),s=r(8138);c.httpHandler=r(2037),c.requestInterceptorsStorage=new o,c.responseInterceptorsStorage=new o,c.retryManager=new s;var u=function(e){var t={};return e&&e.trim().split("\n").forEach(function(e){var r=e.indexOf(":"),n=e.slice(0,r).trim().toLowerCase(),i=e.slice(r+1).trim();void 0===t[n]?t[n]=i:Array.isArray(t[n])?t[n].push(i):t[n]=[t[n],i]}),t};function l(e,t,r){var n=e;return a(t)?(r=t,"string"==typeof e&&(n={uri:e})):n=i({},t,{uri:e}),n.callback=r,n}function c(e,t,r){return f(t=l(e,t,r))}function f(e){if(void 0===e.callback)throw Error("callback argument missing");if(e.requestType&&c.requestInterceptorsStorage.getIsEnabled()){var t,r,n,i={uri:e.uri||e.url,headers:e.headers||{},body:e.body,metadata:e.metadata||{},retry:e.retry,timeout:e.timeout},a=c.requestInterceptorsStorage.execute(e.requestType,i);e.uri=a.uri,e.headers=a.headers,e.body=a.body,e.metadata=a.metadata,e.retry=a.retry,e.timeout=a.timeout}var o=!1,s=function(t,r,n){o||(o=!0,e.callback(t,r,n))};function l(t){if(clearTimeout(n),clearTimeout(e.retryTimeout),t instanceof Error||(t=Error(""+(t||"Unknown XMLHttpRequest Error"))),t.statusCode=0,!r&&c.retryManager.getIsEnabled()&&e.retry&&e.retry.shouldRetry()){e.retryTimeout=setTimeout(function(){e.retry.moveToNextAttempt(),e.xhr=h,f(e)},e.retry.getCurrentFuzzedDelay());return}if(e.requestType&&c.responseInterceptorsStorage.getIsEnabled()){var i={headers:v.headers||{},body:v.body,responseUrl:h.responseURL,responseType:h.responseType},a=c.responseInterceptorsStorage.execute(e.requestType,i);v.body=a.body,v.headers=a.headers}return s(t,v)}function p(){if(!r){clearTimeout(n),clearTimeout(e.retryTimeout);var t,i=v,a=null;if(0!==(t=e.useXDR&&void 0===h.status?200:1223===h.status?204:h.status)?(i={body:function(){var e=void 0;if(e=h.response?h.response:h.responseText||function(e){try{if("document"===e.responseType)return e.responseXML;var t=e.responseXML&&"parsererror"===e.responseXML.documentElement.nodeName;if(""===e.responseType&&!t)return e.responseXML}catch(e){}return null}(h),E)try{e=JSON.parse(e)}catch(e){}return e}(),statusCode:t,method:g,headers:{},url:d,rawRequest:h},h.getAllResponseHeaders&&(i.headers=u(h.getAllResponseHeaders()))):a=Error("Internal XMLHttpRequest Error"),e.requestType&&c.responseInterceptorsStorage.getIsEnabled()){var o={headers:i.headers||{},body:i.body,responseUrl:h.responseURL,responseType:h.responseType},l=c.responseInterceptorsStorage.execute(e.requestType,o);i.body=l.body,i.headers=l.headers}return s(a,i,i.body)}}var h=e.xhr||null;h||(h=e.cors||e.useXDR?new c.XDomainRequest:new c.XMLHttpRequest);var d=h.url=e.uri||e.url,g=h.method=e.method||"GET",m=e.body||e.data,b=h.headers=e.headers||{},y=!!e.sync,E=!1,v={body:void 0,headers:{},statusCode:0,method:g,url:d,rawRequest:h};if("json"in e&&!1!==e.json&&(E=!0,b.accept||b.Accept||(b.Accept="application/json"),"GET"!==g&&"HEAD"!==g&&(b["content-type"]||b["Content-Type"]||(b["Content-Type"]="application/json"),m=JSON.stringify(!0===e.json?m:e.json))),h.onreadystatechange=function(){4!==h.readyState||c.responseInterceptorsStorage.getIsEnabled()||setTimeout(p,0)},h.onload=p,h.onerror=l,h.onprogress=function(){},h.onabort=function(){r=!0,clearTimeout(e.retryTimeout)},h.ontimeout=l,h.open(g,d,!y,e.username,e.password),y||(h.withCredentials=!!e.withCredentials),!y&&e.timeout>0&&(n=setTimeout(function(){if(!r){r=!0,h.abort("timeout");var e=Error("XMLHttpRequest timeout");e.code="ETIMEDOUT",l(e)}},e.timeout)),h.setRequestHeader)for(t in b)b.hasOwnProperty(t)&&h.setRequestHeader(t,b[t]);else if(e.headers&&!function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}(e.headers))throw Error("Headers cannot be set on an XDomainRequest object");return"responseType"in e&&(h.responseType=e.responseType),"beforeSend"in e&&"function"==typeof e.beforeSend&&e.beforeSend(h),h.send(m||null),h}e.exports=c,e.exports.default=c,c.XMLHttpRequest=n.XMLHttpRequest||function(){},c.XDomainRequest="withCredentials"in new c.XMLHttpRequest?c.XMLHttpRequest:n.XDomainRequest,function(e,t){for(var r=0;r<e.length;r++)t(e[r])}(["get","put","post","patch","head","delete"],function(e){c["delete"===e?"del":e]=function(t,r,n){return(r=l(t,r,n)).method=e.toUpperCase(),f(r)}})},5231:(e,t,r)=>{"use strict";r.d(t,{BX:()=>c,Et:()=>u,TY:()=>h,UD:()=>p,YZ:()=>d,Yg:()=>g,dv:()=>m,fz:()=>f,tB:()=>l});var n=r(8202),i=r.n(n),a={mp4:/^(av0?1|avc0?[1234]|vp0?9|flac|opus|mp3|mp4a|mp4v|stpp.ttml.im1t)/,webm:/^(vp0?[89]|av0?1|opus|vorbis)/,ogg:/^(vp0?[89]|theora|flac|opus|vorbis)/,video:/^(av0?1|avc0?[1234]|vp0?[89]|hvc1|hev1|theora|mp4v)/,audio:/^(mp4a|flac|vorbis|opus|ac-[34]|ec-3|alac|mp3|speex|aac)/,text:/^(stpp.ttml.im1t)/,muxerVideo:/^(avc0?1)/,muxerAudio:/^(mp4a)/,muxerText:/a^/},o=["video","audio","text"],s=["Video","Audio","Text"],u=function(e){return e?e.replace(/avc1\.(\d+)\.(\d+)/i,function(e,t,r){return"avc1."+("00"+Number(t).toString(16)).slice(-2)+"00"+("00"+Number(r).toString(16)).slice(-2)}):e},l=function(e){void 0===e&&(e="");var t=e.split(","),r=[];return t.forEach(function(e){var t;e=e.trim(),o.forEach(function(n){var i=a[n].exec(e.toLowerCase());if(i&&!(i.length<=1)){t=n;var o=e.substring(0,i[1].length),s=e.replace(o,"");r.push({type:o,details:s,mediaType:n})}}),t||r.push({type:e,details:"",mediaType:"unknown"})}),r},c=function(e,t){if(!e.mediaGroups.AUDIO||!t)return null;var r=e.mediaGroups.AUDIO[t];if(!r)return null;for(var n in r){var i=r[n];if(i.default&&i.playlists)return l(i.playlists[0].attributes.CODECS)}return null},f=function(e){return void 0===e&&(e=""),a.audio.test(e.trim().toLowerCase())},p=function(e){if(e&&"string"==typeof e){var t,r=e.toLowerCase().split(",").map(function(e){return u(e.trim())}),n="video";1===r.length&&f(r[0])?n="audio":1===r.length&&(void 0===(t=r[0])&&(t=""),a.text.test(t.trim().toLowerCase()))&&(n="application");var i="mp4";return r.every(function(e){return a.mp4.test(e)})?i="mp4":r.every(function(e){return a.webm.test(e)})?i="webm":r.every(function(e){return a.ogg.test(e)})&&(i="ogg"),n+"/"+i+';codecs="'+e+'"'}},h=function(e,t){return void 0===e&&(e=""),void 0===t&&(t=!1),i().MediaSource&&i().MediaSource.isTypeSupported&&i().MediaSource.isTypeSupported(p(e))||t&&i().ManagedMediaSource&&i().ManagedMediaSource.isTypeSupported&&i().ManagedMediaSource.isTypeSupported(p(e))||!1},d=function(e){return void 0===e&&(e=""),e.toLowerCase().split(",").every(function(e){e=e.trim();for(var t=0;t<s.length;t++)if(a["muxer"+s[t]].test(e))return!0;return!1})},g="mp4a.40.2",m="avc1.4d400d"},5407:(e,t,r)=>{"use strict";function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{A:()=>n})},5473:(e,t,r)=>{"use strict";var n=r(7925).freeze;t.XML_ENTITIES=n({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}),t.HTML_ENTITIES=n({Aacute:"\xc1",aacute:"\xe1",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"\xc2",acirc:"\xe2",acute:"\xb4",Acy:"А",acy:"а",AElig:"\xc6",aelig:"\xe6",af:"⁡",Afr:"\uD835\uDD04",afr:"\uD835\uDD1E",Agrave:"\xc0",agrave:"\xe0",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",AMP:"&",amp:"&",And:"⩓",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"\xc5",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"\uD835\uDD38",aopf:"\uD835\uDD52",ap:"≈",apacir:"⩯",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"\xc5",aring:"\xe5",Ascr:"\uD835\uDC9C",ascr:"\uD835\uDCB6",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"\xc3",atilde:"\xe3",Auml:"\xc4",auml:"\xe4",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",Barwed:"⌆",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",Because:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"\uD835\uDD05",bfr:"\uD835\uDD1F",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"\uD835\uDD39",bopf:"\uD835\uDD53",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxDL:"╗",boxDl:"╖",boxdL:"╕",boxdl:"┐",boxDR:"╔",boxDr:"╓",boxdR:"╒",boxdr:"┌",boxH:"═",boxh:"─",boxHD:"╦",boxHd:"╤",boxhD:"╥",boxhd:"┬",boxHU:"╩",boxHu:"╧",boxhU:"╨",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxUL:"╝",boxUl:"╜",boxuL:"╛",boxul:"┘",boxUR:"╚",boxUr:"╙",boxuR:"╘",boxur:"└",boxV:"║",boxv:"│",boxVH:"╬",boxVh:"╫",boxvH:"╪",boxvh:"┼",boxVL:"╣",boxVl:"╢",boxvL:"╡",boxvl:"┤",boxVR:"╠",boxVr:"╟",boxvR:"╞",boxvr:"├",bprime:"‵",Breve:"˘",breve:"˘",brvbar:"\xa6",Bscr:"ℬ",bscr:"\uD835\uDCB7",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",Cap:"⋒",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"\xc7",ccedil:"\xe7",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"\xb8",Cedilla:"\xb8",cemptyv:"⦲",cent:"\xa2",CenterDot:"\xb7",centerdot:"\xb7",Cfr:"ℭ",cfr:"\uD835\uDD20",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",cir:"○",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"\xae",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",Colon:"∷",colon:":",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",Conint:"∯",conint:"∮",ContourIntegral:"∮",Copf:"ℂ",copf:"\uD835\uDD54",coprod:"∐",Coproduct:"∐",COPY:"\xa9",copy:"\xa9",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",Cross:"⨯",cross:"✗",Cscr:"\uD835\uDC9E",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",Cup:"⋓",cup:"∪",cupbrcap:"⩈",CupCap:"≍",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"\xa4",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",Dagger:"‡",dagger:"†",daleth:"ℸ",Darr:"↡",dArr:"⇓",darr:"↓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",DD:"ⅅ",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",DDotrahd:"⤑",ddotseq:"⩷",deg:"\xb0",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"\uD835\uDD07",dfr:"\uD835\uDD21",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"\xb4",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",Diamond:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"\xa8",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"\xf7",divide:"\xf7",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"\uD835\uDD3B",dopf:"\uD835\uDD55",Dot:"\xa8",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"\xa8",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",Downarrow:"⇓",downarrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"\uD835\uDC9F",dscr:"\uD835\uDCB9",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"\xc9",eacute:"\xe9",easter:"⩮",Ecaron:"Ě",ecaron:"ě",ecir:"≖",Ecirc:"\xca",ecirc:"\xea",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",eDot:"≑",edot:"ė",ee:"ⅇ",efDot:"≒",Efr:"\uD835\uDD08",efr:"\uD835\uDD22",eg:"⪚",Egrave:"\xc8",egrave:"\xe8",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp:" ",emsp13:" ",emsp14:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"\uD835\uDD3C",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",Escr:"ℰ",escr:"ℯ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"\xd0",eth:"\xf0",Euml:"\xcb",euml:"\xeb",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",ExponentialE:"ⅇ",exponentiale:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"\uD835\uDD09",ffr:"\uD835\uDD23",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"\uD835\uDD3D",fopf:"\uD835\uDD57",ForAll:"∀",forall:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"\xbd",frac13:"⅓",frac14:"\xbc",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"\xbe",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",Fscr:"ℱ",fscr:"\uD835\uDCBB",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",gE:"≧",ge:"≥",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"\uD835\uDD0A",gfr:"\uD835\uDD24",Gg:"⋙",gg:"≫",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gl:"≷",gla:"⪥",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gnE:"≩",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"\uD835\uDD3E",gopf:"\uD835\uDD58",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"\uD835\uDCA2",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",Gt:"≫",GT:">",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"\xbd",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",hArr:"⇔",harr:"↔",harrcir:"⥈",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",Hfr:"ℌ",hfr:"\uD835\uDD25",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",Hopf:"ℍ",hopf:"\uD835\uDD59",horbar:"―",HorizontalLine:"─",Hscr:"ℋ",hscr:"\uD835\uDCBD",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"\xcd",iacute:"\xed",ic:"⁣",Icirc:"\xce",icirc:"\xee",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"\xa1",iff:"⇔",Ifr:"ℑ",ifr:"\uD835\uDD26",Igrave:"\xcc",igrave:"\xec",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Im:"ℑ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",Implies:"⇒",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",Int:"∬",int:"∫",intcal:"⊺",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"\uD835\uDD40",iopf:"\uD835\uDD5A",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"\xbf",Iscr:"ℐ",iscr:"\uD835\uDCBE",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"\xcf",iuml:"\xef",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"\uD835\uDD0D",jfr:"\uD835\uDD27",jmath:"ȷ",Jopf:"\uD835\uDD41",jopf:"\uD835\uDD5B",Jscr:"\uD835\uDCA5",jscr:"\uD835\uDCBF",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"\uD835\uDD0E",kfr:"\uD835\uDD28",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"\uD835\uDD42",kopf:"\uD835\uDD5C",Kscr:"\uD835\uDCA6",kscr:"\uD835\uDCC0",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",Lang:"⟪",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"\xab",Larr:"↞",lArr:"⇐",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",lAtail:"⤛",latail:"⤙",late:"⪭",lates:"⪭︀",lBarr:"⤎",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",lE:"≦",le:"≤",LeftAngleBracket:"⟨",LeftArrow:"←",Leftarrow:"⇐",leftarrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"\uD835\uDD0F",lfr:"\uD835\uDD29",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",Ll:"⋘",ll:"≪",llarr:"⇇",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnap:"⪉",lnapprox:"⪉",lnE:"≨",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftarrow:"⟵",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longleftrightarrow:"⟷",longmapsto:"⟼",LongRightArrow:"⟶",Longrightarrow:"⟹",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"\uD835\uDD43",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",Lscr:"ℒ",lscr:"\uD835\uDCC1",Lsh:"↰",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",Lt:"≪",LT:"<",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"\xaf",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"\uD835\uDD10",mfr:"\uD835\uDD2A",mho:"℧",micro:"\xb5",mid:"∣",midast:"*",midcir:"⫰",middot:"\xb7",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"\uD835\uDD44",mopf:"\uD835\uDD5E",mp:"∓",Mscr:"ℳ",mscr:"\uD835\uDCC2",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:"\xa0",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",ne:"≠",nearhk:"⤤",neArr:"⇗",nearr:"↗",nearrow:"↗",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",nexist:"∄",nexists:"∄",Nfr:"\uD835\uDD11",nfr:"\uD835\uDD2B",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlArr:"⇍",nlarr:"↚",nldr:"‥",nlE:"≦̸",nle:"≰",nLeftarrow:"⇍",nleftarrow:"↚",nLeftrightarrow:"⇎",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:"\xa0",Nopf:"ℕ",nopf:"\uD835\uDD5F",Not:"⫬",not:"\xac",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nRightarrow:"⇏",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"\uD835\uDCA9",nscr:"\uD835\uDCC3",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"\xd1",ntilde:"\xf1",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nVDash:"⊯",nVdash:"⊮",nvDash:"⊭",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwArr:"⇖",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",Oacute:"\xd3",oacute:"\xf3",oast:"⊛",ocir:"⊚",Ocirc:"\xd4",ocirc:"\xf4",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"\uD835\uDD12",ofr:"\uD835\uDD2C",ogon:"˛",Ograve:"\xd2",ograve:"\xf2",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"\uD835\uDD46",oopf:"\uD835\uDD60",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",Or:"⩔",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"\xaa",ordm:"\xba",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"\uD835\uDCAA",oscr:"ℴ",Oslash:"\xd8",oslash:"\xf8",osol:"⊘",Otilde:"\xd5",otilde:"\xf5",Otimes:"⨷",otimes:"⊗",otimesas:"⨶",Ouml:"\xd6",ouml:"\xf6",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",par:"∥",para:"\xb6",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"\uD835\uDD13",pfr:"\uD835\uDD2D",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"\xb1",plusmn:"\xb1",plussim:"⨦",plustwo:"⨧",pm:"\xb1",Poincareplane:"ℌ",pointint:"⨕",Popf:"ℙ",popf:"\uD835\uDD61",pound:"\xa3",Pr:"⪻",pr:"≺",prap:"⪷",prcue:"≼",prE:"⪳",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",Prime:"″",prime:"′",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportion:"∷",Proportional:"∝",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"\uD835\uDCAB",pscr:"\uD835\uDCC5",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"\uD835\uDD14",qfr:"\uD835\uDD2E",qint:"⨌",Qopf:"ℚ",qopf:"\uD835\uDD62",qprime:"⁗",Qscr:"\uD835\uDCAC",qscr:"\uD835\uDCC6",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",QUOT:'"',quot:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",Rang:"⟫",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"\xbb",Rarr:"↠",rArr:"⇒",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",rAtail:"⤜",ratail:"⤚",ratio:"∶",rationals:"ℚ",RBarr:"⤐",rBarr:"⤏",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",Re:"ℜ",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",REG:"\xae",reg:"\xae",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",Rfr:"ℜ",rfr:"\uD835\uDD2F",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrow:"→",Rightarrow:"⇒",rightarrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",Ropf:"ℝ",ropf:"\uD835\uDD63",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",Rscr:"ℛ",rscr:"\uD835\uDCC7",Rsh:"↱",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",Sc:"⪼",sc:"≻",scap:"⪸",Scaron:"Š",scaron:"š",sccue:"≽",scE:"⪴",sce:"⪰",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",searhk:"⤥",seArr:"⇘",searr:"↘",searrow:"↘",sect:"\xa7",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"\uD835\uDD16",sfr:"\uD835\uDD30",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"\xad",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",Sopf:"\uD835\uDD4A",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",Square:"□",square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squf:"▪",srarr:"→",Sscr:"\uD835\uDCAE",sscr:"\uD835\uDCC8",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"\xaf",Sub:"⋐",sub:"⊂",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",Subset:"⋐",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",Sum:"∑",sum:"∑",sung:"♪",Sup:"⋑",sup:"⊃",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",Supset:"⋑",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swArr:"⇙",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"\xdf",Tab:"	",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"\uD835\uDD17",tfr:"\uD835\uDD31",there4:"∴",Therefore:"∴",therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",thinsp:" ",ThinSpace:" ",thkap:"≈",thksim:"∼",THORN:"\xde",thorn:"\xfe",Tilde:"∼",tilde:"˜",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",times:"\xd7",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",Topf:"\uD835\uDD4B",topf:"\uD835\uDD65",topfork:"⫚",tosa:"⤩",tprime:"‴",TRADE:"™",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"\uD835\uDCAF",tscr:"\uD835\uDCC9",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"\xda",uacute:"\xfa",Uarr:"↟",uArr:"⇑",uarr:"↑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"\xdb",ucirc:"\xfb",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"\uD835\uDD18",ufr:"\uD835\uDD32",Ugrave:"\xd9",ugrave:"\xf9",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"\xa8",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"\uD835\uDD4C",uopf:"\uD835\uDD66",UpArrow:"↑",Uparrow:"⇑",uparrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",Updownarrow:"⇕",updownarrow:"↕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",upsi:"υ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTee:"⊥",UpTeeArrow:"↥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"\uD835\uDCB0",uscr:"\uD835\uDCCA",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"\xdc",uuml:"\xfc",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",vArr:"⇕",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",Vbar:"⫫",vBar:"⫨",vBarv:"⫩",Vcy:"В",vcy:"в",VDash:"⊫",Vdash:"⊩",vDash:"⊨",vdash:"⊢",Vdashl:"⫦",Vee:"⋁",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",Verbar:"‖",verbar:"|",Vert:"‖",vert:"|",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"\uD835\uDD19",vfr:"\uD835\uDD33",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"\uD835\uDD4D",vopf:"\uD835\uDD67",vprop:"∝",vrtri:"⊳",Vscr:"\uD835\uDCB1",vscr:"\uD835\uDCCB",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",Wedge:"⋀",wedge:"∧",wedgeq:"≙",weierp:"℘",Wfr:"\uD835\uDD1A",wfr:"\uD835\uDD34",Wopf:"\uD835\uDD4E",wopf:"\uD835\uDD68",wp:"℘",wr:"≀",wreath:"≀",Wscr:"\uD835\uDCB2",wscr:"\uD835\uDCCC",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"\uD835\uDD1B",xfr:"\uD835\uDD35",xhArr:"⟺",xharr:"⟷",Xi:"Ξ",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"\uD835\uDD4F",xopf:"\uD835\uDD69",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",Xscr:"\uD835\uDCB3",xscr:"\uD835\uDCCD",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"\xdd",yacute:"\xfd",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"\xa5",Yfr:"\uD835\uDD1C",yfr:"\uD835\uDD36",YIcy:"Ї",yicy:"ї",Yopf:"\uD835\uDD50",yopf:"\uD835\uDD6A",Yscr:"\uD835\uDCB4",yscr:"\uD835\uDCCE",YUcy:"Ю",yucy:"ю",Yuml:"Ÿ",yuml:"\xff",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",Zfr:"ℨ",zfr:"\uD835\uDD37",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",Zopf:"ℤ",zopf:"\uD835\uDD6B",Zscr:"\uD835\uDCB5",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"}),t.entityMap=t.HTML_ENTITIES},5666:e=>{var t={"":1,lr:1,rl:1},r={start:1,center:1,end:1,left:1,right:1,auto:1,"line-left":1,"line-right":1};function n(e){return"string"==typeof e&&!!r[e.toLowerCase()]&&e.toLowerCase()}function i(e,r,i){this.hasBeenReset=!1;var a="",o=!1,s=e,u=r,l=i,c=null,f="",p=!0,h="auto",d="start",g="auto",m="auto",b=100,y="center";Object.defineProperties(this,{id:{enumerable:!0,get:function(){return a},set:function(e){a=""+e}},pauseOnExit:{enumerable:!0,get:function(){return o},set:function(e){o=!!e}},startTime:{enumerable:!0,get:function(){return s},set:function(e){if("number"!=typeof e)throw TypeError("Start time must be set to a number.");s=e,this.hasBeenReset=!0}},endTime:{enumerable:!0,get:function(){return u},set:function(e){if("number"!=typeof e)throw TypeError("End time must be set to a number.");u=e,this.hasBeenReset=!0}},text:{enumerable:!0,get:function(){return l},set:function(e){l=""+e,this.hasBeenReset=!0}},region:{enumerable:!0,get:function(){return c},set:function(e){c=e,this.hasBeenReset=!0}},vertical:{enumerable:!0,get:function(){return f},set:function(e){var r="string"==typeof e&&!!t[e.toLowerCase()]&&e.toLowerCase();if(!1===r)throw SyntaxError("Vertical: an invalid or illegal direction string was specified.");f=r,this.hasBeenReset=!0}},snapToLines:{enumerable:!0,get:function(){return p},set:function(e){p=!!e,this.hasBeenReset=!0}},line:{enumerable:!0,get:function(){return h},set:function(e){if("number"!=typeof e&&"auto"!==e)throw SyntaxError("Line: an invalid number or illegal string was specified.");h=e,this.hasBeenReset=!0}},lineAlign:{enumerable:!0,get:function(){return d},set:function(e){var t=n(e);t?(d=t,this.hasBeenReset=!0):console.warn("lineAlign: an invalid or illegal string was specified.")}},position:{enumerable:!0,get:function(){return g},set:function(e){if(e<0||e>100)throw Error("Position must be between 0 and 100.");g=e,this.hasBeenReset=!0}},positionAlign:{enumerable:!0,get:function(){return m},set:function(e){var t=n(e);t?(m=t,this.hasBeenReset=!0):console.warn("positionAlign: an invalid or illegal string was specified.")}},size:{enumerable:!0,get:function(){return b},set:function(e){if(e<0||e>100)throw Error("Size must be between 0 and 100.");b=e,this.hasBeenReset=!0}},align:{enumerable:!0,get:function(){return y},set:function(e){var t=n(e);if(!t)throw SyntaxError("align: an invalid or illegal alignment string was specified.");y=t,this.hasBeenReset=!0}}}),this.displayState=void 0}i.prototype.getCueAsHTML=function(){return WebVTT.convertCueToDOMTree(window,this.text)},e.exports=i},5714:e=>{!function(){var t={675:function(e,t){"use strict";t.byteLength=function(e){var t=u(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,a=u(e),o=a[0],s=a[1],l=new i((o+s)*3/4-s),c=0,f=s>0?o-4:o;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],l[c++]=t>>16&255,l[c++]=t>>8&255,l[c++]=255&t;return 2===s&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,l[c++]=255&t),1===s&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,l[c++]=t>>8&255,l[c++]=255&t),l},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,a=[],o=0,s=n-i;o<s;o+=16383)a.push(function(e,t,n){for(var i,a=[],o=t;o<n;o+=3)i=(e[o]<<16&0xff0000)+(e[o+1]<<8&65280)+(255&e[o+2]),a.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return a.join("")}(e,o,o+16383>s?s:o+16383));return 1===i?a.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&a.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),a.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,s=a.length;o<s;++o)r[o]=a[o],n[a.charCodeAt(o)]=o;function u(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},72:function(e,t,r){"use strict";var n=r(675),i=r(783),a="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,s.prototype),t}function s(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return c(e)}return u(e,t,r)}function u(e,t,r){if("string"==typeof e)return function(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!s.isEncoding(t))throw TypeError("Unknown encoding: "+t);var r=0|h(e,t),n=o(r),i=n.write(e,t);return i!==r&&(n=n.slice(0,i)),n}(e,t);if(ArrayBuffer.isView(e))return f(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(x(e,ArrayBuffer)||e&&x(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(x(e,SharedArrayBuffer)||e&&x(e.buffer,SharedArrayBuffer)))return function(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),s.prototype),n}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return s.from(n,t,r);var i=function(e){if(s.isBuffer(e)){var t=0|p(e.length),r=o(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?o(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}(e);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return s.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function l(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function c(e){return l(e),o(e<0?0:0|p(e))}function f(e){for(var t=e.length<0?0:0|p(e.length),r=o(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}t.Buffer=s,t.SlowBuffer=function(e){return+e!=e&&(e=0),s.alloc(+e)},t.INSPECT_MAX_BYTES=50,t.kMaxLength=0x7fffffff,s.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),s.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(s.prototype,"parent",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.buffer}}),Object.defineProperty(s.prototype,"offset",{enumerable:!0,get:function(){if(s.isBuffer(this))return this.byteOffset}}),s.poolSize=8192,s.from=function(e,t,r){return u(e,t,r)},Object.setPrototypeOf(s.prototype,Uint8Array.prototype),Object.setPrototypeOf(s,Uint8Array),s.alloc=function(e,t,r){return(l(e),e<=0)?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)},s.allocUnsafe=function(e){return c(e)},s.allocUnsafeSlow=function(e){return c(e)};function p(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function h(e,t){if(s.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||x(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return S(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return R(e).length;default:if(i)return n?-1:S(e).length;t=(""+t).toLowerCase(),i=!0}}function d(e,t,r){var i,a,o,s=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",a=t;a<r;++a)i+=I[e[a]];return i}(this,t,r);case"utf8":case"utf-8":return y(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=this,a=t,o=r,0===a&&o===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(a,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",a=0;a<n.length;a+=2)i+=String.fromCharCode(n[a]+256*n[a+1]);return i}(this,t,r);default:if(s)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function g(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function m(e,t,r,n,i){var a;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(a=r*=1)!=a&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return -1;r=e.length-1}else if(r<0){if(!i)return -1;r=0}if("string"==typeof t&&(t=s.from(t,n)),s.isBuffer(t))return 0===t.length?-1:b(e,t,r,n,i);if("number"==typeof t)return(t&=255,"function"==typeof Uint8Array.prototype.indexOf)?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):b(e,[t],r,n,i);throw TypeError("val must be string, number or Buffer")}function b(e,t,r,n,i){var a,o=1,s=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;o=2,s/=2,u/=2,r/=2}function l(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var c=-1;for(a=r;a<s;a++)if(l(e,a)===l(t,-1===c?0:a-c)){if(-1===c&&(c=a),a-c+1===u)return c*o}else -1!==c&&(a-=a-c),c=-1}else for(r+u>s&&(r=s-u),a=r;a>=0;a--){for(var f=!0,p=0;p<u;p++)if(l(e,a+p)!==l(t,p)){f=!1;break}if(f)return a}return -1}s.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==s.prototype},s.compare=function(e,t){if(x(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),x(t,Uint8Array)&&(t=s.from(t,t.offset,t.byteLength)),!s.isBuffer(e)||!s.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,a=Math.min(r,n);i<a;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:+(n<r)},s.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return s.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=s.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var a=e[r];if(x(a,Uint8Array)&&(a=s.from(a)),!s.isBuffer(a))throw TypeError('"list" argument must be an Array of Buffers');a.copy(n,i),i+=a.length}return n},s.byteLength=h,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},s.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},s.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},s.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?y(this,0,e):d.apply(this,arguments)},s.prototype.toLocaleString=s.prototype.toString,s.prototype.equals=function(e){if(!s.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===s.compare(this,e)},s.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},a&&(s.prototype[a]=s.prototype.inspect),s.prototype.compare=function(e,t,r,n,i){if(x(e,Uint8Array)&&(e=s.from(e,e.offset,e.byteLength)),!s.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var a=i-n,o=r-t,u=Math.min(a,o),l=this.slice(n,i),c=e.slice(t,r),f=0;f<u;++f)if(l[f]!==c[f]){a=l[f],o=c[f];break}return a<o?-1:+(o<a)},s.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},s.prototype.indexOf=function(e,t,r){return m(this,e,t,r,!0)},s.prototype.lastIndexOf=function(e,t,r){return m(this,e,t,r,!1)};function y(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var a,o,s,u,l=e[i],c=null,f=l>239?4:l>223?3:l>191?2:1;if(i+f<=r)switch(f){case 1:l<128&&(c=l);break;case 2:(192&(a=e[i+1]))==128&&(u=(31&l)<<6|63&a)>127&&(c=u);break;case 3:a=e[i+1],o=e[i+2],(192&a)==128&&(192&o)==128&&(u=(15&l)<<12|(63&a)<<6|63&o)>2047&&(u<55296||u>57343)&&(c=u);break;case 4:a=e[i+1],o=e[i+2],s=e[i+3],(192&a)==128&&(192&o)==128&&(192&s)==128&&(u=(15&l)<<18|(63&a)<<12|(63&o)<<6|63&s)>65535&&u<1114112&&(c=u)}null===c?(c=65533,f=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=f}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);for(var r="",n=0;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}(n)}function E(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function v(e,t,r,n,i,a){if(!s.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<a)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function T(e,t,r,n,i,a){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function w(e,t,r,n,a){return t*=1,r>>>=0,a||T(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function D(e,t,r,n,a){return t*=1,r>>>=0,a||T(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}s.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,a,o,s,u,l,c,f,p=this.length-t;if((void 0===r||r>p)&&(r=p),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var h=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var a=t.length;n>a/2&&(n=a/2);for(var o=0;o<n;++o){var s,u=parseInt(t.substr(2*o,2),16);if((s=u)!=s)break;e[r+o]=u}return o}(this,e,t,r);case"utf8":case"utf-8":return i=t,a=r,O(S(e,this.length-i),this,i,a);case"ascii":return o=t,s=r,O(N(e),this,o,s);case"latin1":case"binary":return function(e,t,r,n){return O(N(t),e,r,n)}(this,e,t,r);case"base64":return u=t,l=r,O(R(e),this,u,l);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c=t,f=r,O(function(e,t){for(var r,n,i=[],a=0;a<e.length&&!((t-=2)<0);++a)n=(r=e.charCodeAt(a))>>8,i.push(r%256),i.push(n);return i}(e,this.length-c),this,c,f);default:if(h)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),h=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},s.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,s.prototype),n},s.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||E(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n},s.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||E(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},s.prototype.readUInt8=function(e,t){return e>>>=0,t||E(e,1,this.length),this[e]},s.prototype.readUInt16LE=function(e,t){return e>>>=0,t||E(e,2,this.length),this[e]|this[e+1]<<8},s.prototype.readUInt16BE=function(e,t){return e>>>=0,t||E(e,2,this.length),this[e]<<8|this[e+1]},s.prototype.readUInt32LE=function(e,t){return e>>>=0,t||E(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},s.prototype.readUInt32BE=function(e,t){return e>>>=0,t||E(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},s.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||E(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},s.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||E(e,t,this.length);for(var n=t,i=1,a=this[e+--n];n>0&&(i*=256);)a+=this[e+--n]*i;return a>=(i*=128)&&(a-=Math.pow(2,8*t)),a},s.prototype.readInt8=function(e,t){return(e>>>=0,t||E(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},s.prototype.readInt16LE=function(e,t){e>>>=0,t||E(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt16BE=function(e,t){e>>>=0,t||E(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},s.prototype.readInt32LE=function(e,t){return e>>>=0,t||E(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},s.prototype.readInt32BE=function(e,t){return e>>>=0,t||E(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},s.prototype.readFloatLE=function(e,t){return e>>>=0,t||E(e,4,this.length),i.read(this,e,!0,23,4)},s.prototype.readFloatBE=function(e,t){return e>>>=0,t||E(e,4,this.length),i.read(this,e,!1,23,4)},s.prototype.readDoubleLE=function(e,t){return e>>>=0,t||E(e,8,this.length),i.read(this,e,!0,52,8)},s.prototype.readDoubleBE=function(e,t){return e>>>=0,t||E(e,8,this.length),i.read(this,e,!1,52,8)},s.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;v(this,e,t,r,i,0)}var a=1,o=0;for(this[t]=255&e;++o<r&&(a*=256);)this[t+o]=e/a&255;return t+r},s.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;v(this,e,t,r,i,0)}var a=r-1,o=1;for(this[t+a]=255&e;--a>=0&&(o*=256);)this[t+a]=e/o&255;return t+r},s.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,1,255,0),this[t]=255&e,t+1},s.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},s.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);v(this,e,t,r,i-1,-i)}var a=0,o=1,s=0;for(this[t]=255&e;++a<r&&(o*=256);)e<0&&0===s&&0!==this[t+a-1]&&(s=1),this[t+a]=(e/o>>0)-s&255;return t+r},s.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);v(this,e,t,r,i-1,-i)}var a=r-1,o=1,s=0;for(this[t+a]=255&e;--a>=0&&(o*=256);)e<0&&0===s&&0!==this[t+a+1]&&(s=1),this[t+a]=(e/o>>0)-s&255;return t+r},s.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},s.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},s.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},s.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},s.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},s.prototype.writeFloatLE=function(e,t,r){return w(this,e,t,!0,r)},s.prototype.writeFloatBE=function(e,t,r){return w(this,e,t,!1,r)},s.prototype.writeDoubleLE=function(e,t,r){return D(this,e,t,!0,r)},s.prototype.writeDoubleBE=function(e,t,r){return D(this,e,t,!1,r)},s.prototype.copy=function(e,t,r,n){if(!s.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var a=i-1;a>=0;--a)e[a+t]=this[a+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return i},s.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!s.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var i,a=e.charCodeAt(0);("utf8"===n&&a<128||"latin1"===n)&&(e=a)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var o=s.isBuffer(e)?e:s.from(e,n),u=o.length;if(0===u)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=o[i%u]}return this};var A=/[^+/0-9A-Za-z-_]/g;function S(e,t){t=t||1/0;for(var r,n=e.length,i=null,a=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(t-=3)>-1&&a.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&a.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&a.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return a}function N(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function R(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(A,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function O(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function x(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var I=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()},783:function(e,t){t.read=function(e,t,r,n,i){var a,o,s=8*i-n-1,u=(1<<s)-1,l=u>>1,c=-7,f=r?i-1:0,p=r?-1:1,h=e[t+f];for(f+=p,a=h&(1<<-c)-1,h>>=-c,c+=s;c>0;a=256*a+e[t+f],f+=p,c-=8);for(o=a&(1<<-c)-1,a>>=-c,c+=n;c>0;o=256*o+e[t+f],f+=p,c-=8);if(0===a)a=1-l;else{if(a===u)return o?NaN:1/0*(h?-1:1);o+=Math.pow(2,n),a-=l}return(h?-1:1)*o*Math.pow(2,a-n)},t.write=function(e,t,r,n,i,a){var o,s,u,l=8*a-i-1,c=(1<<l)-1,f=c>>1,p=5960464477539062e-23*(23===i),h=n?0:a-1,d=n?1:-1,g=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(s=+!!isNaN(t),o=c):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+f>=1?t+=p/u:t+=p*Math.pow(2,1-f),t*u>=2&&(o++,u/=2),o+f>=c?(s=0,o=c):o+f>=1?(s=(t*u-1)*Math.pow(2,i),o+=f):(s=t*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;e[r+h]=255&s,h+=d,s/=256,i-=8);for(o=o<<i|s,l+=i;l>0;e[r+h]=255&o,h+=d,o/=256,l-=8);e[r+h-d]|=128*g}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var a=r[e]={exports:{}},o=!0;try{t[e](a,a.exports,n),o=!1}finally{o&&delete r[e]}return a.exports}n.ab="//";var i=n(72);e.exports=i}()},6002:(e,t,r)=>{var n=r(770);n.DOMImplementation,n.XMLSerializer,t.DOMParser=r(8962).DOMParser},6302:(e,t,r)=>{var n=r(7925).NAMESPACE,i=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,a=RegExp("[\\-\\.0-9"+i.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),o=RegExp("^"+i.source+a.source+"*(?::"+i.source+a.source+"*)?$");function s(e,t){this.message=e,this.locator=t,Error.captureStackTrace&&Error.captureStackTrace(this,s)}function u(){}function l(e,t){return t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber,t}function c(e,t,r){for(var i=e.tagName,a=null,o=e.length;o--;){var s=e[o],u=s.qName,l=s.value,c=u.indexOf(":");if(c>0)var p=s.prefix=u.slice(0,c),h=u.slice(c+1),d="xmlns"===p&&h;else h=u,p=null,d="xmlns"===u&&"";s.localName=h,!1!==d&&(null==a&&(a={},f(r,r={})),r[d]=a[d]=l,s.uri=n.XMLNS,t.startPrefixMapping(d,l))}for(var o=e.length;o--;){var p=(s=e[o]).prefix;p&&("xml"===p&&(s.uri=n.XML),"xmlns"!==p&&(s.uri=r[p||""]))}var c=i.indexOf(":");c>0?(p=e.prefix=i.slice(0,c),h=e.localName=i.slice(c+1)):(p=null,h=e.localName=i);var g=e.uri=r[p||""];if(t.startElement(g,h,i,e),!e.closed)return e.currentNSMap=r,e.localNSMap=a,!0;if(t.endElement(g,h,i),a)for(p in a)Object.prototype.hasOwnProperty.call(a,p)&&t.endPrefixMapping(p)}function f(e,t){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}function p(){this.attributeNames={}}s.prototype=Error(),s.prototype.name=s.name,u.prototype={parse:function(e,t,r){var i=this.domBuilder;i.startDocument(),f(t,t={}),function(e,t,r,i,a){function o(e){var t,n=e.slice(1,-1);return Object.hasOwnProperty.call(r,n)?r[n]:"#"!==n.charAt(0)?(a.error("entity not found:"+e),e):(t=parseInt(n.substr(1).replace("x","0x")))>65535?String.fromCharCode(55296+((t-=65536)>>10),56320+(1023&t)):String.fromCharCode(t)}function u(t){if(t>E){var r=e.substring(E,t).replace(/&#?\w+;/g,o);m&&f(E),i.characters(r,0,t-E),E=t}}function f(t,r){for(;t>=d&&(r=g.exec(e));)d=(h=r.index)+r[0].length,m.lineNumber++;m.columnNumber=t-h+1}for(var h=0,d=0,g=/.*(?:\r\n?|\n)|.*$/g,m=i.locator,b=[{currentNSMap:t}],y={},E=0;;){try{var v=e.indexOf("<",E);if(v<0){if(!e.substr(E).match(/^\s*$/)){var T=i.doc,w=T.createTextNode(e.substr(E));T.appendChild(w),i.currentElement=w}return}switch(v>E&&u(v),e.charAt(v+1)){case"/":var D=e.indexOf(">",v+3),A=e.substring(v+2,D).replace(/[ \t\n\r]+$/g,""),S=b.pop();D<0?(A=e.substring(v+2).replace(/[\s<].*/,""),a.error("end tag name: "+A+" is not complete:"+S.tagName),D=v+1+A.length):A.match(/\s</)&&(A=A.replace(/[\s<].*/,""),a.error("end tag name: "+A+" maybe not complete"),D=v+1+A.length);var N=S.localNSMap,R=S.tagName==A;if(R||S.tagName&&S.tagName.toLowerCase()==A.toLowerCase()){if(i.endElement(S.uri,S.localName,A),N)for(var O in N)Object.prototype.hasOwnProperty.call(N,O)&&i.endPrefixMapping(O);R||a.fatalError("end tag name: "+A+" is not match the current start tagName:"+S.tagName)}else b.push(S);D++;break;case"?":m&&f(v),D=function(e,t,r){var n=e.indexOf("?>",t);if(n){var i=e.substring(t,n).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);if(i)return i[0].length,r.processingInstruction(i[1],i[2]),n+2}return -1}(e,v,i);break;case"!":m&&f(v),D=function(e,t,r,n){if("-"===e.charAt(t+2)){if("-"===e.charAt(t+3)){var i=e.indexOf("--\x3e",t+4);if(i>t)return r.comment(e,t+4,i-t-4),i+3;n.error("Unclosed comment")}}else{if("CDATA["==e.substr(t+3,6)){var i=e.indexOf("]]>",t+9);return r.startCDATA(),r.characters(e,t+9,i-t-9),r.endCDATA(),i+3}var a=function(e,t){var r,n=[],i=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;for(i.lastIndex=t,i.exec(e);r=i.exec(e);)if(n.push(r),r[1])return n}(e,t),o=a.length;if(o>1&&/!doctype/i.test(a[0][0])){var s=a[1][0],u=!1,l=!1;o>3&&(/^public$/i.test(a[2][0])?(u=a[3][0],l=o>4&&a[4][0]):/^system$/i.test(a[2][0])&&(l=a[3][0]));var c=a[o-1];return r.startDTD(s,u,l),r.endDTD(),c.index+c[0].length}}return -1}(e,v,i,a);break;default:m&&f(v);var x=new p,I=b[b.length-1].currentNSMap,D=function(e,t,r,i,a,o){function s(e,t,n){r.attributeNames.hasOwnProperty(e)&&o.fatalError("Attribute "+e+" redefined"),r.addValue(e,t.replace(/[\t\n\r]/g," ").replace(/&#?\w+;/g,a),n)}for(var u,l,c=++t,f=0;;){var p=e.charAt(c);switch(p){case"=":if(1===f)u=e.slice(t,c),f=3;else if(2===f)f=3;else throw Error("attribute equal must after attrName");break;case"'":case'"':if(3===f||1===f){if(1===f&&(o.warning('attribute value must after "="'),u=e.slice(t,c)),t=c+1,(c=e.indexOf(p,t))>0)s(u,l=e.slice(t,c),t-1),f=5;else throw Error("attribute value no end '"+p+"' match")}else if(4==f)s(u,l=e.slice(t,c),t),o.warning('attribute "'+u+'" missed start quot('+p+")!!"),t=c+1,f=5;else throw Error('attribute value must after "="');break;case"/":switch(f){case 0:r.setTagName(e.slice(t,c));case 5:case 6:case 7:f=7,r.closed=!0;case 4:case 1:break;case 2:r.closed=!0;break;default:throw Error("attribute invalid close char('/')")}break;case"":return o.error("unexpected end of input"),0==f&&r.setTagName(e.slice(t,c)),c;case">":switch(f){case 0:r.setTagName(e.slice(t,c));case 5:case 6:case 7:break;case 4:case 1:"/"===(l=e.slice(t,c)).slice(-1)&&(r.closed=!0,l=l.slice(0,-1));case 2:2===f&&(l=u),4==f?(o.warning('attribute "'+l+'" missed quot(")!'),s(u,l,t)):(n.isHTML(i[""])&&l.match(/^(?:disabled|checked|selected)$/i)||o.warning('attribute "'+l+'" missed value!! "'+l+'" instead!!'),s(l,l,t));break;case 3:throw Error("attribute value missed!!")}return c;case"\x80":p=" ";default:if(p<=" ")switch(f){case 0:r.setTagName(e.slice(t,c)),f=6;break;case 1:u=e.slice(t,c),f=2;break;case 4:var l=e.slice(t,c);o.warning('attribute "'+l+'" missed quot(")!!'),s(u,l,t);case 5:f=6}else switch(f){case 2:r.tagName,n.isHTML(i[""])&&u.match(/^(?:disabled|checked|selected)$/i)||o.warning('attribute "'+u+'" missed value!! "'+u+'" instead2!!'),s(u,u,t),t=c,f=1;break;case 5:o.warning('attribute space is required"'+u+'"!!');case 6:f=1,t=c;break;case 3:f=4,t=c;break;case 7:throw Error("elements closed character '/' and '>' must be connected to")}}c++}}(e,v,x,I,o,a),C=x.length;if(!x.closed&&function(e,t,r,n){var i=n[r];return null==i&&((i=e.lastIndexOf("</"+r+">"))<t&&(i=e.lastIndexOf("</"+r)),n[r]=i),i<t}(e,D,x.tagName,y)&&(x.closed=!0,r.nbsp||a.warning("unclosed xml attribute")),m&&C){for(var L=l(m,{}),U=0;U<C;U++){var M=x[U];f(M.offset),M.locator=l(m,{})}i.locator=L,c(x,i,I)&&b.push(x),i.locator=m}else c(x,i,I)&&b.push(x);n.isHTML(x.uri)&&!x.closed?D=function(e,t,r,n,i){if(/^(?:script|textarea)$/i.test(r)){var a=e.indexOf("</"+r+">",t),o=e.substring(t+1,a);if(/[&<]/.test(o))return/^script$/i.test(r)||(o=o.replace(/&#?\w+;/g,n)),i.characters(o,0,o.length),a}return t+1}(e,D,x.tagName,o,i):D++}}catch(e){if(e instanceof s)throw e;a.error("element parse error: "+e),D=-1}D>E?E=D:u(Math.max(v,E)+1)}}(e,t,r,i,this.errorHandler),i.endDocument()}},p.prototype={setTagName:function(e){if(!o.test(e))throw Error("invalid tagName:"+e);this.tagName=e},addValue:function(e,t,r){if(!o.test(e))throw Error("invalid attribute:"+e);this.attributeNames[e]=this.length,this[this.length++]={qName:e,value:t,offset:r}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},t.XMLReader=u,t.ParseError=s},6368:()=>{},6450:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(2638),i=(0,n.W_)([73,68,51]),a=function(e,t){void 0===t&&(t=0);var r=(e=(0,n.W_)(e))[t+5],i=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9];return(16&r)>>4?i+20:i+10},o=function e(t,r){return(void 0===r&&(r=0),(t=(0,n.W_)(t)).length-r<10||!(0,n.L7)(t,i,{offset:r}))?r:(r+=a(t,r),e(t,r))}},7219:(e,t,r)=>{var n=r(2667),i=Object.create||function(){function e(){}return function(t){if(1!=arguments.length)throw Error("Object.create shim only accepts one parameter.");return e.prototype=t,new e}}();function a(e,t){this.name="ParsingError",this.code=e.code,this.message=t||e.message}function o(e){function t(e,t,r,n){return(0|e)*3600+(0|t)*60+(0|r)+(0|n)/1e3}var r=e.match(/^(\d+):(\d{1,2})(:\d{1,2})?\.(\d{3})/);return r?r[3]?t(r[1],r[2],r[3].replace(":",""),r[4]):r[1]>59?t(r[1],r[2],0,r[4]):t(0,r[1],r[2],r[4]):null}function s(){this.values=i(null)}function u(e,t,r,n){var i=n?e.split(n):[e];for(var a in i){if("string"==typeof i[a]){var o=i[a].split(r);2===o.length&&t(o[0].trim(),o[1].trim())}}}a.prototype=i(Error.prototype),a.prototype.constructor=a,a.Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}},s.prototype={set:function(e,t){this.get(e)||""===t||(this.values[e]=t)},get:function(e,t,r){return r?this.has(e)?this.values[e]:t[r]:this.has(e)?this.values[e]:t},has:function(e){return e in this.values},alt:function(e,t,r){for(var n=0;n<r.length;++n)if(t===r[n]){this.set(e,t);break}},integer:function(e,t){/^-?\d+$/.test(t)&&this.set(e,parseInt(t,10))},percent:function(e,t){return!!(t.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&(t=parseFloat(t))>=0&&t<=100)&&(this.set(e,t),!0)}};var l=n.createElement&&n.createElement("textarea"),c={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},f={white:"rgba(255,255,255,1)",lime:"rgba(0,255,0,1)",cyan:"rgba(0,255,255,1)",red:"rgba(255,0,0,1)",yellow:"rgba(255,255,0,1)",magenta:"rgba(255,0,255,1)",blue:"rgba(0,0,255,1)",black:"rgba(0,0,0,1)"},p={v:"title",lang:"lang"},h={rt:"ruby"};function d(e,t){for(var r,n=e.document.createElement("div"),i=n,a=[];null!==(r=function(){if(!t)return null;var e,r=t.match(/^([^<]*)(<[^>]*>?)?/);return e=r[1]?r[1]:r[2],t=t.substr(e.length),e}());){if("<"===r[0]){if("/"===r[1]){a.length&&a[a.length-1]===r.substr(2).replace(">","")&&(a.pop(),i=i.parentNode);continue}var s,u,d,g,m=o(r.substr(1,r.length-2));if(m){g=e.document.createProcessingInstruction("timestamp",m),i.appendChild(g);continue}var b=r.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);if(!b||!(g=function(t,r){var n=c[t];if(!n)return null;var i=e.document.createElement(n),a=p[t];return a&&r&&(i[a]=r.trim()),i}(b[1],b[3]))||(u=i,h[(d=g).localName]&&h[d.localName]!==u.localName))continue;if(b[2]){var y=b[2].split(".");y.forEach(function(e){var t=/^bg_/.test(e),r=t?e.slice(3):e;if(f.hasOwnProperty(r)){var n=t?"background-color":"color",i=f[r];g.style[n]=i}}),g.className=y.join(" ")}a.push(b[1]),i.appendChild(g),i=g;continue}i.appendChild(e.document.createTextNode((s=r,l.innerHTML=s,s=l.textContent,l.textContent="",s)))}return n}var g=[[1470,1470],[1472,1472],[1475,1475],[1478,1478],[1488,1514],[1520,1524],[1544,1544],[1547,1547],[1549,1549],[1563,1563],[1566,1610],[1645,1647],[1649,1749],[1765,1766],[1774,1775],[1786,1805],[1807,1808],[1810,1839],[1869,1957],[1969,1969],[1984,2026],[2036,2037],[2042,2042],[2048,2069],[2074,2074],[2084,2084],[2088,2088],[2096,2110],[2112,2136],[2142,2142],[2208,2208],[2210,2220],[8207,8207],[64285,64285],[64287,64296],[64298,64310],[64312,64316],[64318,64318],[64320,64321],[64323,64324],[64326,64449],[64467,64829],[64848,64911],[64914,64967],[65008,65020],[65136,65140],[65142,65276],[67584,67589],[67592,67592],[67594,67637],[67639,67640],[67644,67644],[67647,67669],[67671,67679],[67840,67867],[67872,67897],[67903,67903],[67968,68023],[68030,68031],[68096,68096],[68112,68115],[68117,68119],[68121,68147],[68160,68167],[68176,68184],[68192,68223],[68352,68405],[68416,68437],[68440,68466],[68472,68479],[68608,68680],[126464,126467],[126469,126495],[126497,126498],[126500,126500],[126503,126503],[126505,126514],[126516,126519],[126521,126521],[126523,126523],[126530,126530],[126535,126535],[126537,126537],[126539,126539],[126541,126543],[126545,126546],[126548,126548],[126551,126551],[126553,126553],[126555,126555],[126557,126557],[126559,126559],[126561,126562],[126564,126564],[126567,126570],[126572,126578],[126580,126583],[126585,126588],[126590,126590],[126592,126601],[126603,126619],[126625,126627],[126629,126633],[126635,126651],[1114109,1114109]];function m(){}function b(e,t,r){m.call(this),this.cue=t,this.cueDiv=d(e,t.text);var n={color:"rgba(255, 255, 255, 1)",backgroundColor:"rgba(0, 0, 0, 0.8)",position:"relative",left:0,right:0,top:0,bottom:0,display:"inline",writingMode:""===t.vertical?"horizontal-tb":"lr"===t.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext"};this.applyStyles(n,this.cueDiv),this.div=e.document.createElement("div"),n={direction:function(e){var t=[],r="";if(!e||!e.childNodes)return"ltr";function n(e,t){for(var r=t.childNodes.length-1;r>=0;r--)e.push(t.childNodes[r])}for(n(t,e);r=function e(t){if(!t||!t.length)return null;var r=t.pop(),i=r.textContent||r.innerText;if(i){var a=i.match(/^.*(\n|\r)/);return a?(t.length=0,a[0]):i}return"ruby"===r.tagName?e(t):r.childNodes?(n(t,r),e(t)):void 0}(t);)for(var i=0;i<r.length;i++)if(function(e){for(var t=0;t<g.length;t++){var r=g[t];if(e>=r[0]&&e<=r[1])return!0}return!1}(r.charCodeAt(i)))return"rtl";return"ltr"}(this.cueDiv),writingMode:""===t.vertical?"horizontal-tb":"lr"===t.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext",textAlign:"middle"===t.align?"center":t.align,font:r.font,whiteSpace:"pre-line",position:"absolute"},this.applyStyles(n),this.div.appendChild(this.cueDiv);var i=0;switch(t.positionAlign){case"start":case"line-left":i=t.position;break;case"center":i=t.position-t.size/2;break;case"end":case"line-right":i=t.position-t.size}""===t.vertical?this.applyStyles({left:this.formatStyle(i,"%"),width:this.formatStyle(t.size,"%")}):this.applyStyles({top:this.formatStyle(i,"%"),height:this.formatStyle(t.size,"%")}),this.move=function(e){this.applyStyles({top:this.formatStyle(e.top,"px"),bottom:this.formatStyle(e.bottom,"px"),left:this.formatStyle(e.left,"px"),right:this.formatStyle(e.right,"px"),height:this.formatStyle(e.height,"px"),width:this.formatStyle(e.width,"px")})}}function y(e){var t,r,n,i;if(e.div){r=e.div.offsetHeight,n=e.div.offsetWidth,i=e.div.offsetTop;var a=(a=e.div.childNodes)&&(a=a[0])&&a.getClientRects&&a.getClientRects();e=e.div.getBoundingClientRect(),t=a?Math.max(a[0]&&a[0].height||0,e.height/a.length):0}this.left=e.left,this.right=e.right,this.top=e.top||i,this.height=e.height||r,this.bottom=e.bottom||i+(e.height||r),this.width=e.width||n,this.lineHeight=void 0!==t?t:e.lineHeight}function E(){}m.prototype.applyStyles=function(e,t){for(var r in t=t||this.div,e)e.hasOwnProperty(r)&&(t.style[r]=e[r])},m.prototype.formatStyle=function(e,t){return 0===e?0:e+t},b.prototype=i(m.prototype),b.prototype.constructor=b,y.prototype.move=function(e,t){switch(t=void 0!==t?t:this.lineHeight,e){case"+x":this.left+=t,this.right+=t;break;case"-x":this.left-=t,this.right-=t;break;case"+y":this.top+=t,this.bottom+=t;break;case"-y":this.top-=t,this.bottom-=t}},y.prototype.overlaps=function(e){return this.left<e.right&&this.right>e.left&&this.top<e.bottom&&this.bottom>e.top},y.prototype.overlapsAny=function(e){for(var t=0;t<e.length;t++)if(this.overlaps(e[t]))return!0;return!1},y.prototype.within=function(e){return this.top>=e.top&&this.bottom<=e.bottom&&this.left>=e.left&&this.right<=e.right},y.prototype.overlapsOppositeAxis=function(e,t){switch(t){case"+x":return this.left<e.left;case"-x":return this.right>e.right;case"+y":return this.top<e.top;case"-y":return this.bottom>e.bottom}},y.prototype.intersectPercentage=function(e){return Math.max(0,Math.min(this.right,e.right)-Math.max(this.left,e.left))*Math.max(0,Math.min(this.bottom,e.bottom)-Math.max(this.top,e.top))/(this.height*this.width)},y.prototype.toCSSCompatValues=function(e){return{top:this.top-e.top,bottom:e.bottom-this.bottom,left:this.left-e.left,right:e.right-this.right,height:this.height,width:this.width}},y.getSimpleBoxPosition=function(e){var t=e.div?e.div.offsetHeight:e.tagName?e.offsetHeight:0,r=e.div?e.div.offsetWidth:e.tagName?e.offsetWidth:0,n=e.div?e.div.offsetTop:e.tagName?e.offsetTop:0;return{left:(e=e.div?e.div.getBoundingClientRect():e.tagName?e.getBoundingClientRect():e).left,right:e.right,top:e.top||n,height:e.height||t,bottom:e.bottom||n+(e.height||t),width:e.width||r}},E.StringDecoder=function(){return{decode:function(e){if(!e)return"";if("string"!=typeof e)throw Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(e))}}},E.convertCueToDOMTree=function(e,t){return e&&t?d(e,t):null},E.processCues=function(e,t,r){if(!e||!t||!r)return null;for(;r.firstChild;)r.removeChild(r.firstChild);var n=e.document.createElement("div");if(n.style.position="absolute",n.style.left="0",n.style.right="0",n.style.top="0",n.style.bottom="0",n.style.margin="1.5%",r.appendChild(n),!function(e){for(var t=0;t<e.length;t++)if(e[t].hasBeenReset||!e[t].displayState)return!0;return!1}(t)){for(var i=0;i<t.length;i++)n.appendChild(t[i].displayState);return}var a=[],o=y.getSimpleBoxPosition(n),s={font:Math.round(5*o.height)/100+"px sans-serif"};!function(){for(var r,i,u=0;u<t.length;u++)r=new b(e,i=t[u],s),n.appendChild(r.div),function(e,t,r,n){var i=new y(t),a=t.cue,o=function(e){if("number"==typeof e.line&&(e.snapToLines||e.line>=0&&e.line<=100))return e.line;if(!e.track||!e.track.textTrackList||!e.track.textTrackList.mediaElement)return -1;for(var t=e.track,r=t.textTrackList,n=0,i=0;i<r.length&&r[i]!==t;i++)"showing"===r[i].mode&&n++;return -1*++n}(a),s=[];if(a.snapToLines){switch(a.vertical){case"":s=["+y","-y"],u="height";break;case"rl":s=["+x","-x"],u="width";break;case"lr":s=["-x","+x"],u="width"}var u,l=i.lineHeight,c=l*Math.round(o),f=r[u]+l,p=s[0];Math.abs(c)>f&&(c=Math.ceil(f/l)*l*(c<0?-1:1)),o<0&&(c+=""===a.vertical?r.height:r.width,s=s.reverse()),i.move(p,c)}else{var h=i.lineHeight/r.height*100;switch(a.lineAlign){case"center":o-=h/2;break;case"end":o-=h}switch(a.vertical){case"":t.applyStyles({top:t.formatStyle(o,"%")});break;case"rl":t.applyStyles({left:t.formatStyle(o,"%")});break;case"lr":t.applyStyles({right:t.formatStyle(o,"%")})}s=["+y","-x","+x","-y"],i=new y(t)}var d=function(e,t){for(var i,a=new y(e),o=1,s=0;s<t.length;s++){for(;e.overlapsOppositeAxis(r,t[s])||e.within(r)&&e.overlapsAny(n);)e.move(t[s]);if(e.within(r))return e;var u=e.intersectPercentage(r);o>u&&(i=new y(e),o=u),e=new y(a)}return i||a}(i,s);t.move(d.toCSSCompatValues(r))}(0,r,o,a),i.displayState=r.div,a.push(y.getSimpleBoxPosition(r))}()},E.Parser=function(e,t,r){r||(r=t,t={}),t||(t={}),this.window=e,this.vttjs=t,this.state="INITIAL",this.buffer="",this.decoder=r||new TextDecoder("utf8"),this.regionList=[]},E.Parser.prototype={reportOrThrowError:function(e){if(e instanceof a)this.onparsingerror&&this.onparsingerror(e);else throw e},parse:function(e){var t=this;function r(){for(var e=t.buffer,r=0;r<e.length&&"\r"!==e[r]&&"\n"!==e[r];)++r;var n=e.substr(0,r);return"\r"===e[r]&&++r,"\n"===e[r]&&++r,t.buffer=e.substr(r),n}e&&(t.buffer+=t.decoder.decode(e,{stream:!0}));try{if("INITIAL"===t.state){if(!/\r\n|\n/.test(t.buffer))return this;var n,i,l=(i=r()).match(/^WEBVTT([ \t].*)?$/);if(!l||!l[0])throw new a(a.Errors.BadSignature);t.state="HEADER"}for(var c=!1;t.buffer&&/\r\n|\n/.test(t.buffer);)switch(c?c=!1:i=r(),t.state){case"HEADER":/:/.test(i)?(n=i).match(/X-TIMESTAMP-MAP/)?u(n,function(e,r){if("X-TIMESTAMP-MAP"===e){var n;n=new s,u(r,function(e,t){switch(e){case"MPEGT":n.integer(e+"S",t);break;case"LOCA":n.set(e+"L",o(t))}},/[^\d]:/,/,/),t.ontimestampmap&&t.ontimestampmap({MPEGTS:n.get("MPEGTS"),LOCAL:n.get("LOCAL")})}},/=/):u(n,function(e,r){"Region"===e&&!function(e){var r=new s;if(u(e,function(e,t){switch(e){case"id":r.set(e,t);break;case"width":r.percent(e,t);break;case"lines":r.integer(e,t);break;case"regionanchor":case"viewportanchor":var n=t.split(",");if(2!==n.length)break;var i=new s;if(i.percent("x",n[0]),i.percent("y",n[1]),!i.has("x")||!i.has("y"))break;r.set(e+"X",i.get("x")),r.set(e+"Y",i.get("y"));break;case"scroll":r.alt(e,t,["up"])}},/=/,/\s/),r.has("id")){var n=new(t.vttjs.VTTRegion||t.window.VTTRegion);n.width=r.get("width",100),n.lines=r.get("lines",3),n.regionAnchorX=r.get("regionanchorX",0),n.regionAnchorY=r.get("regionanchorY",100),n.viewportAnchorX=r.get("viewportanchorX",0),n.viewportAnchorY=r.get("viewportanchorY",100),n.scroll=r.get("scroll",""),t.onregion&&t.onregion(n),t.regionList.push({id:r.get("id"),region:n})}}(r)},/:/):i||(t.state="ID");continue;case"NOTE":i||(t.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(i)){t.state="NOTE";break}if(!i)continue;t.cue=new(t.vttjs.VTTCue||t.window.VTTCue)(0,0,"");try{t.cue.align="center"}catch(e){t.cue.align="middle"}if(t.state="CUE",-1===i.indexOf("--\x3e")){t.cue.id=i;continue}case"CUE":try{!function(e,t,r){var n=e;function i(){var t=o(e);if(null===t)throw new a(a.Errors.BadTimeStamp,"Malformed timestamp: "+n);return e=e.replace(/^[^\sa-zA-Z-]+/,""),t}function l(){e=e.replace(/^\s+/,"")}if(l(),t.startTime=i(),l(),"--\x3e"!==e.substr(0,3))throw new a(a.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '--\x3e'): "+n);e=e.substr(3),l(),t.endTime=i(),l(),function(e,t){var n=new s;u(e,function(e,t){switch(e){case"region":for(var i=r.length-1;i>=0;i--)if(r[i].id===t){n.set(e,r[i].region);break}break;case"vertical":n.alt(e,t,["rl","lr"]);break;case"line":var a=t.split(","),o=a[0];n.integer(e,o),n.percent(e,o)&&n.set("snapToLines",!1),n.alt(e,o,["auto"]),2===a.length&&n.alt("lineAlign",a[1],["start","center","end"]);break;case"position":a=t.split(","),n.percent(e,a[0]),2===a.length&&n.alt("positionAlign",a[1],["start","center","end"]);break;case"size":n.percent(e,t);break;case"align":n.alt(e,t,["start","center","end","left","right"])}},/:/,/\s/),t.region=n.get("region",null),t.vertical=n.get("vertical","");try{t.line=n.get("line","auto")}catch(e){}t.lineAlign=n.get("lineAlign","start"),t.snapToLines=n.get("snapToLines",!0),t.size=n.get("size",100);try{t.align=n.get("align","center")}catch(e){t.align=n.get("align","middle")}try{t.position=n.get("position","auto")}catch(e){t.position=n.get("position",{start:0,left:0,center:50,middle:50,end:100,right:100},t.align)}t.positionAlign=n.get("positionAlign",{start:"start",left:"start",center:"center",middle:"center",end:"end",right:"end"},t.align)}(e,t)}(i,t.cue,t.regionList)}catch(e){t.reportOrThrowError(e),t.cue=null,t.state="BADCUE";continue}t.state="CUETEXT";continue;case"CUETEXT":var f=-1!==i.indexOf("--\x3e");if(!i||f&&(c=!0)){t.oncue&&t.oncue(t.cue),t.cue=null,t.state="ID";continue}t.cue.text&&(t.cue.text+="\n"),t.cue.text+=i.replace(/\u2028/g,"\n").replace(/u2029/g,"\n");continue;case"BADCUE":i||(t.state="ID");continue}}catch(e){t.reportOrThrowError(e),"CUETEXT"===t.state&&t.cue&&t.oncue&&t.oncue(t.cue),t.cue=null,t.state="INITIAL"===t.state?"BADWEBVTT":"BADCUE"}return this},flush:function(){try{if(this.buffer+=this.decoder.decode(),(this.cue||"HEADER"===this.state)&&(this.buffer+="\n\n",this.parse()),"INITIAL"===this.state)throw new a(a.Errors.BadSignature)}catch(e){this.reportOrThrowError(e)}return this.onflush&&this.onflush(),this}},e.exports=E},7810:e=>{e.exports=function(e){if(!e)return!1;var r=t.call(e);return"[object Function]"===r||"function"==typeof e&&"[object RegExp]"!==r||"undefined"!=typeof window&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)};var t=Object.prototype.toString},7925:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=Object),t&&"function"==typeof t.freeze?t.freeze(e):e}var n=r({HTML:"text/html",isHTML:function(e){return e===n.HTML},XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),i=r({HTML:"http://www.w3.org/1999/xhtml",isHTML:function(e){return e===i.HTML},SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});t.assign=function(e,t){if(null===e||"object"!=typeof e)throw TypeError("target is not an object");for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},t.find=function(e,t,r){if(void 0===r&&(r=Array.prototype),e&&"function"==typeof r.find)return r.find.call(e,t);for(var n=0;n<e.length;n++)if(Object.prototype.hasOwnProperty.call(e,n)){var i=e[n];if(t.call(void 0,i,n,e))return i}},t.freeze=r,t.MIME_TYPE=n,t.NAMESPACE=i},8138:e=>{"use strict";var t=function(){function e(){this.maxAttempts_=1,this.delayFactor_=.1,this.fuzzFactor_=.1,this.initialDelay_=1e3,this.enabled_=!1}var t=e.prototype;return t.getIsEnabled=function(){return this.enabled_},t.enable=function(){this.enabled_=!0},t.disable=function(){this.enabled_=!1},t.reset=function(){this.maxAttempts_=1,this.delayFactor_=.1,this.fuzzFactor_=.1,this.initialDelay_=1e3,this.enabled_=!1},t.getMaxAttempts=function(){return this.maxAttempts_},t.setMaxAttempts=function(e){this.maxAttempts_=e},t.getDelayFactor=function(){return this.delayFactor_},t.setDelayFactor=function(e){this.delayFactor_=e},t.getFuzzFactor=function(){return this.fuzzFactor_},t.setFuzzFactor=function(e){this.fuzzFactor_=e},t.getInitialDelay=function(){return this.initialDelay_},t.setInitialDelay=function(e){this.initialDelay_=e},t.createRetry=function(e){var t=void 0===e?{}:e,n=t.maxAttempts,i=t.delayFactor,a=t.fuzzFactor,o=t.initialDelay;return new r({maxAttempts:n||this.maxAttempts_,delayFactor:i||this.delayFactor_,fuzzFactor:a||this.fuzzFactor_,initialDelay:o||this.initialDelay_})},e}(),r=function(){function e(e){this.maxAttempts_=e.maxAttempts,this.delayFactor_=e.delayFactor,this.fuzzFactor_=e.fuzzFactor,this.currentDelay_=e.initialDelay,this.currentAttempt_=1}var t=e.prototype;return t.moveToNextAttempt=function(){this.currentAttempt_++;var e=this.currentDelay_*this.delayFactor_;this.currentDelay_=this.currentDelay_+e},t.shouldRetry=function(){return this.currentAttempt_<this.maxAttempts_},t.getCurrentDelay=function(){return this.currentDelay_},t.getCurrentMinPossibleDelay=function(){return(1-this.fuzzFactor_)*this.currentDelay_},t.getCurrentMaxPossibleDelay=function(){return(1+this.fuzzFactor_)*this.currentDelay_},t.getCurrentFuzzedDelay=function(){var e=this.getCurrentMinPossibleDelay();return e+Math.random()*(this.getCurrentMaxPossibleDelay()-e)},e}();e.exports=t},8202:(e,t,r)=>{var n;n="undefined"!=typeof window?window:void 0!==r.g?r.g:"undefined"!=typeof self?self:{},e.exports=n},8850:(e,t,r)=>{"use strict";r.d(t,{iX:()=>d});var n=function(){function e(){this.listeners={}}var t=e.prototype;return t.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},t.off=function(e,t){if(!this.listeners[e])return!1;var r=this.listeners[e].indexOf(t);return this.listeners[e]=this.listeners[e].slice(0),this.listeners[e].splice(r,1),r>-1},t.trigger=function(e){var t=this.listeners[e];if(t){if(2==arguments.length)for(var r=t.length,n=0;n<r;++n)t[n].call(this,arguments[1]);else for(var i=Array.prototype.slice.call(arguments,1),a=t.length,o=0;o<a;++o)t[o].apply(this,i)}},t.dispose=function(){this.listeners={}},t.pipe=function(e){this.on("data",function(t){e.push(t)})},e}(),i=r(5407),a=r(9410);class o extends n{constructor(){super(),this.buffer=""}push(e){let t;for(this.buffer+=e,t=this.buffer.indexOf("\n");t>-1;t=this.buffer.indexOf("\n"))this.trigger("data",this.buffer.substring(0,t)),this.buffer=this.buffer.substring(t+1)}}let s=function(e){let t=/([0-9.]*)?@?([0-9.]*)?/.exec(e||""),r={};return t[1]&&(r.length=parseInt(t[1],10)),t[2]&&(r.offset=parseInt(t[2],10)),r},u=function(e){let t;let r={};if(!e)return r;let n=e.split(RegExp('(?:^|,)((?:[^=]*)=(?:"[^"]*"|[^,]*))')),i=n.length;for(;i--;)""!==n[i]&&((t=/([^=]*)=(.*)/.exec(n[i]).slice(1))[0]=t[0].replace(/^\s+|\s+$/g,""),t[1]=t[1].replace(/^\s+|\s+$/g,""),t[1]=t[1].replace(/^['"](.*)['"]$/g,"$1"),r[t[0]]=t[1]);return r},l=e=>{let t=e.split("x"),r={};return t[0]&&(r.width=parseInt(t[0],10)),t[1]&&(r.height=parseInt(t[1],10)),r};class c extends n{constructor(){super(),this.customParsers=[],this.tagMappers=[]}push(e){let t,r;if(0!==(e=e.trim()).length){if("#"!==e[0]){this.trigger("data",{type:"uri",uri:e});return}this.tagMappers.reduce((t,r)=>{let n=r(e);return n===e?t:t.concat([n])},[e]).forEach(e=>{for(let t=0;t<this.customParsers.length;t++)if(this.customParsers[t].call(this,e))return;if(0!==e.indexOf("#EXT")){this.trigger("data",{type:"comment",text:e.slice(1)});return}if(e=e.replace("\r",""),t=/^#EXTM3U/.exec(e)){this.trigger("data",{type:"tag",tagType:"m3u"});return}if(t=/^#EXTINF:([0-9\.]*)?,?(.*)?$/.exec(e)){r={type:"tag",tagType:"inf"},t[1]&&(r.duration=parseFloat(t[1])),t[2]&&(r.title=t[2]),this.trigger("data",r);return}if(t=/^#EXT-X-TARGETDURATION:([0-9.]*)?/.exec(e)){r={type:"tag",tagType:"targetduration"},t[1]&&(r.duration=parseInt(t[1],10)),this.trigger("data",r);return}if(t=/^#EXT-X-VERSION:([0-9.]*)?/.exec(e)){r={type:"tag",tagType:"version"},t[1]&&(r.version=parseInt(t[1],10)),this.trigger("data",r);return}if(t=/^#EXT-X-MEDIA-SEQUENCE:(\-?[0-9.]*)?/.exec(e)){r={type:"tag",tagType:"media-sequence"},t[1]&&(r.number=parseInt(t[1],10)),this.trigger("data",r);return}if(t=/^#EXT-X-DISCONTINUITY-SEQUENCE:(\-?[0-9.]*)?/.exec(e)){r={type:"tag",tagType:"discontinuity-sequence"},t[1]&&(r.number=parseInt(t[1],10)),this.trigger("data",r);return}if(t=/^#EXT-X-PLAYLIST-TYPE:(.*)?$/.exec(e)){r={type:"tag",tagType:"playlist-type"},t[1]&&(r.playlistType=t[1]),this.trigger("data",r);return}if(t=/^#EXT-X-BYTERANGE:(.*)?$/.exec(e)){r=(0,i.A)(s(t[1]),{type:"tag",tagType:"byterange"}),this.trigger("data",r);return}if(t=/^#EXT-X-ALLOW-CACHE:(YES|NO)?/.exec(e)){r={type:"tag",tagType:"allow-cache"},t[1]&&(r.allowed=!/NO/.test(t[1])),this.trigger("data",r);return}if(t=/^#EXT-X-MAP:(.*)$/.exec(e)){if(r={type:"tag",tagType:"map"},t[1]){let e=u(t[1]);e.URI&&(r.uri=e.URI),e.BYTERANGE&&(r.byterange=s(e.BYTERANGE))}this.trigger("data",r);return}if(t=/^#EXT-X-STREAM-INF:(.*)$/.exec(e)){r={type:"tag",tagType:"stream-inf"},t[1]&&(r.attributes=u(t[1]),r.attributes.RESOLUTION&&(r.attributes.RESOLUTION=l(r.attributes.RESOLUTION)),r.attributes.BANDWIDTH&&(r.attributes.BANDWIDTH=parseInt(r.attributes.BANDWIDTH,10)),r.attributes["FRAME-RATE"]&&(r.attributes["FRAME-RATE"]=parseFloat(r.attributes["FRAME-RATE"])),r.attributes["PROGRAM-ID"]&&(r.attributes["PROGRAM-ID"]=parseInt(r.attributes["PROGRAM-ID"],10))),this.trigger("data",r);return}if(t=/^#EXT-X-MEDIA:(.*)$/.exec(e)){r={type:"tag",tagType:"media"},t[1]&&(r.attributes=u(t[1])),this.trigger("data",r);return}if(t=/^#EXT-X-ENDLIST/.exec(e)){this.trigger("data",{type:"tag",tagType:"endlist"});return}if(t=/^#EXT-X-DISCONTINUITY/.exec(e)){this.trigger("data",{type:"tag",tagType:"discontinuity"});return}if(t=/^#EXT-X-PROGRAM-DATE-TIME:(.*)$/.exec(e)){r={type:"tag",tagType:"program-date-time"},t[1]&&(r.dateTimeString=t[1],r.dateTimeObject=new Date(t[1])),this.trigger("data",r);return}if(t=/^#EXT-X-KEY:(.*)$/.exec(e)){r={type:"tag",tagType:"key"},t[1]&&(r.attributes=u(t[1]),r.attributes.IV&&("0x"===r.attributes.IV.substring(0,2).toLowerCase()&&(r.attributes.IV=r.attributes.IV.substring(2)),r.attributes.IV=r.attributes.IV.match(/.{8}/g),r.attributes.IV[0]=parseInt(r.attributes.IV[0],16),r.attributes.IV[1]=parseInt(r.attributes.IV[1],16),r.attributes.IV[2]=parseInt(r.attributes.IV[2],16),r.attributes.IV[3]=parseInt(r.attributes.IV[3],16),r.attributes.IV=new Uint32Array(r.attributes.IV))),this.trigger("data",r);return}if(t=/^#EXT-X-START:(.*)$/.exec(e)){r={type:"tag",tagType:"start"},t[1]&&(r.attributes=u(t[1]),r.attributes["TIME-OFFSET"]=parseFloat(r.attributes["TIME-OFFSET"]),r.attributes.PRECISE=/YES/.test(r.attributes.PRECISE)),this.trigger("data",r);return}if(t=/^#EXT-X-CUE-OUT-CONT:(.*)?$/.exec(e)){r={type:"tag",tagType:"cue-out-cont"},t[1]?r.data=t[1]:r.data="",this.trigger("data",r);return}if(t=/^#EXT-X-CUE-OUT:(.*)?$/.exec(e)){r={type:"tag",tagType:"cue-out"},t[1]?r.data=t[1]:r.data="",this.trigger("data",r);return}if(t=/^#EXT-X-CUE-IN:?(.*)?$/.exec(e)){r={type:"tag",tagType:"cue-in"},t[1]?r.data=t[1]:r.data="",this.trigger("data",r);return}if((t=/^#EXT-X-SKIP:(.*)$/.exec(e))&&t[1]){(r={type:"tag",tagType:"skip"}).attributes=u(t[1]),r.attributes.hasOwnProperty("SKIPPED-SEGMENTS")&&(r.attributes["SKIPPED-SEGMENTS"]=parseInt(r.attributes["SKIPPED-SEGMENTS"],10)),r.attributes.hasOwnProperty("RECENTLY-REMOVED-DATERANGES")&&(r.attributes["RECENTLY-REMOVED-DATERANGES"]=r.attributes["RECENTLY-REMOVED-DATERANGES"].split("	")),this.trigger("data",r);return}if((t=/^#EXT-X-PART:(.*)$/.exec(e))&&t[1]){(r={type:"tag",tagType:"part"}).attributes=u(t[1]),["DURATION"].forEach(function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=parseFloat(r.attributes[e]))}),["INDEPENDENT","GAP"].forEach(function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=/YES/.test(r.attributes[e]))}),r.attributes.hasOwnProperty("BYTERANGE")&&(r.attributes.byterange=s(r.attributes.BYTERANGE)),this.trigger("data",r);return}if((t=/^#EXT-X-SERVER-CONTROL:(.*)$/.exec(e))&&t[1]){(r={type:"tag",tagType:"server-control"}).attributes=u(t[1]),["CAN-SKIP-UNTIL","PART-HOLD-BACK","HOLD-BACK"].forEach(function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=parseFloat(r.attributes[e]))}),["CAN-SKIP-DATERANGES","CAN-BLOCK-RELOAD"].forEach(function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=/YES/.test(r.attributes[e]))}),this.trigger("data",r);return}if((t=/^#EXT-X-PART-INF:(.*)$/.exec(e))&&t[1]){(r={type:"tag",tagType:"part-inf"}).attributes=u(t[1]),["PART-TARGET"].forEach(function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=parseFloat(r.attributes[e]))}),this.trigger("data",r);return}if((t=/^#EXT-X-PRELOAD-HINT:(.*)$/.exec(e))&&t[1]){(r={type:"tag",tagType:"preload-hint"}).attributes=u(t[1]),["BYTERANGE-START","BYTERANGE-LENGTH"].forEach(function(e){if(r.attributes.hasOwnProperty(e)){r.attributes[e]=parseInt(r.attributes[e],10);let t="BYTERANGE-LENGTH"===e?"length":"offset";r.attributes.byterange=r.attributes.byterange||{},r.attributes.byterange[t]=r.attributes[e],delete r.attributes[e]}}),this.trigger("data",r);return}if((t=/^#EXT-X-RENDITION-REPORT:(.*)$/.exec(e))&&t[1]){(r={type:"tag",tagType:"rendition-report"}).attributes=u(t[1]),["LAST-MSN","LAST-PART"].forEach(function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=parseInt(r.attributes[e],10))}),this.trigger("data",r);return}if((t=/^#EXT-X-DATERANGE:(.*)$/.exec(e))&&t[1]){(r={type:"tag",tagType:"daterange"}).attributes=u(t[1]),["ID","CLASS"].forEach(function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=String(r.attributes[e]))}),["START-DATE","END-DATE"].forEach(function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=new Date(r.attributes[e]))}),["DURATION","PLANNED-DURATION"].forEach(function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=parseFloat(r.attributes[e]))}),["END-ON-NEXT"].forEach(function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=/YES/i.test(r.attributes[e]))}),["SCTE35-CMD"," SCTE35-OUT","SCTE35-IN"].forEach(function(e){r.attributes.hasOwnProperty(e)&&(r.attributes[e]=r.attributes[e].toString(16))});let e=/^X-([A-Z]+-)+[A-Z]+$/;for(let t in r.attributes){if(!e.test(t))continue;let n=/[0-9A-Fa-f]{6}/g.test(r.attributes[t]),i=/^\d+(\.\d+)?$/.test(r.attributes[t]);r.attributes[t]=n?r.attributes[t].toString(16):i?parseFloat(r.attributes[t]):String(r.attributes[t])}this.trigger("data",r);return}if(t=/^#EXT-X-INDEPENDENT-SEGMENTS/.exec(e)){this.trigger("data",{type:"tag",tagType:"independent-segments"});return}if(t=/^#EXT-X-I-FRAMES-ONLY/.exec(e)){this.trigger("data",{type:"tag",tagType:"i-frames-only"});return}if(t=/^#EXT-X-CONTENT-STEERING:(.*)$/.exec(e)){(r={type:"tag",tagType:"content-steering"}).attributes=u(t[1]),this.trigger("data",r);return}if(t=/^#EXT-X-I-FRAME-STREAM-INF:(.*)$/.exec(e)){(r={type:"tag",tagType:"i-frame-playlist"}).attributes=u(t[1]),r.attributes.URI&&(r.uri=r.attributes.URI),r.attributes.BANDWIDTH&&(r.attributes.BANDWIDTH=parseInt(r.attributes.BANDWIDTH,10)),r.attributes.RESOLUTION&&(r.attributes.RESOLUTION=l(r.attributes.RESOLUTION)),r.attributes["AVERAGE-BANDWIDTH"]&&(r.attributes["AVERAGE-BANDWIDTH"]=parseInt(r.attributes["AVERAGE-BANDWIDTH"],10)),r.attributes["FRAME-RATE"]&&(r.attributes["FRAME-RATE"]=parseFloat(r.attributes["FRAME-RATE"])),this.trigger("data",r);return}if(t=/^#EXT-X-DEFINE:(.*)$/.exec(e)){(r={type:"tag",tagType:"define"}).attributes=u(t[1]),this.trigger("data",r);return}this.trigger("data",{type:"tag",data:e.slice(4)})})}}addParser({expression:e,customType:t,dataParser:r,segment:n}){"function"!=typeof r&&(r=e=>e),this.customParsers.push(i=>{if(e.exec(i))return this.trigger("data",{type:"custom",data:r(i),customType:t,segment:n}),!0})}addTagMapper({expression:e,map:t}){this.tagMappers.push(r=>e.test(r)?t(r):r)}}let f=e=>e.toLowerCase().replace(/-(\w)/g,e=>e[1].toUpperCase()),p=function(e){let t={};return Object.keys(e).forEach(function(r){t[f(r)]=e[r]}),t},h=function(e){let{serverControl:t,targetDuration:r,partTargetDuration:n}=e;if(!t)return;let i="#EXT-X-SERVER-CONTROL",a="holdBack",o="partHoldBack",s=r&&3*r,u=n&&2*n;r&&!t.hasOwnProperty(a)&&(t[a]=s,this.trigger("info",{message:`${i} defaulting HOLD-BACK to targetDuration * 3 (${s}).`})),s&&t[a]<s&&(this.trigger("warn",{message:`${i} clamping HOLD-BACK (${t[a]}) to targetDuration * 3 (${s})`}),t[a]=s),n&&!t.hasOwnProperty(o)&&(t[o]=3*n,this.trigger("info",{message:`${i} defaulting PART-HOLD-BACK to partTargetDuration * 3 (${t[o]}).`})),n&&t[o]<u&&(this.trigger("warn",{message:`${i} clamping PART-HOLD-BACK (${t[o]}) to partTargetDuration * 2 (${u}).`}),t[o]=u)};class d extends n{constructor(e={}){let t,r;super(),this.lineStream=new o,this.parseStream=new c,this.lineStream.pipe(this.parseStream),this.mainDefinitions=e.mainDefinitions||{},this.params=new URL(e.uri,"https://a.com").searchParams,this.lastProgramDateTime=null;let n=this,s=[],u={},l=!1,f=function(){},d={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},g=0;this.manifest={allowCache:!0,discontinuityStarts:[],dateRanges:[],iFramePlaylists:[],segments:[]};let m=0,b=0,y={};this.on("end",()=>{!u.uri&&(u.parts||u.preloadHints)&&(!u.map&&t&&(u.map=t),!u.key&&r&&(u.key=r),u.timeline||"number"!=typeof g||(u.timeline=g),this.manifest.preloadSegment=u)}),this.parseStream.on("data",function(e){let o,c;if(n.manifest.definitions){for(let t in n.manifest.definitions)if(e.uri&&(e.uri=e.uri.replace(`{$${t}}`,n.manifest.definitions[t])),e.attributes)for(let r in e.attributes)"string"==typeof e.attributes[r]&&(e.attributes[r]=e.attributes[r].replace(`{$${t}}`,n.manifest.definitions[t]))}({tag(){(({version(){e.version&&(this.manifest.version=e.version)},"allow-cache"(){this.manifest.allowCache=e.allowed,"allowed"in e||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange(){let t={};"length"in e&&(u.byterange=t,t.length=e.length,"offset"in e||(e.offset=m)),"offset"in e&&(u.byterange=t,t.offset=e.offset),m=t.offset+t.length},endlist(){this.manifest.endList=!0},inf(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),e.title&&(u.title=e.title),e.duration>0&&(u.duration=e.duration),0===e.duration&&(u.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=s},key(){if(!e.attributes){this.trigger("warn",{message:"ignoring key declaration without attribute list"});return}if("NONE"===e.attributes.METHOD){r=null;return}if(!e.attributes.URI){this.trigger("warn",{message:"ignoring key declaration without URI"});return}if("com.apple.streamingkeydelivery"===e.attributes.KEYFORMAT){this.manifest.contentProtection=this.manifest.contentProtection||{},this.manifest.contentProtection["com.apple.fps.1_0"]={attributes:e.attributes};return}if("com.microsoft.playready"===e.attributes.KEYFORMAT){this.manifest.contentProtection=this.manifest.contentProtection||{},this.manifest.contentProtection["com.microsoft.playready"]={uri:e.attributes.URI};return}if("urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"===e.attributes.KEYFORMAT){if(-1===["SAMPLE-AES","SAMPLE-AES-CTR","SAMPLE-AES-CENC"].indexOf(e.attributes.METHOD)){this.trigger("warn",{message:"invalid key method provided for Widevine"});return}if("SAMPLE-AES-CENC"===e.attributes.METHOD&&this.trigger("warn",{message:"SAMPLE-AES-CENC is deprecated, please use SAMPLE-AES-CTR instead"}),"data:text/plain;base64,"!==e.attributes.URI.substring(0,23)){this.trigger("warn",{message:"invalid key URI provided for Widevine"});return}if(!(e.attributes.KEYID&&"0x"===e.attributes.KEYID.substring(0,2))){this.trigger("warn",{message:"invalid key ID provided for Widevine"});return}this.manifest.contentProtection=this.manifest.contentProtection||{},this.manifest.contentProtection["com.widevine.alpha"]={attributes:{schemeIdUri:e.attributes.KEYFORMAT,keyId:e.attributes.KEYID.substring(2)},pssh:(0,a.A)(e.attributes.URI.split(",")[1])};return}e.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),r={method:e.attributes.METHOD||"AES-128",uri:e.attributes.URI},void 0!==e.attributes.IV&&(r.iv=e.attributes.IV)},"media-sequence"(){if(!isFinite(e.number)){this.trigger("warn",{message:"ignoring invalid media sequence: "+e.number});return}this.manifest.mediaSequence=e.number},"discontinuity-sequence"(){if(!isFinite(e.number)){this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+e.number});return}this.manifest.discontinuitySequence=e.number,g=e.number},"playlist-type"(){if(!/VOD|EVENT/.test(e.playlistType)){this.trigger("warn",{message:"ignoring unknown playlist type: "+e.playlist});return}this.manifest.playlistType=e.playlistType},map(){t={},e.uri&&(t.uri=e.uri),e.byterange&&(t.byterange=e.byterange),r&&(t.key=r)},"stream-inf"(){if(this.manifest.playlists=s,this.manifest.mediaGroups=this.manifest.mediaGroups||d,!e.attributes){this.trigger("warn",{message:"ignoring empty stream-inf attributes"});return}u.attributes||(u.attributes={}),(0,i.A)(u.attributes,e.attributes)},media(){if(this.manifest.mediaGroups=this.manifest.mediaGroups||d,!(e.attributes&&e.attributes.TYPE&&e.attributes["GROUP-ID"]&&e.attributes.NAME)){this.trigger("warn",{message:"ignoring incomplete or missing media group"});return}let t=this.manifest.mediaGroups[e.attributes.TYPE];t[e.attributes["GROUP-ID"]]=t[e.attributes["GROUP-ID"]]||{},o=t[e.attributes["GROUP-ID"]],(c={default:/yes/i.test(e.attributes.DEFAULT)}).default?c.autoselect=!0:c.autoselect=/yes/i.test(e.attributes.AUTOSELECT),e.attributes.LANGUAGE&&(c.language=e.attributes.LANGUAGE),e.attributes.URI&&(c.uri=e.attributes.URI),e.attributes["INSTREAM-ID"]&&(c.instreamId=e.attributes["INSTREAM-ID"]),e.attributes.CHARACTERISTICS&&(c.characteristics=e.attributes.CHARACTERISTICS),e.attributes.FORCED&&(c.forced=/yes/i.test(e.attributes.FORCED)),o[e.attributes.NAME]=c},discontinuity(){g+=1,u.discontinuity=!0,this.manifest.discontinuityStarts.push(s.length)},"program-date-time"(){void 0===this.manifest.dateTimeString&&(this.manifest.dateTimeString=e.dateTimeString,this.manifest.dateTimeObject=e.dateTimeObject),u.dateTimeString=e.dateTimeString,u.dateTimeObject=e.dateTimeObject;let{lastProgramDateTime:t}=this;this.lastProgramDateTime=new Date(e.dateTimeString).getTime(),null===t&&this.manifest.segments.reduceRight((e,t)=>(t.programDateTime=e-1e3*t.duration,t.programDateTime),this.lastProgramDateTime)},targetduration(){if(!isFinite(e.duration)||e.duration<0){this.trigger("warn",{message:"ignoring invalid target duration: "+e.duration});return}this.manifest.targetDuration=e.duration,h.call(this,this.manifest)},start(){if(!e.attributes||isNaN(e.attributes["TIME-OFFSET"])){this.trigger("warn",{message:"ignoring start declaration without appropriate attribute list"});return}this.manifest.start={timeOffset:e.attributes["TIME-OFFSET"],precise:e.attributes.PRECISE}},"cue-out"(){u.cueOut=e.data},"cue-out-cont"(){u.cueOutCont=e.data},"cue-in"(){u.cueIn=e.data},skip(){this.manifest.skip=p(e.attributes),this.warnOnMissingAttributes_("#EXT-X-SKIP",e.attributes,["SKIPPED-SEGMENTS"])},part(){l=!0;let t=this.manifest.segments.length,r=p(e.attributes);u.parts=u.parts||[],u.parts.push(r),r.byterange&&(r.byterange.hasOwnProperty("offset")||(r.byterange.offset=b),b=r.byterange.offset+r.byterange.length);let n=u.parts.length-1;this.warnOnMissingAttributes_(`#EXT-X-PART #${n} for segment #${t}`,e.attributes,["URI","DURATION"]),this.manifest.renditionReports&&this.manifest.renditionReports.forEach((e,t)=>{e.hasOwnProperty("lastPart")||this.trigger("warn",{message:`#EXT-X-RENDITION-REPORT #${t} lacks required attribute(s): LAST-PART`})})},"server-control"(){let t=this.manifest.serverControl=p(e.attributes);t.hasOwnProperty("canBlockReload")||(t.canBlockReload=!1,this.trigger("info",{message:"#EXT-X-SERVER-CONTROL defaulting CAN-BLOCK-RELOAD to false"})),h.call(this,this.manifest),t.canSkipDateranges&&!t.hasOwnProperty("canSkipUntil")&&this.trigger("warn",{message:"#EXT-X-SERVER-CONTROL lacks required attribute CAN-SKIP-UNTIL which is required when CAN-SKIP-DATERANGES is set"})},"preload-hint"(){let t=this.manifest.segments.length,r=p(e.attributes),n=r.type&&"PART"===r.type;u.preloadHints=u.preloadHints||[],u.preloadHints.push(r),r.byterange&&!r.byterange.hasOwnProperty("offset")&&(r.byterange.offset=n?b:0,n&&(b=r.byterange.offset+r.byterange.length));let i=u.preloadHints.length-1;if(this.warnOnMissingAttributes_(`#EXT-X-PRELOAD-HINT #${i} for segment #${t}`,e.attributes,["TYPE","URI"]),r.type)for(let e=0;e<u.preloadHints.length-1;e++){let n=u.preloadHints[e];n.type&&n.type===r.type&&this.trigger("warn",{message:`#EXT-X-PRELOAD-HINT #${i} for segment #${t} has the same TYPE ${r.type} as preload hint #${e}`})}},"rendition-report"(){let t=p(e.attributes);this.manifest.renditionReports=this.manifest.renditionReports||[],this.manifest.renditionReports.push(t);let r=this.manifest.renditionReports.length-1,n=["LAST-MSN","URI"];l&&n.push("LAST-PART"),this.warnOnMissingAttributes_(`#EXT-X-RENDITION-REPORT #${r}`,e.attributes,n)},"part-inf"(){this.manifest.partInf=p(e.attributes),this.warnOnMissingAttributes_("#EXT-X-PART-INF",e.attributes,["PART-TARGET"]),this.manifest.partInf.partTarget&&(this.manifest.partTargetDuration=this.manifest.partInf.partTarget),h.call(this,this.manifest)},daterange(){this.manifest.dateRanges.push(p(e.attributes));let t=this.manifest.dateRanges.length-1;this.warnOnMissingAttributes_(`#EXT-X-DATERANGE #${t}`,e.attributes,["ID","START-DATE"]);let r=this.manifest.dateRanges[t];r.endDate&&r.startDate&&new Date(r.endDate)<new Date(r.startDate)&&this.trigger("warn",{message:"EXT-X-DATERANGE END-DATE must be equal to or later than the value of the START-DATE"}),r.duration&&r.duration<0&&this.trigger("warn",{message:"EXT-X-DATERANGE DURATION must not be negative"}),r.plannedDuration&&r.plannedDuration<0&&this.trigger("warn",{message:"EXT-X-DATERANGE PLANNED-DURATION must not be negative"});let n=!!r.endOnNext;if(n&&!r.class&&this.trigger("warn",{message:"EXT-X-DATERANGE with an END-ON-NEXT=YES attribute must have a CLASS attribute"}),n&&(r.duration||r.endDate)&&this.trigger("warn",{message:"EXT-X-DATERANGE with an END-ON-NEXT=YES attribute must not contain DURATION or END-DATE attributes"}),r.duration&&r.endDate){let e=r.startDate.getTime()+1e3*r.duration;this.manifest.dateRanges[t].endDate=new Date(e)}if(y[r.id]){for(let e in y[r.id])if(r[e]&&JSON.stringify(y[r.id][e])!==JSON.stringify(r[e])){this.trigger("warn",{message:"EXT-X-DATERANGE tags with the same ID in a playlist must have the same attributes values"});break}let e=this.manifest.dateRanges.findIndex(e=>e.id===r.id);this.manifest.dateRanges[e]=(0,i.A)(this.manifest.dateRanges[e],r),y[r.id]=(0,i.A)(y[r.id],r),this.manifest.dateRanges.pop()}else y[r.id]=r},"independent-segments"(){this.manifest.independentSegments=!0},"i-frames-only"(){this.manifest.iFramesOnly=!0,this.requiredCompatibilityversion(this.manifest.version,4)},"content-steering"(){this.manifest.contentSteering=p(e.attributes),this.warnOnMissingAttributes_("#EXT-X-CONTENT-STEERING",e.attributes,["SERVER-URI"])},define(){this.manifest.definitions=this.manifest.definitions||{};let t=(e,t)=>{if(e in this.manifest.definitions){this.trigger("error",{message:`EXT-X-DEFINE: Duplicate name ${e}`});return}this.manifest.definitions[e]=t};if("QUERYPARAM"in e.attributes){if("NAME"in e.attributes||"IMPORT"in e.attributes){this.trigger("error",{message:"EXT-X-DEFINE: Invalid attributes"});return}let r=this.params.get(e.attributes.QUERYPARAM);if(!r){this.trigger("error",{message:`EXT-X-DEFINE: No query param ${e.attributes.QUERYPARAM}`});return}t(e.attributes.QUERYPARAM,decodeURIComponent(r));return}if("NAME"in e.attributes){if("IMPORT"in e.attributes){this.trigger("error",{message:"EXT-X-DEFINE: Invalid attributes"});return}if(!("VALUE"in e.attributes)||"string"!=typeof e.attributes.VALUE){this.trigger("error",{message:`EXT-X-DEFINE: No value for ${e.attributes.NAME}`});return}t(e.attributes.NAME,e.attributes.VALUE);return}if("IMPORT"in e.attributes){if(!this.mainDefinitions[e.attributes.IMPORT]){this.trigger("error",{message:`EXT-X-DEFINE: No value ${e.attributes.IMPORT} to import, or IMPORT used on main playlist`});return}t(e.attributes.IMPORT,this.mainDefinitions[e.attributes.IMPORT]);return}this.trigger("error",{message:"EXT-X-DEFINE: No attribute"})},"i-frame-playlist"(){this.manifest.iFramePlaylists.push({attributes:e.attributes,uri:e.uri,timeline:g}),this.warnOnMissingAttributes_("#EXT-X-I-FRAME-STREAM-INF",e.attributes,["BANDWIDTH","URI"])}})[e.tagType]||f).call(n)},uri(){u.uri=e.uri,s.push(u),!this.manifest.targetDuration||"duration"in u||(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),u.duration=this.manifest.targetDuration),r&&(u.key=r),u.timeline=g,t&&(u.map=t),b=0,null!==this.lastProgramDateTime&&(u.programDateTime=this.lastProgramDateTime,this.lastProgramDateTime+=1e3*u.duration),u={}},comment(){},custom(){e.segment?(u.custom=u.custom||{},u.custom[e.customType]=e.data):(this.manifest.custom=this.manifest.custom||{},this.manifest.custom[e.customType]=e.data)}})[e.type].call(n)})}requiredCompatibilityversion(e,t){(e<t||!e)&&this.trigger("warn",{message:`manifest must be at least version ${t}`})}warnOnMissingAttributes_(e,t,r){let n=[];r.forEach(function(e){t.hasOwnProperty(e)||n.push(e)}),n.length&&this.trigger("warn",{message:`${e} lacks required attribute(s): ${n.join(", ")}`})}push(e){this.lineStream.push(e)}end(){this.lineStream.push("\n"),this.manifest.dateRanges.length&&null===this.lastProgramDateTime&&this.trigger("warn",{message:"A playlist with EXT-X-DATERANGE tag must contain atleast one EXT-X-PROGRAM-DATE-TIME tag"}),this.lastProgramDateTime=null,this.trigger("end")}addParser(e){this.parseStream.addParser(e)}addTagMapper(e){this.parseStream.addTagMapper(e)}}},8962:(e,t,r)=>{var n=r(7925),i=r(770),a=r(5473),o=r(6302),s=i.DOMImplementation,u=n.NAMESPACE,l=o.ParseError,c=o.XMLReader;function f(e){return e.replace(/\r[\n\u0085]/g,"\n").replace(/[\r\u0085\u2028]/g,"\n")}function p(e){this.options=e||{locator:{}}}function h(){this.cdata=!1}function d(e,t){t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber}function g(e){if(e)return"\n@"+(e.systemId||"")+"#[line:"+e.lineNumber+",col:"+e.columnNumber+"]"}function m(e,t,r){return"string"==typeof e?e.substr(t,r):e.length>=t+r||t?new java.lang.String(e,t,r)+"":e}function b(e,t){e.currentElement?e.currentElement.appendChild(t):e.doc.appendChild(t)}p.prototype.parseFromString=function(e,t){var r=this.options,n=new c,i=r.domBuilder||new h,o=r.errorHandler,s=r.locator,l=r.xmlns||{},p=/\/x?html?$/.test(t),d=p?a.HTML_ENTITIES:a.XML_ENTITIES;s&&i.setDocumentLocator(s),n.errorHandler=function(e,t,r){if(!e){if(t instanceof h)return t;e=t}var n={},i=e instanceof Function;function a(t){var a=e[t];!a&&i&&(a=2==e.length?function(r){e(t,r)}:e),n[t]=a&&function(e){a("[xmldom "+t+"]	"+e+g(r))}||function(){}}return r=r||{},a("warning"),a("error"),a("fatalError"),n}(o,i,s),n.domBuilder=r.domBuilder||i,p&&(l[""]=u.HTML),l.xml=l.xml||u.XML;var m=r.normalizeLineEndings||f;return e&&"string"==typeof e?n.parse(m(e),l,d):n.errorHandler.error("invalid doc source"),i.doc},h.prototype={startDocument:function(){this.doc=new s().createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(e,t,r,n){var i=this.doc,a=i.createElementNS(e,r||t),o=n.length;b(this,a),this.currentElement=a,this.locator&&d(this.locator,a);for(var s=0;s<o;s++){var e=n.getURI(s),u=n.getValue(s),r=n.getQName(s),l=i.createAttributeNS(e,r);this.locator&&d(n.getLocator(s),l),l.value=l.nodeValue=u,a.setAttributeNode(l)}},endElement:function(e,t,r){var n=this.currentElement;n.tagName,this.currentElement=n.parentNode},startPrefixMapping:function(e,t){},endPrefixMapping:function(e){},processingInstruction:function(e,t){var r=this.doc.createProcessingInstruction(e,t);this.locator&&d(this.locator,r),b(this,r)},ignorableWhitespace:function(e,t,r){},characters:function(e,t,r){if(e=m.apply(this,arguments)){if(this.cdata)var n=this.doc.createCDATASection(e);else var n=this.doc.createTextNode(e);this.currentElement?this.currentElement.appendChild(n):/^\s*$/.test(e)&&this.doc.appendChild(n),this.locator&&d(this.locator,n)}},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){(this.locator=e)&&(e.lineNumber=0)},comment:function(e,t,r){e=m.apply(this,arguments);var n=this.doc.createComment(e);this.locator&&d(this.locator,n),b(this,n)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,t,r){var n=this.doc.implementation;if(n&&n.createDocumentType){var i=n.createDocumentType(e,t,r);this.locator&&d(this.locator,i),b(this,i),this.doc.doctype=i}},warning:function(e){console.warn("[xmldom warning]	"+e,g(this.locator))},error:function(e){console.error("[xmldom error]	"+e,g(this.locator))},fatalError:function(e){throw new l(e,this.locator)}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,function(e){h.prototype[e]=function(){return null}}),t.DOMParser=p},9410:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(8202),i=r.n(n),a=r(5714).Buffer;function o(e){for(var t=i().atob?i().atob(e):a.from(e,"base64").toString("binary"),r=new Uint8Array(t.length),n=0;n<t.length;n++)r[n]=t.charCodeAt(n);return r}},9484:(e,t,r)=>{"use strict";r.d(t,{I:()=>a});var n=/^(audio|video|application)\/(x-|vnd\.apple\.)?mpegurl/i,i=/^application\/dash\+xml/i,a=function(e){return n.test(e)?"hls":i.test(e)?"dash":"application/vnd.videojs.vhs+json"===e?"vhs-json":null}},9512:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(8202),i=r.n(n),a="https://example.com";let o=function(e,t){if(/^[a-z]+:/i.test(t))return t;/^data:/.test(e)&&(e=i().location&&i().location.href||"");var r=/^\/\//.test(e),n=!i().location&&!/\/\//i.test(e),o=new URL(t,e=new(i()).URL(e,i().location||a));return n?o.href.slice(a.length):r?o.href.slice(o.protocol.length):o.href}}}]);