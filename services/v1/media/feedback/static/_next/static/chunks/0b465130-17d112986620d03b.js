"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[776],{4787:(t,e,i)=>{let r,s,n;function o(t,e,i){var r;return(e="symbol"==typeof(r=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"))?r:r+"")in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function a(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)}return i}function l(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?a(Object(i),!0).forEach(function(e){o(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):a(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function h(t,e){if(null==t)return{};var i,r,s=function(t,e){if(null==t)return{};var i={};for(var r in t)if(({}).hasOwnProperty.call(t,r)){if(e.indexOf(r)>=0)continue;i[r]=t[r]}return i}(t,e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);for(r=0;r<n.length;r++)i=n[r],e.indexOf(i)>=0||({}).propertyIsEnumerable.call(t,i)&&(s[i]=t[i])}return s}function c(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}i.d(e,{Hl:()=>sw,mW:()=>sH});class u{constructor(){o(this,"browserShadowBlurConstant",1),o(this,"DPI",96),o(this,"devicePixelRatio","undefined"!=typeof window?window.devicePixelRatio:1),o(this,"perfLimitSizeTotal",2097152),o(this,"maxCacheSideLimit",4096),o(this,"minCacheSideLimit",256),o(this,"disableStyleCopyPaste",!1),o(this,"enableGLFiltering",!0),o(this,"textureSize",4096),o(this,"forceGLPutImageData",!1),o(this,"cachesBoundsOfCurve",!1),o(this,"fontPaths",{}),o(this,"NUM_FRACTION_DIGITS",4)}}let d=new class extends u{constructor(t){super(),this.configure(t)}configure(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Object.assign(this,t)}addFonts(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.fontPaths=l(l({},this.fontPaths),t)}removeFonts(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach(t=>{delete this.fontPaths[t]})}clearFonts(){this.fontPaths={}}restoreDefaults(t){let e=new u,i=(null==t?void 0:t.reduce((t,i)=>(t[i]=e[i],t),{}))||e;this.configure(i)}},g=function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return console[t]("fabric",...i)};class f extends Error{constructor(t,e){super("fabric: ".concat(t),e)}}class p extends f{constructor(t){super("".concat(t," 'options.signal' is in 'aborted' state"))}}class m{}class v extends m{testPrecision(t,e){let i=t.createShader(t.FRAGMENT_SHADER);return!!i&&(t.shaderSource(i,"precision ".concat(e," float;\nvoid main(){}")),t.compileShader(i),!!t.getShaderParameter(i,t.COMPILE_STATUS))}queryWebGL(t){let e=t.getContext("webgl");e&&(this.maxTextureSize=e.getParameter(e.MAX_TEXTURE_SIZE),this.GLPrecision=["highp","mediump","lowp"].find(t=>this.testPrecision(e,t)),e.getExtension("WEBGL_lose_context").loseContext(),g("log","WebGL: max texture size ".concat(this.maxTextureSize)))}isSupported(t){return!!this.maxTextureSize&&this.maxTextureSize>=t}}let _={},y=()=>r||(r={document:document,window:window,isTouchSupported:"ontouchstart"in window||"ontouchstart"in document||window&&window.navigator&&window.navigator.maxTouchPoints>0,WebGLProbe:new v,dispose(){},copyPasteData:_}),x=()=>y().document,C=()=>y().window,b=()=>{var t;return Math.max(null!==(t=d.devicePixelRatio)&&void 0!==t?t:C().devicePixelRatio,1)},S=new class{constructor(){o(this,"charWidthsCache",{}),o(this,"boundsOfCurveCache",{})}getFontCache(t){let{fontFamily:e,fontStyle:i,fontWeight:r}=t;e=e.toLowerCase(),this.charWidthsCache[e]||(this.charWidthsCache[e]={});let s=this.charWidthsCache[e],n="".concat(i.toLowerCase(),"_").concat((r+"").toLowerCase());return s[n]||(s[n]={}),s[n]}clearFontCache(t){(t=(t||"").toLowerCase())?this.charWidthsCache[t]&&delete this.charWidthsCache[t]:this.charWidthsCache={}}limitDimsByArea(t){let{perfLimitSizeTotal:e}=d,i=Math.sqrt(e*t);return[Math.floor(i),Math.floor(e/i)]}},w="6.6.1";function T(){}let O=Math.PI/2,D=2*Math.PI,k=Math.PI/180,M=Object.freeze([1,0,0,1,0,0]),E="center",P="left",A="bottom",j="right",F="none",L=/\r?\n/,R="moving",I="scaling",B="rotating",X="rotate",Y="skewing",W="resizing",V="modifyPoly",H="changed",z="scale",G="scaleX",N="scaleY",U="skewX",q="skewY",K="fill",J="stroke",Q="modified",Z="json",$=new class{constructor(){this[Z]=new Map,this.svg=new Map}has(t){return this[Z].has(t)}getClass(t){let e=this[Z].get(t);if(!e)throw new f("No class registered for ".concat(t));return e}setClass(t,e){e?this[Z].set(e,t):(this[Z].set(t.type,t),this[Z].set(t.type.toLowerCase(),t))}getSVGClass(t){return this.svg.get(t)}setSVGClass(t,e){this.svg.set(null!=e?e:t.type.toLowerCase(),t)}},tt=new class extends Array{remove(t){let e=this.indexOf(t);e>-1&&this.splice(e,1)}cancelAll(){let t=this.splice(0);return t.forEach(t=>t.abort()),t}cancelByCanvas(t){if(!t)return[];let e=this.filter(e=>{var i;return e.target===t||"object"==typeof e.target&&(null===(i=e.target)||void 0===i?void 0:i.canvas)===t});return e.forEach(t=>t.abort()),e}cancelByTarget(t){if(!t)return[];let e=this.filter(e=>e.target===t);return e.forEach(t=>t.abort()),e}};class te{constructor(){o(this,"__eventListeners",{})}on(t,e){return(this.__eventListeners||(this.__eventListeners={}),"object"==typeof t)?(Object.entries(t).forEach(t=>{let[e,i]=t;this.on(e,i)}),()=>this.off(t)):e?(this.__eventListeners[t]||(this.__eventListeners[t]=[]),this.__eventListeners[t].push(e),()=>this.off(t,e)):()=>!1}once(t,e){if("object"==typeof t){let e=[];return Object.entries(t).forEach(t=>{let[i,r]=t;e.push(this.once(i,r))}),()=>e.forEach(t=>t())}if(e){let i=this.on(t,function(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];e.call(this,...r),i()});return i}return()=>!1}_removeEventListener(t,e){if(this.__eventListeners[t]){if(e){let i=this.__eventListeners[t],r=i.indexOf(e);r>-1&&i.splice(r,1)}else this.__eventListeners[t]=[]}}off(t,e){if(this.__eventListeners){if(void 0===t)for(let t in this.__eventListeners)this._removeEventListener(t);else"object"==typeof t?Object.entries(t).forEach(t=>{let[e,i]=t;this._removeEventListener(e,i)}):this._removeEventListener(t,e)}}fire(t,e){var i;if(!this.__eventListeners)return;let r=null===(i=this.__eventListeners[t])||void 0===i?void 0:i.concat();if(r)for(let t=0;t<r.length;t++)r[t].call(this,e||{})}}let ti=(t,e)=>{let i=t.indexOf(e);return -1!==i&&t.splice(i,1),t},tr=t=>{if(0===t)return 1;switch(Math.abs(t)/O){case 1:case 3:return 0;case 2:return -1}return Math.cos(t)},ts=t=>{if(0===t)return 0;let e=Math.sign(t);switch(t/O){case 1:return e;case 2:return 0;case 3:return-e}return Math.sin(t)};class tn{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;"object"==typeof t?(this.x=t.x,this.y=t.y):(this.x=t,this.y=e)}add(t){return new tn(this.x+t.x,this.y+t.y)}addEquals(t){return this.x+=t.x,this.y+=t.y,this}scalarAdd(t){return new tn(this.x+t,this.y+t)}scalarAddEquals(t){return this.x+=t,this.y+=t,this}subtract(t){return new tn(this.x-t.x,this.y-t.y)}subtractEquals(t){return this.x-=t.x,this.y-=t.y,this}scalarSubtract(t){return new tn(this.x-t,this.y-t)}scalarSubtractEquals(t){return this.x-=t,this.y-=t,this}multiply(t){return new tn(this.x*t.x,this.y*t.y)}scalarMultiply(t){return new tn(this.x*t,this.y*t)}scalarMultiplyEquals(t){return this.x*=t,this.y*=t,this}divide(t){return new tn(this.x/t.x,this.y/t.y)}scalarDivide(t){return new tn(this.x/t,this.y/t)}scalarDivideEquals(t){return this.x/=t,this.y/=t,this}eq(t){return this.x===t.x&&this.y===t.y}lt(t){return this.x<t.x&&this.y<t.y}lte(t){return this.x<=t.x&&this.y<=t.y}gt(t){return this.x>t.x&&this.y>t.y}gte(t){return this.x>=t.x&&this.y>=t.y}lerp(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.5;return e=Math.max(Math.min(1,e),0),new tn(this.x+(t.x-this.x)*e,this.y+(t.y-this.y)*e)}distanceFrom(t){let e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)}midPointFrom(t){return this.lerp(t)}min(t){return new tn(Math.min(this.x,t.x),Math.min(this.y,t.y))}max(t){return new tn(Math.max(this.x,t.x),Math.max(this.y,t.y))}toString(){return"".concat(this.x,",").concat(this.y)}setXY(t,e){return this.x=t,this.y=e,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setFromPoint(t){return this.x=t.x,this.y=t.y,this}swap(t){let e=this.x,i=this.y;this.x=t.x,this.y=t.y,t.x=e,t.y=i}clone(){return new tn(this.x,this.y)}rotate(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:to,i=ts(t),r=tr(t),s=this.subtract(e);return new tn(s.x*r-s.y*i,s.x*i+s.y*r).add(e)}transform(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new tn(t[0]*this.x+t[2]*this.y+(e?0:t[4]),t[1]*this.x+t[3]*this.y+(e?0:t[5]))}}let to=new tn(0,0),ta=t=>!!t&&Array.isArray(t._objects);function tl(t){class e extends t{constructor(){super(...arguments),o(this,"_objects",[])}_onObjectAdded(t){}_onObjectRemoved(t){}_onStackOrderChanged(t){}add(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];let r=this._objects.push(...e);return e.forEach(t=>this._onObjectAdded(t)),r}insertAt(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return this._objects.splice(t,0,...i),i.forEach(t=>this._onObjectAdded(t)),this._objects.length}remove(){let t=this._objects,e=[];for(var i=arguments.length,r=Array(i),s=0;s<i;s++)r[s]=arguments[s];return r.forEach(i=>{let r=t.indexOf(i);-1!==r&&(t.splice(r,1),e.push(i),this._onObjectRemoved(i))}),e}forEachObject(t){this.getObjects().forEach((e,i,r)=>t(e,i,r))}getObjects(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return 0===e.length?[...this._objects]:this._objects.filter(t=>t.isType(...e))}item(t){return this._objects[t]}isEmpty(){return 0===this._objects.length}size(){return this._objects.length}contains(t,i){return!!this._objects.includes(t)||!!i&&this._objects.some(i=>i instanceof e&&i.contains(t,!0))}complexity(){return this._objects.reduce((t,e)=>t+=e.complexity?e.complexity():0,0)}sendObjectToBack(t){return!(!t||t===this._objects[0])&&(ti(this._objects,t),this._objects.unshift(t),this._onStackOrderChanged(t),!0)}bringObjectToFront(t){return!(!t||t===this._objects[this._objects.length-1])&&(ti(this._objects,t),this._objects.push(t),this._onStackOrderChanged(t),!0)}sendObjectBackwards(t,e){if(!t)return!1;let i=this._objects.indexOf(t);if(0!==i){let r=this.findNewLowerIndex(t,i,e);return ti(this._objects,t),this._objects.splice(r,0,t),this._onStackOrderChanged(t),!0}return!1}bringObjectForward(t,e){if(!t)return!1;let i=this._objects.indexOf(t);if(i!==this._objects.length-1){let r=this.findNewUpperIndex(t,i,e);return ti(this._objects,t),this._objects.splice(r,0,t),this._onStackOrderChanged(t),!0}return!1}moveObjectTo(t,e){return t!==this._objects[e]&&(ti(this._objects,t),this._objects.splice(e,0,t),this._onStackOrderChanged(t),!0)}findNewLowerIndex(t,e,i){let r;if(i){r=e;for(let i=e-1;i>=0;--i)if(t.isOverlapping(this._objects[i])){r=i;break}}else r=e-1;return r}findNewUpperIndex(t,e,i){let r;if(i){r=e;for(let i=e+1;i<this._objects.length;++i)if(t.isOverlapping(this._objects[i])){r=i;break}}else r=e+1;return r}collectObjects(t){let{left:e,top:i,width:r,height:s}=t,{includeIntersecting:n=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=[],a=new tn(e,i),l=a.add(new tn(r,s));for(let t=this._objects.length-1;t>=0;t--){let e=this._objects[t];e.selectable&&e.visible&&(n&&e.intersectsWithRect(a,l)||e.isContainedWithinRect(a,l)||n&&e.containsPoint(a)||n&&e.containsPoint(l))&&o.push(e)}return o}}return e}class th extends te{_setOptions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};for(let e in t)this.set(e,t[e])}_setObject(t){for(let e in t)this._set(e,t[e])}set(t,e){return"object"==typeof t?this._setObject(t):this._set(t,e),this}_set(t,e){this[t]=e}toggle(t){let e=this.get(t);return"boolean"==typeof e&&this.set(t,!e),this}get(t){return this[t]}}function tc(t){return C().requestAnimationFrame(t)}function tu(t){return C().cancelAnimationFrame(t)}let td=0,tg=()=>td++,tf=()=>{let t=x().createElement("canvas");if(!t||void 0===t.getContext)throw new f("Failed to create `canvas` element");return t},tp=()=>x().createElement("img"),tm=t=>{let e=tf();return e.width=t.width,e.height=t.height,e},tv=(t,e,i)=>t.toDataURL("image/".concat(e),i),t_=(t,e,i)=>new Promise((r,s)=>{t.toBlob(r,"image/".concat(e),i)}),ty=t=>t*k,tx=t=>t/k,tC=t=>t.every((t,e)=>t===M[e]),tb=(t,e,i)=>new tn(t).transform(e,i),tS=t=>{let e=1/(t[0]*t[3]-t[1]*t[2]),i=[e*t[3],-e*t[1],-e*t[2],e*t[0],0,0],{x:r,y:s}=new tn(t[4],t[5]).transform(i,!0);return i[4]=-r,i[5]=-s,i},tw=(t,e,i)=>[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],i?0:t[0]*e[4]+t[2]*e[5]+t[4],i?0:t[1]*e[4]+t[3]*e[5]+t[5]],tT=(t,e)=>t.reduceRight((t,i)=>i&&t?tw(i,t,e):i||t,void 0)||M.concat(),tO=t=>{let[e,i]=t;return Math.atan2(i,e)},tD=t=>{let e=tO(t),i=Math.pow(t[0],2)+Math.pow(t[1],2),r=Math.sqrt(i),s=(t[0]*t[3]-t[2]*t[1])/r,n=Math.atan2(t[0]*t[2]+t[1]*t[3],i);return{angle:tx(e),scaleX:r,scaleY:s,skewX:tx(n),skewY:0,translateX:t[4]||0,translateY:t[5]||0}},tk=function(t){return[1,0,0,1,t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0]};function tM(){let{angle:t=0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{x:e=0,y:i=0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=ty(t),s=tr(r),n=ts(r);return[s,n,-n,s,e?e-(s*e-n*i):0,i?i-(n*e+s*i):0]}let tE=function(t){return[t,0,0,arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,0,0]},tP=t=>Math.tan(ty(t)),tA=t=>[1,0,tP(t),1,0,0],tj=t=>[1,tP(t),0,1,0,0],tF=t=>{let{scaleX:e=1,scaleY:i=1,flipX:r=!1,flipY:s=!1,skewX:n=0,skewY:o=0}=t,a=tE(r?-e:e,s?-i:i);return n&&(a=tw(a,tA(n),!0)),o&&(a=tw(a,tj(o),!0)),a},tL=t=>{let{translateX:e=0,translateY:i=0,angle:r=0}=t,s=tk(e,i);r&&(s=tw(s,tM({angle:r})));let n=tF(t);return tC(n)||(s=tw(s,n)),s},tR=function(t){let{signal:e,crossOrigin:i=null}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise(function(r,s){let n;if(e&&e.aborted)return s(new p("loadImage"));let o=tp();e&&(n=function(t){o.src="",s(t)},e.addEventListener("abort",n,{once:!0}));let a=function(){o.onload=o.onerror=null,n&&(null==e||e.removeEventListener("abort",n)),r(o)};t?(o.onload=a,o.onerror=function(){n&&(null==e||e.removeEventListener("abort",n)),s(new f("Error loading ".concat(o.src)))},i&&(o.crossOrigin=i),o.src=t):a()})},tI=function(t){let{signal:e,reviver:i=T}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((r,s)=>{let n=[];e&&e.addEventListener("abort",s,{once:!0}),Promise.all(t.map(t=>$.getClass(t.type).fromObject(t,{signal:e}).then(e=>(i(t,e),n.push(e),e)))).then(r).catch(t=>{n.forEach(t=>{t.dispose&&t.dispose()}),s(t)}).finally(()=>{e&&e.removeEventListener("abort",s)})})},tB=function(t){let{signal:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((i,r)=>{let s=[];e&&e.addEventListener("abort",r,{once:!0});let n=Object.values(t).map(t=>t&&t.type&&$.has(t.type)?tI([t],{signal:e}).then(t=>{let[e]=t;return s.push(e),e}):t),o=Object.keys(t);Promise.all(n).then(t=>t.reduce((t,e,i)=>(t[o[i]]=e,t),{})).then(i).catch(t=>{s.forEach(t=>{t.dispose&&t.dispose()}),r(t)}).finally(()=>{e&&e.removeEventListener("abort",r)})})},tX=function(t){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:[]).reduce((e,i)=>(i in t&&(e[i]=t[i]),e),{})},tY=(t,e)=>Object.keys(t).reduce((i,r)=>(e(t[r],r,t)&&(i[r]=t[r]),i),{}),tW={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#0FF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000",blanchedalmond:"#FFEBCD",blue:"#00F",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#0FF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",darkgreen:"#006400",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#F0F",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",lightgreen:"#90EE90",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#789",lightslategrey:"#789",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#0F0",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#F0F",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#639",red:"#F00",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",white:"#FFF",whitesmoke:"#F5F5F5",yellow:"#FF0",yellowgreen:"#9ACD32"},tV=(t,e,i)=>(i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t),tH=(t,e,i,r)=>{let s,n;t/=255;let o=Math.max(t,e/=255,i/=255),a=Math.min(t,e,i),l=(o+a)/2;if(o===a)s=n=0;else{let r=o-a;switch(n=l>.5?r/(2-o-a):r/(o+a),o){case t:s=(e-i)/r+6*(e<i);break;case e:s=(i-t)/r+2;break;case i:s=(t-e)/r+4}s/=6}return[Math.round(360*s),Math.round(100*n),Math.round(100*l),r]},tz=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"1";return parseFloat(t)/(t.endsWith("%")?100:1)},tG=t=>Math.min(Math.round(t),255).toString(16).toUpperCase().padStart(2,"0"),tN=t=>{let[e,i,r,s=1]=t,n=Math.round(.3*e+.59*i+.11*r);return[n,n,n,s]};class tU{constructor(t){if(o(this,"isUnrecognised",!1),t){if(t instanceof tU)this.setSource([...t._source]);else if(Array.isArray(t)){let[e,i,r,s=1]=t;this.setSource([e,i,r,s])}else this.setSource(this._tryParsingColor(t))}else this.setSource([0,0,0,1])}_tryParsingColor(t){return(t=t.toLowerCase())in tW&&(t=tW[t]),"transparent"===t?[255,255,255,0]:tU.sourceFromHex(t)||tU.sourceFromRgb(t)||tU.sourceFromHsl(t)||(this.isUnrecognised=!0,[0,0,0,1])}getSource(){return this._source}setSource(t){this._source=t}toRgb(){let[t,e,i]=this.getSource();return"rgb(".concat(t,",").concat(e,",").concat(i,")")}toRgba(){return"rgba(".concat(this.getSource().join(","),")")}toHsl(){let[t,e,i]=tH(...this.getSource());return"hsl(".concat(t,",").concat(e,"%,").concat(i,"%)")}toHsla(){let[t,e,i,r]=tH(...this.getSource());return"hsla(".concat(t,",").concat(e,"%,").concat(i,"%,").concat(r,")")}toHex(){return this.toHexa().slice(0,6)}toHexa(){let[t,e,i,r]=this.getSource();return"".concat(tG(t)).concat(tG(e)).concat(tG(i)).concat(tG(Math.round(255*r)))}getAlpha(){return this.getSource()[3]}setAlpha(t){return this._source[3]=t,this}toGrayscale(){return this.setSource(tN(this.getSource())),this}toBlackWhite(t){let[e,,,i]=tN(this.getSource()),r=e<(t||127)?0:255;return this.setSource([r,r,r,i]),this}overlayWith(t){t instanceof tU||(t=new tU(t));let e=this.getSource(),i=t.getSource(),[r,s,n]=e.map((t,e)=>Math.round(.5*t+.5*i[e]));return this.setSource([r,s,n,e[3]]),this}static fromRgb(t){return tU.fromRgba(t)}static fromRgba(t){return new tU(tU.sourceFromRgb(t))}static sourceFromRgb(t){let e=t.match(/^rgba?\(\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*(?:\s*[,/]\s*(\d{0,3}(?:\.\d+)?%?)\s*)?\)$/i);if(e){let[t,i,r]=e.slice(1,4).map(t=>{let e=parseFloat(t);return t.endsWith("%")?Math.round(2.55*e):e});return[t,i,r,tz(e[4])]}}static fromHsl(t){return tU.fromHsla(t)}static fromHsla(t){return new tU(tU.sourceFromHsl(t))}static sourceFromHsl(t){let e,i,r;let s=t.match(/^hsla?\(\s*([+-]?\d{0,3}(?:\.\d+)?(?:deg|turn|rad)?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*(?:\s*[,/]\s*(\d*(?:\.\d+)?%?)\s*)?\)$/i);if(!s)return;let n=(tU.parseAngletoDegrees(s[1])%360+360)%360/360,o=parseFloat(s[2])/100,a=parseFloat(s[3])/100;if(0===o)e=i=r=a;else{let t=a<=.5?a*(o+1):a+o-a*o,s=2*a-t;e=tV(s,t,n+1/3),i=tV(s,t,n),r=tV(s,t,n-1/3)}return[Math.round(255*e),Math.round(255*i),Math.round(255*r),tz(s[4])]}static fromHex(t){return new tU(tU.sourceFromHex(t))}static sourceFromHex(t){if(t.match(/^#?(([0-9a-f]){3,4}|([0-9a-f]{2}){3,4})$/i)){let e=t.slice(t.indexOf("#")+1),[i,r,s,n=255]=(e.length<=4?e.split("").map(t=>t+t):e.match(/.{2}/g)).map(t=>parseInt(t,16));return[i,r,s,n/255]}}static parseAngletoDegrees(t){let e=t.toLowerCase(),i=parseFloat(e);return e.includes("rad")?tx(i):e.includes("turn")?360*i:i}}let tq=(t,e)=>parseFloat(Number(t).toFixed(e)),tK=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:16,i=/\D{0,2}$/.exec(t),r=parseFloat(t),s=d.DPI;switch(null==i?void 0:i[0]){case"mm":return r*s/25.4;case"cm":return r*s/2.54;case"in":return r*s;case"pt":return r*s/72;case"pc":return r*s/72*12;case"em":return r*e;default:return r}},tJ=t=>{let[e,i]=t.trim().split(" "),[r,s]=e&&e!==F?[e.slice(1,4),e.slice(5,8)]:e===F?[e,e]:["Mid","Mid"];return{meetOrSlice:i||"meet",alignX:r,alignY:s}},tQ=t=>"matrix("+t.map(t=>tq(t,d.NUM_FRACTION_DIGITS)).join(" ")+")",tZ=function(t,e){let i,r,s=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(e){if(e.toLive)i="url(#SVGID_".concat(e.id,")");else{let t=new tU(e),s=t.getAlpha();i=t.toRgb(),1!==s&&(r=s.toString())}}else i="none";return s?"".concat(t,": ").concat(i,"; ").concat(r?"".concat(t,"-opacity: ").concat(r,"; "):""):"".concat(t,'="').concat(i,'" ').concat(r?"".concat(t,'-opacity="').concat(r,'" '):"")},t$=t=>!!t&&void 0!==t.toLive,t0=t=>!!t&&"function"==typeof t.toObject,t1=t=>!!t&&void 0!==t.offsetX&&"source"in t,t2=t=>!!t&&"multiSelectionStacking"in t;function t5(t){let e=t&&t3(t),i=0,r=0;if(!t||!e)return{left:i,top:r};let s=t,n=e.documentElement,o=e.body||{scrollLeft:0,scrollTop:0};for(;s&&(s.parentNode||s.host)&&((s=s.parentNode||s.host)===e?(i=o.scrollLeft||n.scrollLeft||0,r=o.scrollTop||n.scrollTop||0):(i+=s.scrollLeft||0,r+=s.scrollTop||0),1!==s.nodeType||"fixed"!==s.style.position););return{left:i,top:r}}let t3=t=>t.ownerDocument||null,t4=t=>{var e;return(null===(e=t.ownerDocument)||void 0===e?void 0:e.defaultView)||null},t8=function(t,e,i){let{width:r,height:s}=i,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;t.width=r,t.height=s,n>1&&(t.setAttribute("width",(r*n).toString()),t.setAttribute("height",(s*n).toString()),e.scale(n,n))},t6=(t,e)=>{let{width:i,height:r}=e;i&&(t.style.width="number"==typeof i?"".concat(i,"px"):i),r&&(t.style.height="number"==typeof r?"".concat(r,"px"):r)};function t9(t){return void 0!==t.onselectstart&&(t.onselectstart=()=>!1),t.style.userSelect=F,t}class t7{constructor(t){o(this,"_originalCanvasStyle",void 0),o(this,"lower",void 0);let e=this.createLowerCanvas(t);this.lower={el:e,ctx:e.getContext("2d")}}createLowerCanvas(t){let e=t&&void 0!==t.getContext?t:t&&x().getElementById(t)||tf();if(e.hasAttribute("data-fabric"))throw new f("Trying to initialize a canvas that has already been initialized. Did you forget to dispose the canvas?");return this._originalCanvasStyle=e.style.cssText,e.setAttribute("data-fabric","main"),e.classList.add("lower-canvas"),e}cleanupDOM(t){let{width:e,height:i}=t,{el:r}=this.lower;r.classList.remove("lower-canvas"),r.removeAttribute("data-fabric"),r.setAttribute("width","".concat(e)),r.setAttribute("height","".concat(i)),r.style.cssText=this._originalCanvasStyle||"",this._originalCanvasStyle=void 0}setDimensions(t,e){let{el:i,ctx:r}=this.lower;t8(i,r,t,e)}setCSSDimensions(t){t6(this.lower.el,t)}calcOffset(){return function(t){var e;let i=t&&t3(t),r={left:0,top:0};if(!i)return r;let s=(null===(e=t4(t))||void 0===e?void 0:e.getComputedStyle(t,null))||{};r.left+=parseInt(s.borderLeftWidth,10)||0,r.top+=parseInt(s.borderTopWidth,10)||0,r.left+=parseInt(s.paddingLeft,10)||0,r.top+=parseInt(s.paddingTop,10)||0;let n={left:0,top:0},o=i.documentElement;void 0!==t.getBoundingClientRect&&(n=t.getBoundingClientRect());let a=t5(t);return{left:n.left+a.left-(o.clientLeft||0)+r.left,top:n.top+a.top-(o.clientTop||0)+r.top}}(this.lower.el)}dispose(){y().dispose(this.lower.el),delete this.lower}}let et={backgroundVpt:!0,backgroundColor:"",overlayVpt:!0,overlayColor:"",includeDefaultValues:!0,svgViewportTransformation:!0,renderOnAddRemove:!0,skipOffscreen:!0,enableRetinaScaling:!0,imageSmoothingEnabled:!0,controlsAboveOverlay:!1,allowTouchScrolling:!1,viewportTransform:[...M]};class ee extends tl(th){get lowerCanvasEl(){var t;return null===(t=this.elements.lower)||void 0===t?void 0:t.el}get contextContainer(){var t;return null===(t=this.elements.lower)||void 0===t?void 0:t.ctx}static getDefaults(){return ee.ownDefaults}constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),Object.assign(this,this.constructor.getDefaults()),this.set(e),this.initElements(t),this._setDimensionsImpl({width:this.width||this.elements.lower.el.width||0,height:this.height||this.elements.lower.el.height||0}),this.skipControlsDrawing=!1,this.viewportTransform=[...this.viewportTransform],this.calcViewportBoundaries()}initElements(t){this.elements=new t7(t)}add(){let t=super.add(...arguments);return arguments.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),t}insertAt(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];let s=super.insertAt(t,...i);return i.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),s}remove(){let t=super.remove(...arguments);return t.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),t}_onObjectAdded(t){t.canvas&&t.canvas!==this&&(g("warn","Canvas is trying to add an object that belongs to a different canvas.\nResulting to default behavior: removing object from previous canvas and adding to new canvas"),t.canvas.remove(t)),t._set("canvas",this),t.setCoords(),this.fire("object:added",{target:t}),t.fire("added",{target:this})}_onObjectRemoved(t){t._set("canvas",void 0),this.fire("object:removed",{target:t}),t.fire("removed",{target:this})}_onStackOrderChanged(){this.renderOnAddRemove&&this.requestRenderAll()}getRetinaScaling(){return this.enableRetinaScaling?b():1}calcOffset(){return this._offset=this.elements.calcOffset()}getWidth(){return this.width}getHeight(){return this.height}setWidth(t,e){return this.setDimensions({width:t},e)}setHeight(t,e){return this.setDimensions({height:t},e)}_setDimensionsImpl(t){let{cssOnly:e=!1,backstoreOnly:i=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e){let e=l({width:this.width,height:this.height},t);this.elements.setDimensions(e,this.getRetinaScaling()),this.hasLostContext=!0,this.width=e.width,this.height=e.height}i||this.elements.setCSSDimensions(t),this.calcOffset()}setDimensions(t,e){this._setDimensionsImpl(t,e),e&&e.cssOnly||this.requestRenderAll()}getZoom(){return this.viewportTransform[0]}setViewportTransform(t){this.viewportTransform=t,this.calcViewportBoundaries(),this.renderOnAddRemove&&this.requestRenderAll()}zoomToPoint(t,e){let i=[...this.viewportTransform],r=tb(t,tS(i));i[0]=e,i[3]=e;let s=tb(r,i);i[4]+=t.x-s.x,i[5]+=t.y-s.y,this.setViewportTransform(i)}setZoom(t){this.zoomToPoint(new tn(0,0),t)}absolutePan(t){let e=[...this.viewportTransform];return e[4]=-t.x,e[5]=-t.y,this.setViewportTransform(e)}relativePan(t){return this.absolutePan(new tn(-t.x-this.viewportTransform[4],-t.y-this.viewportTransform[5]))}getElement(){return this.elements.lower.el}clearContext(t){t.clearRect(0,0,this.width,this.height)}getContext(){return this.elements.lower.ctx}clear(){this.remove(...this.getObjects()),this.backgroundImage=void 0,this.overlayImage=void 0,this.backgroundColor="",this.overlayColor="",this.clearContext(this.getContext()),this.fire("canvas:cleared"),this.renderOnAddRemove&&this.requestRenderAll()}renderAll(){this.cancelRequestedRender(),this.destroyed||this.renderCanvas(this.getContext(),this._objects)}renderAndReset(){this.nextRenderHandle=0,this.renderAll()}requestRenderAll(){this.nextRenderHandle||this.disposed||this.destroyed||(this.nextRenderHandle=tc(()=>this.renderAndReset()))}calcViewportBoundaries(){let t=this.width,e=this.height,i=tS(this.viewportTransform),r=tb({x:0,y:0},i),s=tb({x:t,y:e},i),n=r.min(s),o=r.max(s);return this.vptCoords={tl:n,tr:new tn(o.x,n.y),bl:new tn(n.x,o.y),br:o}}cancelRequestedRender(){this.nextRenderHandle&&(tu(this.nextRenderHandle),this.nextRenderHandle=0)}drawControls(t){}renderCanvas(t,e){if(this.destroyed)return;let i=this.viewportTransform,r=this.clipPath;this.calcViewportBoundaries(),this.clearContext(t),t.imageSmoothingEnabled=this.imageSmoothingEnabled,t.patternQuality="best",this.fire("before:render",{ctx:t}),this._renderBackground(t),t.save(),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),this._renderObjects(t,e),t.restore(),this.controlsAboveOverlay||this.skipControlsDrawing||this.drawControls(t),r&&(r._set("canvas",this),r.shouldCache(),r._transformDone=!0,r.renderCache({forClipping:!0}),this.drawClipPathOnCanvas(t,r)),this._renderOverlay(t),this.controlsAboveOverlay&&!this.skipControlsDrawing&&this.drawControls(t),this.fire("after:render",{ctx:t}),this.__cleanupTask&&(this.__cleanupTask(),this.__cleanupTask=void 0)}drawClipPathOnCanvas(t,e){let i=this.viewportTransform;t.save(),t.transform(...i),t.globalCompositeOperation="destination-in",e.transform(t),t.scale(1/e.zoomX,1/e.zoomY),t.drawImage(e._cacheCanvas,-e.cacheTranslationX,-e.cacheTranslationY),t.restore()}_renderObjects(t,e){for(let i=0,r=e.length;i<r;++i)e[i]&&e[i].render(t)}_renderBackgroundOrOverlay(t,e){let i=this["".concat(e,"Color")],r=this["".concat(e,"Image")],s=this.viewportTransform,n=this["".concat(e,"Vpt")];if(!i&&!r)return;let o=t$(i);if(i){if(t.save(),t.beginPath(),t.moveTo(0,0),t.lineTo(this.width,0),t.lineTo(this.width,this.height),t.lineTo(0,this.height),t.closePath(),t.fillStyle=o?i.toLive(t):i,n&&t.transform(...s),o){t.transform(1,0,0,1,i.offsetX||0,i.offsetY||0);let e=i.gradientTransform||i.patternTransform;e&&t.transform(...e)}t.fill(),t.restore()}if(r){t.save();let{skipOffscreen:e}=this;this.skipOffscreen=n,n&&t.transform(...s),r.render(t),this.skipOffscreen=e,t.restore()}}_renderBackground(t){this._renderBackgroundOrOverlay(t,"background")}_renderOverlay(t){this._renderBackgroundOrOverlay(t,"overlay")}getCenter(){return{top:this.height/2,left:this.width/2}}getCenterPoint(){return new tn(this.width/2,this.height/2)}centerObjectH(t){return this._centerObject(t,new tn(this.getCenterPoint().x,t.getCenterPoint().y))}centerObjectV(t){return this._centerObject(t,new tn(t.getCenterPoint().x,this.getCenterPoint().y))}centerObject(t){return this._centerObject(t,this.getCenterPoint())}viewportCenterObject(t){return this._centerObject(t,this.getVpCenter())}viewportCenterObjectH(t){return this._centerObject(t,new tn(this.getVpCenter().x,t.getCenterPoint().y))}viewportCenterObjectV(t){return this._centerObject(t,new tn(t.getCenterPoint().x,this.getVpCenter().y))}getVpCenter(){return tb(this.getCenterPoint(),tS(this.viewportTransform))}_centerObject(t,e){t.setXY(e,E,E),t.setCoords(),this.renderOnAddRemove&&this.requestRenderAll()}toDatalessJSON(t){return this.toDatalessObject(t)}toObject(t){return this._toObjectMethod("toObject",t)}toJSON(){return this.toObject()}toDatalessObject(t){return this._toObjectMethod("toDatalessObject",t)}_toObjectMethod(t,e){let i=this.clipPath,r=i&&!i.excludeFromExport?this._toObject(i,t,e):null;return l(l(l({version:w},tX(this,e)),{},{objects:this._objects.filter(t=>!t.excludeFromExport).map(i=>this._toObject(i,t,e))},this.__serializeBgOverlay(t,e)),r?{clipPath:r}:null)}_toObject(t,e,i){let r;this.includeDefaultValues||(r=t.includeDefaultValues,t.includeDefaultValues=!1);let s=t[e](i);return this.includeDefaultValues||(t.includeDefaultValues=!!r),s}__serializeBgOverlay(t,e){let i={},r=this.backgroundImage,s=this.overlayImage,n=this.backgroundColor,o=this.overlayColor;return t$(n)?n.excludeFromExport||(i.background=n.toObject(e)):n&&(i.background=n),t$(o)?o.excludeFromExport||(i.overlay=o.toObject(e)):o&&(i.overlay=o),r&&!r.excludeFromExport&&(i.backgroundImage=this._toObject(r,t,e)),s&&!s.excludeFromExport&&(i.overlayImage=this._toObject(s,t,e)),i}toSVG(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;t.reviver=e;let i=[];return this._setSVGPreamble(i,t),this._setSVGHeader(i,t),this.clipPath&&i.push('<g clip-path="url(#'.concat(this.clipPath.clipPathId,')" >\n')),this._setSVGBgOverlayColor(i,"background"),this._setSVGBgOverlayImage(i,"backgroundImage",e),this._setSVGObjects(i,e),this.clipPath&&i.push("</g>\n"),this._setSVGBgOverlayColor(i,"overlay"),this._setSVGBgOverlayImage(i,"overlayImage",e),i.push("</svg>"),i.join("")}_setSVGPreamble(t,e){e.suppressPreamble||t.push('<?xml version="1.0" encoding="',e.encoding||"UTF-8",'" standalone="no" ?>\n','<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" ','"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n')}_setSVGHeader(t,e){let i;let r=e.width||"".concat(this.width),s=e.height||"".concat(this.height),n=d.NUM_FRACTION_DIGITS,o=e.viewBox;if(o)i='viewBox="'.concat(o.x," ").concat(o.y," ").concat(o.width," ").concat(o.height,'" ');else if(this.svgViewportTransformation){let t=this.viewportTransform;i='viewBox="'.concat(tq(-t[4]/t[0],n)," ").concat(tq(-t[5]/t[3],n)," ").concat(tq(this.width/t[0],n)," ").concat(tq(this.height/t[3],n),'" ')}else i='viewBox="0 0 '.concat(this.width," ").concat(this.height,'" ');t.push("<svg ",'xmlns="http://www.w3.org/2000/svg" ','xmlns:xlink="http://www.w3.org/1999/xlink" ','version="1.1" ','width="',r,'" ','height="',s,'" ',i,'xml:space="preserve">\n',"<desc>Created with Fabric.js ",w,"</desc>\n","<defs>\n",this.createSVGFontFacesMarkup(),this.createSVGRefElementsMarkup(),this.createSVGClipPathMarkup(e),"</defs>\n")}createSVGClipPathMarkup(t){let e=this.clipPath;return e?(e.clipPathId="CLIPPATH_".concat(tg()),'<clipPath id="'.concat(e.clipPathId,'" >\n').concat(e.toClipPathSVG(t.reviver),"</clipPath>\n")):""}createSVGRefElementsMarkup(){return["background","overlay"].map(t=>{let e=this["".concat(t,"Color")];if(t$(e)){let i=this["".concat(t,"Vpt")],r=this.viewportTransform,s={isType:()=>!1,width:this.width/(i?r[0]:1),height:this.height/(i?r[3]:1)};return e.toSVG(s,{additionalTransform:i?tQ(r):""})}}).join("")}createSVGFontFacesMarkup(){let t=[],e={},i=d.fontPaths;this._objects.forEach(function e(i){t.push(i),ta(i)&&i._objects.forEach(e)}),t.forEach(t=>{if(!t||"function"!=typeof t._renderText)return;let{styles:r,fontFamily:s}=t;!e[s]&&i[s]&&(e[s]=!0,r&&Object.values(r).forEach(t=>{Object.values(t).forEach(t=>{let{fontFamily:r=""}=t;!e[r]&&i[r]&&(e[r]=!0)})}))});let r=Object.keys(e).map(t=>"		@font-face {\n			font-family: '".concat(t,"';\n			src: url('").concat(i[t],"');\n		}\n")).join("");return r?'	<style type="text/css"><![CDATA[\n'.concat(r,"]]></style>\n"):""}_setSVGObjects(t,e){this.forEachObject(i=>{i.excludeFromExport||this._setSVGObject(t,i,e)})}_setSVGObject(t,e,i){t.push(e.toSVG(i))}_setSVGBgOverlayImage(t,e,i){let r=this[e];r&&!r.excludeFromExport&&r.toSVG&&t.push(r.toSVG(i))}_setSVGBgOverlayColor(t,e){let i=this["".concat(e,"Color")];if(i){if(t$(i)){let r=i.repeat||"",s=this.width,n=this.height,o=this["".concat(e,"Vpt")]?tQ(tS(this.viewportTransform)):"";t.push('<rect transform="'.concat(o," translate(").concat(s/2,",").concat(n/2,')" x="').concat(i.offsetX-s/2,'" y="').concat(i.offsetY-n/2,'" width="').concat(("repeat-y"===r||"no-repeat"===r)&&t1(i)?i.source.width:s,'" height="').concat(("repeat-x"===r||"no-repeat"===r)&&t1(i)?i.source.height:n,'" fill="url(#SVGID_').concat(i.id,')"></rect>\n'))}else t.push('<rect x="0" y="0" width="100%" height="100%" ','fill="',i,'"',"></rect>\n")}}loadFromJSON(t,e){let{signal:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!t)return Promise.reject(new f("`json` is undefined"));let r="string"==typeof t?JSON.parse(t):t,{objects:s=[],backgroundImage:n,background:o,overlayImage:a,overlay:l,clipPath:h}=r,c=this.renderOnAddRemove;return this.renderOnAddRemove=!1,Promise.all([tI(s,{reviver:e,signal:i}),tB({backgroundImage:n,backgroundColor:o,overlayImage:a,overlayColor:l,clipPath:h},{signal:i})]).then(t=>{let[e,i]=t;return this.clear(),this.add(...e),this.set(r),this.set(i),this.renderOnAddRemove=c,this})}clone(t){let e=this.toObject(t);return this.cloneWithoutData().loadFromJSON(e)}cloneWithoutData(){let t=tm(this);return new this.constructor(t)}toDataURL(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{format:e="png",quality:i=1,multiplier:r=1,enableRetinaScaling:s=!1}=t,n=r*(s?this.getRetinaScaling():1);return tv(this.toCanvasElement(n,t),e,i)}toBlob(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{format:e="png",quality:i=1,multiplier:r=1,enableRetinaScaling:s=!1}=t,n=r*(s?this.getRetinaScaling():1);return t_(this.toCanvasElement(n,t),e,i)}toCanvasElement(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,{width:e,height:i,left:r,top:s,filter:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=(e||this.width)*t,a=(i||this.height)*t,l=this.getZoom(),h=this.width,c=this.height,u=this.skipControlsDrawing,d=l*t,g=this.viewportTransform,f=[d,0,0,d,(g[4]-(r||0))*t,(g[5]-(s||0))*t],p=this.enableRetinaScaling,m=tm({width:o,height:a}),v=n?this._objects.filter(t=>n(t)):this._objects;return this.enableRetinaScaling=!1,this.viewportTransform=f,this.width=o,this.height=a,this.skipControlsDrawing=!0,this.calcViewportBoundaries(),this.renderCanvas(m.getContext("2d"),v),this.viewportTransform=g,this.width=h,this.height=c,this.calcViewportBoundaries(),this.enableRetinaScaling=p,this.skipControlsDrawing=u,m}dispose(){return this.disposed||this.elements.cleanupDOM({width:this.width,height:this.height}),tt.cancelByCanvas(this),this.disposed=!0,new Promise((t,e)=>{let i=()=>{this.destroy(),t(!0)};i.kill=e,this.__cleanupTask&&this.__cleanupTask.kill("aborted"),this.destroyed?t(!1):this.nextRenderHandle?this.__cleanupTask=i:i()})}destroy(){this.destroyed=!0,this.cancelRequestedRender(),this.forEachObject(t=>t.dispose()),this._objects=[],this.backgroundImage&&this.backgroundImage.dispose(),this.backgroundImage=void 0,this.overlayImage&&this.overlayImage.dispose(),this.overlayImage=void 0,this.elements.dispose()}toString(){return"#<Canvas (".concat(this.complexity(),"): { objects: ").concat(this._objects.length," }>")}}o(ee,"ownDefaults",et);let ei=["touchstart","touchmove","touchend"],er=t=>{let e=t5(t.target),i=function(t){let e=t.changedTouches;return e&&e[0]?e[0]:t}(t);return new tn(i.clientX+e.left,i.clientY+e.top)},es=t=>ei.includes(t.type)||"touch"===t.pointerType,en=t=>{t.preventDefault(),t.stopPropagation()},eo=t=>{let e=0,i=0,r=0,s=0;for(let n=0,o=t.length;n<o;n++){let{x:o,y:a}=t[n];(o>r||!n)&&(r=o),(o<e||!n)&&(e=o),(a>s||!n)&&(s=a),(a<i||!n)&&(i=a)}return{left:e,top:i,width:r-e,height:s-i}},ea=["translateX","translateY","scaleX","scaleY"],el=(t,e)=>eh(t,tw(e,t.calcOwnMatrix())),eh=(t,e)=>{let i=tD(e),{translateX:r,translateY:s,scaleX:n,scaleY:o}=i,a=h(i,ea),l=new tn(r,s);t.flipX=!1,t.flipY=!1,Object.assign(t,a),t.set({scaleX:n,scaleY:o}),t.setPositionByOrigin(l,E,E)},ec=t=>{t.scaleX=1,t.scaleY=1,t.skewX=0,t.skewY=0,t.flipX=!1,t.flipY=!1,t.rotate(0)},eu=t=>({scaleX:t.scaleX,scaleY:t.scaleY,skewX:t.skewX,skewY:t.skewY,angle:t.angle,left:t.left,flipX:t.flipX,flipY:t.flipY,top:t.top}),ed=(t,e,i)=>{let r=t/2,s=e/2,n=eo([new tn(-r,-s),new tn(r,-s),new tn(-r,s),new tn(r,s)].map(t=>t.transform(i)));return new tn(n.width,n.height)},eg=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:M;return tw(tS(arguments.length>1&&void 0!==arguments[1]?arguments[1]:M),t)},ef=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:M,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:M;return t.transform(eg(e,i))},ep=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:M,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:M;return t.transform(eg(e,i),!0)},em=(t,e,i)=>{let r=eg(e,i);return eh(t,tw(r,t.calcOwnMatrix())),r},ev=(t,e)=>{var i;let{transform:{target:r}}=e;null===(i=r.canvas)||void 0===i||i.fire("object:".concat(t),l(l({},e),{},{target:r})),r.fire(t,e)},e_={left:-.5,top:-.5,center:0,bottom:.5,right:.5},ey=t=>"string"==typeof t?e_[t]:t-.5,ex="not-allowed";function eC(t){return ey(t.originX)===ey(E)&&ey(t.originY)===ey(E)}let eb=(t,e)=>t[e],eS=(t,e,i,r)=>({e:t,transform:e,pointer:new tn(i,r)});function ew(t,e){return Math.round((t.getTotalAngle()+tx(Math.atan2(e.y,e.x))+360)%360/45)}function eT(t,e,i,r,s){var n;let{target:o,corner:a}=t,l=o.controls[a],h=(null===(n=o.canvas)||void 0===n?void 0:n.getZoom())||1,c=o.padding/h,u=function(t,e,i,r){let s=t.getRelativeCenterPoint(),n=void 0!==i&&void 0!==r?t.translateToGivenOrigin(s,E,E,i,r):new tn(t.left,t.top);return(t.angle?e.rotate(-ty(t.angle),s):e).subtract(n)}(o,new tn(r,s),e,i);return u.x>=c&&(u.x-=c),u.x<=-c&&(u.x+=c),u.y>=c&&(u.y-=c),u.y<=c&&(u.y+=c),u.x-=l.offsetX,u.y-=l.offsetY,u}let eO=(t,e,i,r)=>{let{target:s,offsetX:n,offsetY:o}=e,a=i-n,l=r-o,h=!eb(s,"lockMovementX")&&s.left!==a,c=!eb(s,"lockMovementY")&&s.top!==l;return h&&s.set(P,a),c&&s.set("top",l),(h||c)&&ev(R,eS(t,e,i,r)),h||c};class eD{getSvgStyles(t){let e=this.fillRule?this.fillRule:"nonzero",i=this.strokeWidth?this.strokeWidth:"0",r=this.strokeDashArray?this.strokeDashArray.join(" "):F,s=this.strokeDashOffset?this.strokeDashOffset:"0",n=this.strokeLineCap?this.strokeLineCap:"butt",o=this.strokeLineJoin?this.strokeLineJoin:"miter",a=this.strokeMiterLimit?this.strokeMiterLimit:"4",l=void 0!==this.opacity?this.opacity:"1",h=this.visible?"":" visibility: hidden;",c=t?"":this.getSvgFilter(),u=tZ(K,this.fill);return[tZ(J,this.stroke),"stroke-width: ",i,"; ","stroke-dasharray: ",r,"; ","stroke-linecap: ",n,"; ","stroke-dashoffset: ",s,"; ","stroke-linejoin: ",o,"; ","stroke-miterlimit: ",a,"; ",u,"fill-rule: ",e,"; ","opacity: ",l,";",c,h].join("")}getSvgFilter(){return this.shadow?"filter: url(#SVGID_".concat(this.shadow.id,");"):""}getSvgCommons(){return[this.id?'id="'.concat(this.id,'" '):"",this.clipPath?'clip-path="url(#'.concat(this.clipPath.clipPathId,')" '):""].join("")}getSvgTransform(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=t?this.calcTransformMatrix():this.calcOwnMatrix(),r='transform="'.concat(tQ(i));return"".concat(r).concat(e,'" ')}_toSVG(t){return[""]}toSVG(t){return this._createBaseSVGMarkup(this._toSVG(t),{reviver:t})}toClipPathSVG(t){return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(t),{reviver:t})}_createBaseClipPathSVGMarkup(t){let{reviver:e,additionalTransform:i=""}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=[this.getSvgTransform(!0,i),this.getSvgCommons()].join(""),s=t.indexOf("COMMON_PARTS");return t[s]=r,e?e(t.join("")):t.join("")}_createBaseSVGMarkup(t){let e,{noStyle:i,reviver:r,withShadow:s,additionalTransform:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=i?"":'style="'.concat(this.getSvgStyles(),'" '),a=s?'style="'.concat(this.getSvgFilter(),'" '):"",l=this.clipPath,h=this.strokeUniform?'vector-effect="non-scaling-stroke" ':"",c=l&&l.absolutePositioned,u=this.stroke,d=this.fill,g=this.shadow,f=[],p=t.indexOf("COMMON_PARTS");l&&(l.clipPathId="CLIPPATH_".concat(tg()),e='<clipPath id="'.concat(l.clipPathId,'" >\n').concat(l.toClipPathSVG(r),"</clipPath>\n")),c&&f.push("<g ",a,this.getSvgCommons()," >\n"),f.push("<g ",this.getSvgTransform(!1),c?"":a+this.getSvgCommons()," >\n");let m=[o,h,i?"":this.addPaintOrder()," ",n?'transform="'.concat(n,'" '):""].join("");return t[p]=m,t$(d)&&f.push(d.toSVG(this)),t$(u)&&f.push(u.toSVG(this)),g&&f.push(g.toSVG(this)),l&&f.push(e),f.push(t.join("")),f.push("</g>\n"),c&&f.push("</g>\n"),r?r(f.join("")):f.join("")}addPaintOrder(){return this.paintFirst!==K?' paint-order="'.concat(this.paintFirst,'" '):""}}function ek(t){return RegExp("^("+t.join("|")+")\\b","i")}let eM=String.raw(e9||(e9=c(["(?:[-+]?(?:d*.d+|d+.?)(?:[eE][-+]?d+)?)"],["(?:[-+]?(?:\\d*\\.\\d+|\\d+\\.?)(?:[eE][-+]?\\d+)?)"]))),eE="http://www.w3.org/2000/svg",eP=RegExp("(normal|italic)?\\s*(normal|small-caps)?\\s*(normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900)?\\s*("+eM+"(?:px|cm|mm|em|pt|pc|in)*)(?:\\/(normal|"+eM+"))?\\s+(.*)"),eA={cx:P,x:P,r:"radius",cy:"top",y:"top",display:"visible",visibility:"visible",transform:"transformMatrix","fill-opacity":"fillOpacity","fill-rule":"fillRule","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","letter-spacing":"charSpacing","paint-order":"paintFirst","stroke-dasharray":"strokeDashArray","stroke-dashoffset":"strokeDashOffset","stroke-linecap":"strokeLineCap","stroke-linejoin":"strokeLineJoin","stroke-miterlimit":"strokeMiterLimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth","text-decoration":"textDecoration","text-anchor":"textAnchor",opacity:"opacity","clip-path":"clipPath","clip-rule":"clipRule","vector-effect":"strokeUniform","image-rendering":"imageSmoothing"},ej="font-size",eF="clip-path",eL=ek(["path","circle","polygon","polyline","ellipse","rect","line","image","text"]),eR=ek(["symbol","image","marker","pattern","view","svg"]),eI=ek(["symbol","g","a","svg","clipPath","defs"]),eB=RegExp("^\\s*("+eM+"+)\\s*,?\\s*("+eM+"+)\\s*,?\\s*("+eM+"+)\\s*,?\\s*("+eM+"+)\\s*$"),eX=new tn(1,0),eY=new tn,eW=(t,e)=>t.rotate(e),eV=(t,e)=>new tn(e).subtract(t),eH=t=>t.distanceFrom(eY),ez=(t,e)=>Math.atan2(eq(t,e),eK(t,e)),eG=t=>ez(eX,t),eN=t=>t.eq(eY)?t:t.scalarDivide(eH(t)),eU=function(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return eN(new tn(-t.y,t.x).scalarMultiply(e?1:-1))},eq=(t,e)=>t.x*e.y-t.y*e.x,eK=(t,e)=>t.x*e.x+t.y*e.y,eJ=(t,e,i)=>{if(t.eq(e)||t.eq(i))return!0;let r=eq(e,i),s=eq(e,t),n=eq(i,t);return r>=0?s>=0&&n<=0:!(s<=0&&n>=0)},eQ="(-?\\d+(?:\\.\\d*)?(?:px)?(?:\\s?|$))?",eZ=RegExp("(?:\\s|^)"+eQ+eQ+"("+eM+"?(?:px)?)?(?:\\s?|$)(?:$|\\s)");class e${constructor(t){let e="string"==typeof t?e$.parseShadow(t):t;Object.assign(this,e$.ownDefaults,e),this.id=tg()}static parseShadow(t){let e=t.trim(),[,i=0,r=0,s=0]=(eZ.exec(e)||[]).map(t=>parseFloat(t)||0);return{color:(e.replace(eZ,"")||"rgb(0,0,0)").trim(),offsetX:i,offsetY:r,blur:s}}toString(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")}toSVG(t){let e=eW(new tn(this.offsetX,this.offsetY),ty(-t.angle)),i=new tU(this.color),r=40,s=40;return t.width&&t.height&&(r=100*tq((Math.abs(e.x)+this.blur)/t.width,d.NUM_FRACTION_DIGITS)+20,s=100*tq((Math.abs(e.y)+this.blur)/t.height,d.NUM_FRACTION_DIGITS)+20),t.flipX&&(e.x*=-1),t.flipY&&(e.y*=-1),'<filter id="SVGID_'.concat(this.id,'" y="-').concat(s,'%" height="').concat(100+2*s,'%" x="-').concat(r,'%" width="').concat(100+2*r,'%" >\n	<feGaussianBlur in="SourceAlpha" stdDeviation="').concat(tq(this.blur?this.blur/2:0,d.NUM_FRACTION_DIGITS),'"></feGaussianBlur>\n	<feOffset dx="').concat(tq(e.x,d.NUM_FRACTION_DIGITS),'" dy="').concat(tq(e.y,d.NUM_FRACTION_DIGITS),'" result="oBlur" ></feOffset>\n	<feFlood flood-color="').concat(i.toRgb(),'" flood-opacity="').concat(i.getAlpha(),'"/>\n	<feComposite in2="oBlur" operator="in" />\n	<feMerge>\n		<feMergeNode></feMergeNode>\n		<feMergeNode in="SourceGraphic"></feMergeNode>\n	</feMerge>\n</filter>\n')}toObject(){let t={color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY,affectStroke:this.affectStroke,nonScaling:this.nonScaling,type:this.constructor.type},e=e$.ownDefaults;return this.includeDefaultValues?t:tY(t,(t,i)=>t!==e[i])}static async fromObject(t){return new this(t)}}o(e$,"ownDefaults",{color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,nonScaling:!1}),o(e$,"type","shadow"),$.setClass(e$,"shadow");let e0=(t,e,i)=>Math.max(t,Math.min(e,i)),e1=["top",P,G,N,"flipX","flipY","originX","originY","angle","opacity","globalCompositeOperation","shadow","visible",U,q],e2=[K,J,"strokeWidth","strokeDashArray","width","height","paintFirst","strokeUniform","strokeLineCap","strokeDashOffset","strokeLineJoin","strokeMiterLimit","backgroundColor","clipPath"],e5=(t,e,i,r)=>(t<Math.abs(e)?(t=e,r=i/4):r=0===e&&0===t?i/D*Math.asin(1):i/D*Math.asin(e/t),{a:t,c:e,p:i,s:r}),e3=(t,e,i,r,s)=>t*Math.pow(2,10*(r-=1))*Math.sin((r*s-e)*D/i),e4=(t,e,i,r)=>-i*Math.cos(t/r*O)+i+e,e8=(t,e,i,r)=>(t/=r)<1/2.75?7.5625*t*t*i+e:t<2/2.75?i*(7.5625*(t-=1.5/2.75)*t+.75)+e:t<2.5/2.75?i*(7.5625*(t-=2.25/2.75)*t+.9375)+e:i*(7.5625*(t-=2.625/2.75)*t+.984375)+e,e6=(t,e,i,r)=>i-e8(r-t,0,i,r)+e;var e9,e7,it,ie,ii,ir,is,io,ia=Object.freeze({__proto__:null,defaultEasing:e4,easeInBack:function(t,e,i,r){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1.70158;return i*(t/=r)*t*((s+1)*t-s)+e},easeInBounce:e6,easeInCirc:(t,e,i,r)=>-i*(Math.sqrt(1-(t/=r)*t)-1)+e,easeInCubic:(t,e,i,r)=>i*(t/r)**3+e,easeInElastic:(t,e,i,r)=>{let s=0;if(0===t)return e;if(1==(t/=r))return e+i;s||(s=.3*r);let{a:n,s:o,p:a}=e5(i,i,s,1.70158);return-e3(n,o,a,t,r)+e},easeInExpo:(t,e,i,r)=>0===t?e:i*2**(10*(t/r-1))+e,easeInOutBack:function(t,e,i,r){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1.70158;return(t/=r/2)<1?i/2*(t*t*((1+(s*=1.525))*t-s))+e:i/2*((t-=2)*t*((1+(s*=1.525))*t+s)+2)+e},easeInOutBounce:(t,e,i,r)=>t<r/2?.5*e6(2*t,0,i,r)+e:.5*e8(2*t-r,0,i,r)+.5*i+e,easeInOutCirc:(t,e,i,r)=>(t/=r/2)<1?-i/2*(Math.sqrt(1-t**2)-1)+e:i/2*(Math.sqrt(1-(t-=2)*t)+1)+e,easeInOutCubic:(t,e,i,r)=>(t/=r/2)<1?i/2*t**3+e:i/2*((t-2)**3+2)+e,easeInOutElastic:(t,e,i,r)=>{let s=0;if(0===t)return e;if(2==(t/=r/2))return e+i;s||(s=.3*1.5*r);let{a:n,s:o,p:a,c:l}=e5(i,i,s,1.70158);return t<1?-.5*e3(n,o,a,t,r)+e:n*Math.pow(2,-10*(t-=1))*Math.sin((t*r-o)*D/a)*.5+l+e},easeInOutExpo:(t,e,i,r)=>0===t?e:t===r?e+i:(t/=r/2)<1?i/2*2**(10*(t-1))+e:-(i/2*(2**(-10*--t)+2))+e,easeInOutQuad:(t,e,i,r)=>(t/=r/2)<1?i/2*t**2+e:-i/2*(--t*(t-2)-1)+e,easeInOutQuart:(t,e,i,r)=>(t/=r/2)<1?i/2*t**4+e:-i/2*((t-=2)*t**3-2)+e,easeInOutQuint:(t,e,i,r)=>(t/=r/2)<1?i/2*t**5+e:i/2*((t-2)**5+2)+e,easeInOutSine:(t,e,i,r)=>-i/2*(Math.cos(Math.PI*t/r)-1)+e,easeInQuad:(t,e,i,r)=>i*(t/=r)*t+e,easeInQuart:(t,e,i,r)=>i*(t/=r)*t**3+e,easeInQuint:(t,e,i,r)=>i*(t/r)**5+e,easeInSine:(t,e,i,r)=>-i*Math.cos(t/r*O)+i+e,easeOutBack:function(t,e,i,r){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1.70158;return i*((t=t/r-1)*t*((s+1)*t+s)+1)+e},easeOutBounce:e8,easeOutCirc:(t,e,i,r)=>i*Math.sqrt(1-(t=t/r-1)*t)+e,easeOutCubic:(t,e,i,r)=>i*((t/r-1)**3+1)+e,easeOutElastic:(t,e,i,r)=>{let s=0;if(0===t)return e;if(1==(t/=r))return e+i;s||(s=.3*r);let{a:n,s:o,p:a,c:l}=e5(i,i,s,1.70158);return n*2**(-10*t)*Math.sin((t*r-o)*D/a)+l+e},easeOutExpo:(t,e,i,r)=>t===r?e+i:-(i*(2**(-10*t/r)+1))+e,easeOutQuad:(t,e,i,r)=>-i*(t/=r)*(t-2)+e,easeOutQuart:(t,e,i,r)=>-i*((t=t/r-1)*t**3-1)+e,easeOutQuint:(t,e,i,r)=>i*((t/r-1)**5+1)+e,easeOutSine:(t,e,i,r)=>i*Math.sin(t/r*O)+e});let il=()=>!1;class ih{constructor(t){let{startValue:e,byValue:i,duration:r=500,delay:s=0,easing:n=e4,onStart:a=T,onChange:l=T,onComplete:h=T,abort:c=il,target:u}=t;o(this,"_state","pending"),o(this,"durationProgress",0),o(this,"valueProgress",0),this.tick=this.tick.bind(this),this.duration=r,this.delay=s,this.easing=n,this._onStart=a,this._onChange=l,this._onComplete=h,this._abort=c,this.target=u,this.startValue=e,this.byValue=i,this.value=this.startValue,this.endValue=Object.freeze(this.calculate(this.duration).value)}get state(){return this._state}isDone(){return"aborted"===this._state||"completed"===this._state}start(){let t=t=>{"pending"===this._state&&(this.startTime=t||+new Date,this._state="running",this._onStart(),this.tick(this.startTime))};this.register(),this.delay>0?setTimeout(()=>tc(t),this.delay):tc(t)}tick(t){let e=(t||+new Date)-this.startTime,i=Math.min(e,this.duration);this.durationProgress=i/this.duration;let{value:r,valueProgress:s}=this.calculate(i);this.value=Object.freeze(r),this.valueProgress=s,"aborted"!==this._state&&(this._abort(this.value,this.valueProgress,this.durationProgress)?(this._state="aborted",this.unregister()):e>=this.duration?(this.durationProgress=this.valueProgress=1,this._onChange(this.endValue,this.valueProgress,this.durationProgress),this._state="completed",this._onComplete(this.endValue,this.valueProgress,this.durationProgress),this.unregister()):(this._onChange(this.value,this.valueProgress,this.durationProgress),tc(this.tick)))}register(){tt.push(this)}unregister(){tt.remove(this)}abort(){this._state="aborted",this.unregister()}}let ic=["startValue","endValue"];class iu extends ih{constructor(t){let{startValue:e=0,endValue:i=100}=t;super(l(l({},h(t,ic)),{},{startValue:e,byValue:i-e}))}calculate(t){let e=this.easing(t,this.startValue,this.byValue,this.duration);return{value:e,valueProgress:Math.abs((e-this.startValue)/this.byValue)}}}let id=["startValue","endValue"];class ig extends ih{constructor(t){let{startValue:e=[0],endValue:i=[100]}=t;super(l(l({},h(t,id)),{},{startValue:e,byValue:i.map((t,i)=>t-e[i])}))}calculate(t){let e=this.startValue.map((e,i)=>this.easing(t,e,this.byValue[i],this.duration,i));return{value:e,valueProgress:Math.abs((e[0]-this.startValue[0])/this.byValue[0])}}}let ip=["startValue","endValue","easing","onChange","onComplete","abort"],im=(t,e,i,r)=>e+i*(1-Math.cos(t/r*O)),iv=t=>t&&((e,i,r)=>t(new tU(e).toRgba(),i,r));class i_ extends ih{constructor(t){let{startValue:e,endValue:i,easing:r=im,onChange:s,onComplete:n,abort:o}=t,a=h(t,ip),c=new tU(e).getSource();super(l(l({},a),{},{startValue:c,byValue:new tU(i).getSource().map((t,e)=>t-c[e]),easing:r,onChange:iv(s),onComplete:iv(n),abort:iv(o)}))}calculate(t){let[e,i,r,s]=this.startValue.map((e,i)=>this.easing(t,e,this.byValue[i],this.duration,i)),n=[...[e,i,r].map(Math.round),e0(0,s,1)];return{value:n,valueProgress:n.map((t,e)=>0!==this.byValue[e]?Math.abs((t-this.startValue[e])/this.byValue[e]):0).find(t=>0!==t)||0}}}function iy(t){let e=Array.isArray(t.startValue)||Array.isArray(t.endValue)?new ig(t):new iu(t);return e.start(),e}function ix(t){let e=new i_(t);return e.start(),e}class iC{constructor(t){this.status=t,this.points=[]}includes(t){return this.points.some(e=>e.eq(t))}append(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return this.points=this.points.concat(e.filter(t=>!this.includes(t))),this}static isPointContained(t,e,i){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(e.eq(i))return t.eq(e);if(e.x===i.x)return t.x===e.x&&(r||t.y>=Math.min(e.y,i.y)&&t.y<=Math.max(e.y,i.y));if(e.y===i.y)return t.y===e.y&&(r||t.x>=Math.min(e.x,i.x)&&t.x<=Math.max(e.x,i.x));{let s=eV(e,i),n=eV(e,t).divide(s);return r?Math.abs(n.x)===Math.abs(n.y):n.x===n.y&&n.x>=0&&n.x<=1}}static isPointInPolygon(t,e){let i=new tn(t).setX(Math.min(t.x-1,...e.map(t=>t.x))),r=0;for(let s=0;s<e.length;s++){let n=this.intersectSegmentSegment(e[s],e[(s+1)%e.length],t,i);if(n.includes(t))return!0;r+=Number("Intersection"===n.status)}return r%2==1}static intersectLineLine(t,e,i,r){let s=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],n=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],o=e.x-t.x,a=e.y-t.y,l=r.x-i.x,h=r.y-i.y,c=t.x-i.x,u=t.y-i.y,d=l*u-h*c,g=o*u-a*c,f=h*o-l*a;if(0!==f){let e=d/f,i=g/f;return(s||0<=e&&e<=1)&&(n||0<=i&&i<=1)?new iC("Intersection").append(new tn(t.x+e*o,t.y+e*a)):new iC}if(0===d||0===g){let o=s||n||iC.isPointContained(t,i,r)||iC.isPointContained(e,i,r)||iC.isPointContained(i,t,e)||iC.isPointContained(r,t,e);return new iC(o?"Coincident":void 0)}return new iC("Parallel")}static intersectSegmentLine(t,e,i,r){return iC.intersectLineLine(t,e,i,r,!1,!0)}static intersectSegmentSegment(t,e,i,r){return iC.intersectLineLine(t,e,i,r,!1,!1)}static intersectLinePolygon(t,e,i){let r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],s=new iC,n=i.length;for(let o,a,l,h=0;h<n;h++){if(o=i[h],a=i[(h+1)%n],"Coincident"===(l=iC.intersectLineLine(t,e,o,a,r,!1)).status)return l;s.append(...l.points)}return s.points.length>0&&(s.status="Intersection"),s}static intersectSegmentPolygon(t,e,i){return iC.intersectLinePolygon(t,e,i,!1)}static intersectPolygonPolygon(t,e){let i=new iC,r=t.length,s=[];for(let n=0;n<r;n++){let o=t[n],a=t[(n+1)%r],l=iC.intersectSegmentPolygon(o,a,e);"Coincident"===l.status?(s.push(l),i.append(o,a)):i.append(...l.points)}return s.length>0&&s.length===t.length?new iC("Coincident"):(i.points.length>0&&(i.status="Intersection"),i)}static intersectPolygonRectangle(t,e,i){let r=e.min(i),s=e.max(i),n=new tn(s.x,r.y),o=new tn(r.x,s.y);return iC.intersectPolygonPolygon(t,[r,n,s,o])}}class ib extends th{getX(){return this.getXY().x}setX(t){this.setXY(this.getXY().setX(t))}getY(){return this.getXY().y}setY(t){this.setXY(this.getXY().setY(t))}getRelativeX(){return this.left}setRelativeX(t){this.left=t}getRelativeY(){return this.top}setRelativeY(t){this.top=t}getXY(){let t=this.getRelativeXY();return this.group?tb(t,this.group.calcTransformMatrix()):t}setXY(t,e,i){this.group&&(t=tb(t,tS(this.group.calcTransformMatrix()))),this.setRelativeXY(t,e,i)}getRelativeXY(){return new tn(this.left,this.top)}setRelativeXY(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.originX,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.originY;this.setPositionByOrigin(t,e,i)}isStrokeAccountedForInDimensions(){return!1}getCoords(){let{tl:t,tr:e,br:i,bl:r}=this.aCoords||(this.aCoords=this.calcACoords()),s=[t,e,i,r];if(this.group){let t=this.group.calcTransformMatrix();return s.map(e=>tb(e,t))}return s}intersectsWithRect(t,e){return"Intersection"===iC.intersectPolygonRectangle(this.getCoords(),t,e).status}intersectsWithObject(t){let e=iC.intersectPolygonPolygon(this.getCoords(),t.getCoords());return"Intersection"===e.status||"Coincident"===e.status||t.isContainedWithinObject(this)||this.isContainedWithinObject(t)}isContainedWithinObject(t){return this.getCoords().every(e=>t.containsPoint(e))}isContainedWithinRect(t,e){let{left:i,top:r,width:s,height:n}=this.getBoundingRect();return i>=t.x&&i+s<=e.x&&r>=t.y&&r+n<=e.y}isOverlapping(t){return this.intersectsWithObject(t)||this.isContainedWithinObject(t)||t.isContainedWithinObject(this)}containsPoint(t){return iC.isPointInPolygon(t,this.getCoords())}isOnScreen(){if(!this.canvas)return!1;let{tl:t,br:e}=this.canvas.vptCoords;return!!this.getCoords().some(i=>i.x<=e.x&&i.x>=t.x&&i.y<=e.y&&i.y>=t.y)||!!this.intersectsWithRect(t,e)||this.containsPoint(t.midPointFrom(e))}isPartiallyOnScreen(){if(!this.canvas)return!1;let{tl:t,br:e}=this.canvas.vptCoords;return!!this.intersectsWithRect(t,e)||this.getCoords().every(i=>(i.x>=e.x||i.x<=t.x)&&(i.y>=e.y||i.y<=t.y))&&this.containsPoint(t.midPointFrom(e))}getBoundingRect(){return eo(this.getCoords())}getScaledWidth(){return this._getTransformedDimensions().x}getScaledHeight(){return this._getTransformedDimensions().y}scale(t){this._set(G,t),this._set(N,t),this.setCoords()}scaleToWidth(t){let e=this.getBoundingRect().width/this.getScaledWidth();return this.scale(t/this.width/e)}scaleToHeight(t){let e=this.getBoundingRect().height/this.getScaledHeight();return this.scale(t/this.height/e)}getCanvasRetinaScaling(){var t;return(null===(t=this.canvas)||void 0===t?void 0:t.getRetinaScaling())||1}getTotalAngle(){return this.group?tx(tO(this.calcTransformMatrix())):this.angle}getViewportTransform(){var t;return(null===(t=this.canvas)||void 0===t?void 0:t.viewportTransform)||M.concat()}calcACoords(){let t=tM({angle:this.angle}),{x:e,y:i}=this.getRelativeCenterPoint(),r=tw(tk(e,i),t),s=this._getTransformedDimensions(),n=s.x/2,o=s.y/2;return{tl:tb({x:-n,y:-o},r),tr:tb({x:n,y:-o},r),bl:tb({x:-n,y:o},r),br:tb({x:n,y:o},r)}}setCoords(){this.aCoords=this.calcACoords()}transformMatrixKey(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=[];return!t&&this.group&&(e=this.group.transformMatrixKey(t)),e.push(this.top,this.left,this.width,this.height,this.scaleX,this.scaleY,this.angle,this.strokeWidth,this.skewX,this.skewY,+this.flipX,+this.flipY,ey(this.originX),ey(this.originY)),e}calcTransformMatrix(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.calcOwnMatrix();if(t||!this.group)return e;let i=this.transformMatrixKey(t),r=this.matrixCache;return r&&r.key.every((t,e)=>t===i[e])?r.value:(this.group&&(e=tw(this.group.calcTransformMatrix(!1),e)),this.matrixCache={key:i,value:e},e)}calcOwnMatrix(){let t=this.transformMatrixKey(!0),e=this.ownMatrixCache;if(e&&e.key===t)return e.value;let i=this.getRelativeCenterPoint(),r=tL({angle:this.angle,translateX:i.x,translateY:i.y,scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,flipX:this.flipX,flipY:this.flipY});return this.ownMatrixCache={key:t,value:r},r}_getNonTransformedDimensions(){return new tn(this.width,this.height).scalarAdd(this.strokeWidth)}_calculateCurrentDimensions(t){return this._getTransformedDimensions(t).transform(this.getViewportTransform(),!0).scalarAdd(2*this.padding)}_getTransformedDimensions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=l({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,width:this.width,height:this.height,strokeWidth:this.strokeWidth},t),i=e.strokeWidth,r=i,s=0;this.strokeUniform&&(r=0,s=i);let n=e.width+r,o=e.height+r;return(0===e.skewX&&0===e.skewY?new tn(n*e.scaleX,o*e.scaleY):ed(n,o,tF(e))).scalarAdd(s)}translateToGivenOrigin(t,e,i,r,s){let n=t.x,o=t.y,a=ey(r)-ey(e),l=ey(s)-ey(i);if(a||l){let t=this._getTransformedDimensions();n+=a*t.x,o+=l*t.y}return new tn(n,o)}translateToCenterPoint(t,e,i){if(e===E&&i===E)return t;let r=this.translateToGivenOrigin(t,e,i,E,E);return this.angle?r.rotate(ty(this.angle),t):r}translateToOriginPoint(t,e,i){let r=this.translateToGivenOrigin(t,E,E,e,i);return this.angle?r.rotate(ty(this.angle),t):r}getCenterPoint(){let t=this.getRelativeCenterPoint();return this.group?tb(t,this.group.calcTransformMatrix()):t}getRelativeCenterPoint(){return this.translateToCenterPoint(new tn(this.left,this.top),this.originX,this.originY)}getPointByOrigin(t,e){return this.translateToOriginPoint(this.getRelativeCenterPoint(),t,e)}setPositionByOrigin(t,e,i){let r=this.translateToCenterPoint(t,e,i),s=this.translateToOriginPoint(r,this.originX,this.originY);this.set({left:s.x,top:s.y})}_getLeftTopCoords(){return this.translateToOriginPoint(this.getRelativeCenterPoint(),P,"top")}}let iS=["type"],iw=["extraParam"],iT=class t extends ib{static getDefaults(){return t.ownDefaults}get type(){let t=this.constructor.type;return"FabricObject"===t?"object":t.toLowerCase()}set type(t){g("warn","Setting type has no effect",t)}constructor(e){super(),o(this,"_cacheContext",null),Object.assign(this,t.ownDefaults),this.setOptions(e)}_createCacheCanvas(){this._cacheCanvas=tf(),this._cacheContext=this._cacheCanvas.getContext("2d"),this._updateCacheCanvas(),this.dirty=!0}_limitCacheSize(t){let e=t.width,i=t.height,r=d.maxCacheSideLimit,s=d.minCacheSideLimit;if(e<=r&&i<=r&&e*i<=d.perfLimitSizeTotal)return e<s&&(t.width=s),i<s&&(t.height=s),t;let[n,o]=S.limitDimsByArea(e/i),a=e0(s,n,r),l=e0(s,o,r);return e>a&&(t.zoomX/=e/a,t.width=a,t.capped=!0),i>l&&(t.zoomY/=i/l,t.height=l,t.capped=!0),t}_getCacheCanvasDimensions(){let t=this.getTotalObjectScaling(),e=this._getTransformedDimensions({skewX:0,skewY:0}),i=e.x*t.x/this.scaleX,r=e.y*t.y/this.scaleY;return{width:Math.ceil(i+2),height:Math.ceil(r+2),zoomX:t.x,zoomY:t.y,x:i,y:r}}_updateCacheCanvas(){let t=this._cacheCanvas,e=this._cacheContext,{width:i,height:r,zoomX:s,zoomY:n,x:o,y:a}=this._limitCacheSize(this._getCacheCanvasDimensions()),l=i!==t.width||r!==t.height,h=this.zoomX!==s||this.zoomY!==n;if(!t||!e)return!1;if(l||h){i!==t.width||r!==t.height?(t.width=i,t.height=r):(e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t.width,t.height));let l=o/2,h=a/2;return this.cacheTranslationX=Math.round(t.width/2-l)+l,this.cacheTranslationY=Math.round(t.height/2-h)+h,e.translate(this.cacheTranslationX,this.cacheTranslationY),e.scale(s,n),this.zoomX=s,this.zoomY=n,!0}return!1}setOptions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._setOptions(t)}transform(t){let e=this.group&&!this.group._transformDone||this.group&&this.canvas&&t===this.canvas.contextTop,i=this.calcTransformMatrix(!e);t.transform(i[0],i[1],i[2],i[3],i[4],i[5])}getObjectScaling(){if(!this.group)return new tn(Math.abs(this.scaleX),Math.abs(this.scaleY));let t=tD(this.calcTransformMatrix());return new tn(Math.abs(t.scaleX),Math.abs(t.scaleY))}getTotalObjectScaling(){let t=this.getObjectScaling();if(this.canvas){let e=this.canvas.getZoom(),i=this.getCanvasRetinaScaling();return t.scalarMultiply(e*i)}return t}getObjectOpacity(){let t=this.opacity;return this.group&&(t*=this.group.getObjectOpacity()),t}_constrainScale(t){return Math.abs(t)<this.minScaleLimit?t<0?-this.minScaleLimit:this.minScaleLimit:0===t?1e-4:t}_set(t,e){t!==G&&t!==N||(e=this._constrainScale(e)),t===G&&e<0?(this.flipX=!this.flipX,e*=-1):"scaleY"===t&&e<0?(this.flipY=!this.flipY,e*=-1):"shadow"!==t||!e||e instanceof e$||(e=new e$(e));let i=this[t]!==e;return this[t]=e,i&&this.constructor.cacheProperties.includes(t)&&(this.dirty=!0),this.parent&&(this.dirty||i&&this.constructor.stateProperties.includes(t))&&this.parent._set("dirty",!0),this}isNotVisible(){return 0===this.opacity||!this.width&&!this.height&&0===this.strokeWidth||!this.visible}render(t){this.isNotVisible()||this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(t.save(),this._setupCompositeOperation(t),this.drawSelectionBackground(t),this.transform(t),this._setOpacity(t),this._setShadow(t),this.shouldCache()?(this.renderCache(),this.drawCacheOnCanvas(t)):(this._removeCacheCanvas(),this.drawObject(t,!1,{}),this.dirty=!1),t.restore())}drawSelectionBackground(t){}renderCache(t){if(t=t||{},this._cacheCanvas&&this._cacheContext||this._createCacheCanvas(),this.isCacheDirty()&&this._cacheContext){let{zoomX:e,zoomY:i,cacheTranslationX:r,cacheTranslationY:s}=this,{width:n,height:o}=this._cacheCanvas;this.drawObject(this._cacheContext,t.forClipping,{zoomX:e,zoomY:i,cacheTranslationX:r,cacheTranslationY:s,width:n,height:o,parentClipPaths:[]}),this.dirty=!1}}_removeCacheCanvas(){this._cacheCanvas=void 0,this._cacheContext=null}hasStroke(){return this.stroke&&"transparent"!==this.stroke&&0!==this.strokeWidth}hasFill(){return this.fill&&"transparent"!==this.fill}needsItsOwnCache(){return!!(this.paintFirst===J&&this.hasFill()&&this.hasStroke()&&this.shadow)||!!this.clipPath}shouldCache(){return this.ownCaching=this.objectCaching&&(!this.parent||!this.parent.isOnACache())||this.needsItsOwnCache(),this.ownCaching}willDrawShadow(){return!!this.shadow&&(0!==this.shadow.offsetX||0!==this.shadow.offsetY)}drawClipPathOnCache(t,e,i){t.save(),e.inverted?t.globalCompositeOperation="destination-out":t.globalCompositeOperation="destination-in",t.setTransform(1,0,0,1,0,0),t.drawImage(i,0,0),t.restore()}drawObject(t,e,i){let r=this.fill,s=this.stroke;e?(this.fill="black",this.stroke="",this._setClippingProperties(t)):this._renderBackground(t),this._render(t),this._drawClipPath(t,this.clipPath,i),this.fill=r,this.stroke=s}createClipPathLayer(t,e){let i=tm(e),r=i.getContext("2d");if(r.translate(e.cacheTranslationX,e.cacheTranslationY),r.scale(e.zoomX,e.zoomY),t._cacheCanvas=i,e.parentClipPaths.forEach(t=>{t.transform(r)}),e.parentClipPaths.push(t),t.absolutePositioned){let t=tS(this.calcTransformMatrix());r.transform(t[0],t[1],t[2],t[3],t[4],t[5])}return t.transform(r),t.drawObject(r,!0,e),i}_drawClipPath(t,e,i){if(!e)return;e._transformDone=!0;let r=this.createClipPathLayer(e,i);this.drawClipPathOnCache(t,e,r)}drawCacheOnCanvas(t){t.scale(1/this.zoomX,1/this.zoomY),t.drawImage(this._cacheCanvas,-this.cacheTranslationX,-this.cacheTranslationY)}isCacheDirty(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.isNotVisible())return!1;let e=this._cacheCanvas,i=this._cacheContext;return!(!e||!i||t||!this._updateCacheCanvas())||!!(this.dirty||this.clipPath&&this.clipPath.absolutePositioned)&&(e&&i&&!t&&(i.save(),i.setTransform(1,0,0,1,0,0),i.clearRect(0,0,e.width,e.height),i.restore()),!0)}_renderBackground(t){if(!this.backgroundColor)return;let e=this._getNonTransformedDimensions();t.fillStyle=this.backgroundColor,t.fillRect(-e.x/2,-e.y/2,e.x,e.y),this._removeShadow(t)}_setOpacity(t){this.group&&!this.group._transformDone?t.globalAlpha=this.getObjectOpacity():t.globalAlpha*=this.opacity}_setStrokeStyles(t,e){let i=e.stroke;i&&(t.lineWidth=e.strokeWidth,t.lineCap=e.strokeLineCap,t.lineDashOffset=e.strokeDashOffset,t.lineJoin=e.strokeLineJoin,t.miterLimit=e.strokeMiterLimit,t$(i)?"percentage"===i.gradientUnits||i.gradientTransform||i.patternTransform?this._applyPatternForTransformedGradient(t,i):(t.strokeStyle=i.toLive(t),this._applyPatternGradientTransform(t,i)):t.strokeStyle=e.stroke)}_setFillStyles(t,e){let{fill:i}=e;i&&(t$(i)?(t.fillStyle=i.toLive(t),this._applyPatternGradientTransform(t,i)):t.fillStyle=i)}_setClippingProperties(t){t.globalAlpha=1,t.strokeStyle="transparent",t.fillStyle="#000000"}_setLineDash(t,e){e&&0!==e.length&&t.setLineDash(e)}_setShadow(t){if(!this.shadow)return;let e=this.shadow,i=this.canvas,r=this.getCanvasRetinaScaling(),[s,,,n]=(null==i?void 0:i.viewportTransform)||M,o=s*r,a=n*r,l=e.nonScaling?new tn(1,1):this.getObjectScaling();t.shadowColor=e.color,t.shadowBlur=e.blur*d.browserShadowBlurConstant*(o+a)*(l.x+l.y)/4,t.shadowOffsetX=e.offsetX*o*l.x,t.shadowOffsetY=e.offsetY*a*l.y}_removeShadow(t){this.shadow&&(t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0)}_applyPatternGradientTransform(t,e){if(!t$(e))return{offsetX:0,offsetY:0};let i=e.gradientTransform||e.patternTransform,r=-this.width/2+e.offsetX||0,s=-this.height/2+e.offsetY||0;return"percentage"===e.gradientUnits?t.transform(this.width,0,0,this.height,r,s):t.transform(1,0,0,1,r,s),i&&t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),{offsetX:r,offsetY:s}}_renderPaintInOrder(t){this.paintFirst===J?(this._renderStroke(t),this._renderFill(t)):(this._renderFill(t),this._renderStroke(t))}_render(t){}_renderFill(t){this.fill&&(t.save(),this._setFillStyles(t,this),"evenodd"===this.fillRule?t.fill("evenodd"):t.fill(),t.restore())}_renderStroke(t){if(this.stroke&&0!==this.strokeWidth){if(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this.strokeUniform){let e=this.getObjectScaling();t.scale(1/e.x,1/e.y)}this._setLineDash(t,this.strokeDashArray),this._setStrokeStyles(t,this),t.stroke(),t.restore()}}_applyPatternForTransformedGradient(t,e){var i;let r=this._limitCacheSize(this._getCacheCanvasDimensions()),s=this.getCanvasRetinaScaling(),n=r.x/this.scaleX/s,o=r.y/this.scaleY/s,a=tm({width:Math.ceil(n),height:Math.ceil(o)}),l=a.getContext("2d");l&&(l.beginPath(),l.moveTo(0,0),l.lineTo(n,0),l.lineTo(n,o),l.lineTo(0,o),l.closePath(),l.translate(n/2,o/2),l.scale(r.zoomX/this.scaleX/s,r.zoomY/this.scaleY/s),this._applyPatternGradientTransform(l,e),l.fillStyle=e.toLive(t),l.fill(),t.translate(-this.width/2-this.strokeWidth/2,-this.height/2-this.strokeWidth/2),t.scale(s*this.scaleX/r.zoomX,s*this.scaleY/r.zoomY),t.strokeStyle=null!==(i=l.createPattern(a,"no-repeat"))&&void 0!==i?i:"")}_findCenterFromElement(){return new tn(this.left+this.width/2,this.top+this.height/2)}clone(t){let e=this.toObject(t);return this.constructor.fromObject(e)}cloneAsImage(t){let e=this.toCanvasElement(t);return new($.getClass("image"))(e)}toCanvasElement(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=eu(this),i=this.group,r=this.shadow,s=Math.abs,n=t.enableRetinaScaling?b():1,o=(t.multiplier||1)*n,a=t.canvasProvider||(t=>new ee(t,{enableRetinaScaling:!1,renderOnAddRemove:!1,skipOffscreen:!1}));delete this.group,t.withoutTransform&&ec(this),t.withoutShadow&&(this.shadow=null),t.viewportTransform&&em(this,this.getViewportTransform()),this.setCoords();let l=tf(),h=this.getBoundingRect(),c=this.shadow,u=new tn;if(c){let t=c.blur,e=c.nonScaling?new tn(1,1):this.getObjectScaling();u.x=2*Math.round(s(c.offsetX)+t)*s(e.x),u.y=2*Math.round(s(c.offsetY)+t)*s(e.y)}let d=h.width+u.x,g=h.height+u.y;l.width=Math.ceil(d),l.height=Math.ceil(g);let f=a(l);"jpeg"===t.format&&(f.backgroundColor="#fff"),this.setPositionByOrigin(new tn(f.width/2,f.height/2),E,E);let p=this.canvas;f._objects=[this],this.set("canvas",f),this.setCoords();let m=f.toCanvasElement(o||1,t);return this.set("canvas",p),this.shadow=r,i&&(this.group=i),this.set(e),this.setCoords(),f._objects=[],f.destroy(),m}toDataURL(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return tv(this.toCanvasElement(t),t.format||"png",t.quality||1)}toBlob(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return t_(this.toCanvasElement(t),t.format||"png",t.quality||1)}isType(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.includes(this.constructor.type)||e.includes(this.type)}complexity(){return 1}toJSON(){return this.toObject()}rotate(t){let{centeredRotation:e,originX:i,originY:r}=this;if(e){let{x:t,y:e}=this.getRelativeCenterPoint();this.originX=E,this.originY=E,this.left=t,this.top=e}if(this.set("angle",t),e){let{x:t,y:e}=this.translateToOriginPoint(this.getRelativeCenterPoint(),i,r);this.left=t,this.top=e,this.originX=i,this.originY=r}}setOnGroup(){}_setupCompositeOperation(t){this.globalCompositeOperation&&(t.globalCompositeOperation=this.globalCompositeOperation)}dispose(){tt.cancelByTarget(this),this.off(),this._set("canvas",void 0),this._cacheCanvas&&y().dispose(this._cacheCanvas),this._cacheCanvas=void 0,this._cacheContext=null}animate(t,e){return Object.entries(t).reduce((t,i)=>{let[r,s]=i;return t[r]=this._animate(r,s,e),t},{})}_animate(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=t.split("."),s=this.constructor.colorProperties.includes(r[r.length-1]),{abort:n,startValue:o,onChange:a,onComplete:h}=i,c=l(l({},i),{},{target:this,startValue:null!=o?o:r.reduce((t,e)=>t[e],this),endValue:e,abort:null==n?void 0:n.bind(this),onChange:(t,e,i)=>{r.reduce((e,i,s)=>(s===r.length-1&&(e[i]=t),e[i]),this),a&&a(t,e,i)},onComplete:(t,e,i)=>{this.setCoords(),h&&h(t,e,i)}});return s?ix(c):iy(c)}isDescendantOf(t){let{parent:e,group:i}=this;return e===t||i===t||!!e&&e.isDescendantOf(t)||!!i&&i!==e&&i.isDescendantOf(t)}getAncestors(){let t=[],e=this;do(e=e.parent)&&t.push(e);while(e);return t}findCommonAncestors(t){if(this===t)return{fork:[],otherFork:[],common:[this,...this.getAncestors()]};let e=this.getAncestors(),i=t.getAncestors();if(0===e.length&&i.length>0&&this===i[i.length-1])return{fork:[],otherFork:[t,...i.slice(0,i.length-1)],common:[this]};for(let r,s=0;s<e.length;s++){if((r=e[s])===t)return{fork:[this,...e.slice(0,s)],otherFork:[],common:e.slice(s)};for(let n=0;n<i.length;n++){if(this===i[n])return{fork:[],otherFork:[t,...i.slice(0,n)],common:[this,...e]};if(r===i[n])return{fork:[this,...e.slice(0,s)],otherFork:[t,...i.slice(0,n)],common:e.slice(s)}}}return{fork:[this,...e],otherFork:[t,...i],common:[]}}hasCommonAncestors(t){let e=this.findCommonAncestors(t);return e&&!!e.common.length}isInFrontOf(t){if(this===t)return;let e=this.findCommonAncestors(t);if(e.fork.includes(t))return!0;if(e.otherFork.includes(this))return!1;let i=e.common[0]||this.canvas;if(!i)return;let r=e.fork.pop(),s=e.otherFork.pop(),n=i._objects.indexOf(r),o=i._objects.indexOf(s);return n>-1&&n>o}toObject(){let e;let i=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).concat(t.customProperties,this.constructor.customProperties||[]),r=d.NUM_FRACTION_DIGITS,{clipPath:s,fill:n,stroke:o,shadow:a,strokeDashArray:h,left:c,top:u,originX:g,originY:f,width:p,height:m,strokeWidth:v,strokeLineCap:_,strokeDashOffset:y,strokeLineJoin:x,strokeUniform:C,strokeMiterLimit:b,scaleX:S,scaleY:T,angle:O,flipX:D,flipY:k,opacity:M,visible:E,backgroundColor:P,fillRule:A,paintFirst:j,globalCompositeOperation:F,skewX:L,skewY:R}=this;s&&!s.excludeFromExport&&(e=s.toObject(i.concat("inverted","absolutePositioned")));let I=t=>tq(t,r),B=l(l({},tX(this,i)),{},{type:this.constructor.type,version:w,originX:g,originY:f,left:I(c),top:I(u),width:I(p),height:I(m),fill:t0(n)?n.toObject():n,stroke:t0(o)?o.toObject():o,strokeWidth:I(v),strokeDashArray:h?h.concat():h,strokeLineCap:_,strokeDashOffset:y,strokeLineJoin:x,strokeUniform:C,strokeMiterLimit:I(b),scaleX:I(S),scaleY:I(T),angle:I(O),flipX:D,flipY:k,opacity:I(M),shadow:a?a.toObject():a,visible:E,backgroundColor:P,fillRule:A,paintFirst:j,globalCompositeOperation:F,skewX:I(L),skewY:I(R)},e?{clipPath:e}:null);return this.includeDefaultValues?B:this._removeDefaultValues(B)}toDatalessObject(t){return this.toObject(t)}_removeDefaultValues(t){let e=this.constructor.getDefaults(),i=Object.keys(e).length>0?e:Object.getPrototypeOf(this);return tY(t,(t,e)=>{if(e===P||"top"===e||"type"===e)return!0;let r=i[e];return t!==r&&!(Array.isArray(t)&&Array.isArray(r)&&0===t.length&&0===r.length)})}toString(){return"#<".concat(this.constructor.type,">")}static _fromObject(t){let e=h(t,iS),i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{extraParam:r}=i;return tB(e,h(i,iw)).then(t=>r?(delete t[r],new this(e[r],t)):new this(t))}static fromObject(t,e){return this._fromObject(t,e)}};o(iT,"stateProperties",e1),o(iT,"cacheProperties",e2),o(iT,"ownDefaults",{top:0,left:0,width:0,height:0,angle:0,flipX:!1,flipY:!1,scaleX:1,scaleY:1,minScaleLimit:0,skewX:0,skewY:0,originX:P,originY:"top",strokeWidth:1,strokeUniform:!1,padding:0,opacity:1,paintFirst:K,fill:"rgb(0,0,0)",fillRule:"nonzero",stroke:null,strokeDashArray:null,strokeDashOffset:0,strokeLineCap:"butt",strokeLineJoin:"miter",strokeMiterLimit:4,globalCompositeOperation:"source-over",backgroundColor:"",shadow:null,visible:!0,includeDefaultValues:!0,excludeFromExport:!1,objectCaching:!0,clipPath:void 0,inverted:!1,absolutePositioned:!1,centeredRotation:!0,centeredScaling:!1,dirty:!0}),o(iT,"type","FabricObject"),o(iT,"colorProperties",[K,J,"backgroundColor"]),o(iT,"customProperties",[]),$.setClass(iT),$.setClass(iT,"object");let iO=(t,e,i)=>(r,s,n,o)=>{let a=e(r,s,n,o);return a&&ev(t,l(l({},eS(r,s,n,o)),i)),a};function iD(t){return(e,i,r,s)=>{let{target:n,originX:o,originY:a}=i,l=n.getRelativeCenterPoint(),h=n.translateToOriginPoint(l,o,a),c=t(e,i,r,s);return n.setPositionByOrigin(h,i.originX,i.originY),c}}let ik=iO(W,iD((t,e,i,r)=>{let s=eT(e,e.originX,e.originY,i,r);if(ey(e.originX)===ey(E)||ey(e.originX)===ey(j)&&s.x<0||ey(e.originX)===ey(P)&&s.x>0){let{target:t}=e,i=t.strokeWidth/(t.strokeUniform?t.scaleX:1),r=eC(e)?2:1,n=t.width,o=Math.abs(s.x*r/t.scaleX)-i;return t.set("width",Math.max(o,1)),n!==t.width}return!1}));function iM(t,e,i,r,s){r=r||{};let n=this.sizeX||r.cornerSize||s.cornerSize,o=this.sizeY||r.cornerSize||s.cornerSize,a=void 0!==r.transparentCorners?r.transparentCorners:s.transparentCorners,l=a?J:K,h=!a&&(r.cornerStrokeColor||s.cornerStrokeColor),c,u=e,d=i;t.save(),t.fillStyle=r.cornerColor||s.cornerColor||"",t.strokeStyle=r.cornerStrokeColor||s.cornerStrokeColor||"",n>o?(c=n,t.scale(1,o/n),d=i*n/o):o>n?(c=o,t.scale(n/o,1),u=e*o/n):c=n,t.beginPath(),t.arc(u,d,c/2,0,D,!1),t[l](),h&&t.stroke(),t.restore()}function iE(t,e,i,r,s){r=r||{};let n=this.sizeX||r.cornerSize||s.cornerSize,o=this.sizeY||r.cornerSize||s.cornerSize,a=void 0!==r.transparentCorners?r.transparentCorners:s.transparentCorners,l=a?J:K,h=!a&&(r.cornerStrokeColor||s.cornerStrokeColor),c=n/2,u=o/2;t.save(),t.fillStyle=r.cornerColor||s.cornerColor||"",t.strokeStyle=r.cornerStrokeColor||s.cornerStrokeColor||"",t.translate(e,i);let d=s.getTotalAngle();t.rotate(ty(d)),t["".concat(l,"Rect")](-c,-u,n,o),h&&t.strokeRect(-c,-u,n,o),t.restore()}class iP{constructor(t){o(this,"visible",!0),o(this,"actionName",z),o(this,"angle",0),o(this,"x",0),o(this,"y",0),o(this,"offsetX",0),o(this,"offsetY",0),o(this,"sizeX",0),o(this,"sizeY",0),o(this,"touchSizeX",0),o(this,"touchSizeY",0),o(this,"cursorStyle","crosshair"),o(this,"withConnection",!1),Object.assign(this,t)}shouldActivate(t,e,i,r){var s;let{tl:n,tr:o,br:a,bl:l}=r;return(null===(s=e.canvas)||void 0===s?void 0:s.getActiveObject())===e&&e.isControlVisible(t)&&iC.isPointInPolygon(i,[n,o,a,l])}getActionHandler(t,e,i){return this.actionHandler}getMouseDownHandler(t,e,i){return this.mouseDownHandler}getMouseUpHandler(t,e,i){return this.mouseUpHandler}cursorStyleHandler(t,e,i){return e.cursorStyle}getActionName(t,e,i){return e.actionName}getVisibility(t,e){var i,r;return null!==(i=null===(r=t._controlsVisibility)||void 0===r?void 0:r[e])&&void 0!==i?i:this.visible}setVisibility(t,e,i){this.visible=t}positionHandler(t,e,i,r){return new tn(this.x*t.x+this.offsetX,this.y*t.y+this.offsetY).transform(e)}calcCornerCoords(t,e,i,r,s,n){let o=tT([tk(i,r),tM({angle:t}),tE((s?this.touchSizeX:this.sizeX)||e,(s?this.touchSizeY:this.sizeY)||e)]);return{tl:new tn(-.5,-.5).transform(o),tr:new tn(.5,-.5).transform(o),br:new tn(.5,.5).transform(o),bl:new tn(-.5,.5).transform(o)}}render(t,e,i,r,s){"circle"===((r=r||{}).cornerStyle||s.cornerStyle)?iM.call(this,t,e,i,r,s):iE.call(this,t,e,i,r,s)}}let iA=(t,e,i)=>i.lockRotation?ex:e.cursorStyle,ij=iO(B,iD((t,e,i,r)=>{let{target:s,ex:n,ey:o,theta:a,originX:l,originY:h}=e,c=s.translateToOriginPoint(s.getRelativeCenterPoint(),l,h);if(eb(s,"lockRotation"))return!1;let u=Math.atan2(o-c.y,n-c.x),d=tx(Math.atan2(r-c.y,i-c.x)-u+a);if(s.snapAngle&&s.snapAngle>0){let t=s.snapAngle,e=s.snapThreshold||t,i=Math.ceil(d/t)*t,r=Math.floor(d/t)*t;Math.abs(d-r)<e?d=r:Math.abs(d-i)<e&&(d=i)}d<0&&(d=360+d),d%=360;let g=s.angle!==d;return s.angle=d,g}));function iF(t,e){let i=e.canvas,r=t[i.uniScaleKey];return i.uniformScaling&&!r||!i.uniformScaling&&r}function iL(t,e,i){let r=eb(t,"lockScalingX"),s=eb(t,"lockScalingY");if(r&&s||!e&&(r||s)&&i||r&&"x"===e||s&&"y"===e)return!0;let{width:n,height:o,strokeWidth:a}=t;return 0===n&&0===a&&"y"!==e||0===o&&0===a&&"x"!==e}let iR=["e","se","s","sw","w","nw","n","ne","e"],iI=(t,e,i)=>{let r=iF(t,i);if(iL(i,0!==e.x&&0===e.y?"x":0===e.x&&0!==e.y?"y":"",r))return ex;let s=ew(i,e);return"".concat(iR[s],"-resize")};function iB(t,e,i,r){let s,n,o,a,l,h,c=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},u=e.target,d=c.by,g=iF(t,u);if(iL(u,d,g))return!1;if(e.gestureScale)n=e.scaleX*e.gestureScale,o=e.scaleY*e.gestureScale;else{if(s=eT(e,e.originX,e.originY,i,r),l="y"!==d?Math.sign(s.x||e.signX||1):1,h="x"!==d?Math.sign(s.y||e.signY||1):1,e.signX||(e.signX=l),e.signY||(e.signY=h),eb(u,"lockScalingFlip")&&(e.signX!==l||e.signY!==h))return!1;if(a=u._getTransformedDimensions(),g&&!d){let t=Math.abs(s.x)+Math.abs(s.y),{original:i}=e,r=t/(Math.abs(a.x*i.scaleX/u.scaleX)+Math.abs(a.y*i.scaleY/u.scaleY));n=i.scaleX*r,o=i.scaleY*r}else n=Math.abs(s.x*u.scaleX/a.x),o=Math.abs(s.y*u.scaleY/a.y);eC(e)&&(n*=2,o*=2),e.signX!==l&&"y"!==d&&(e.originX=.5-ey(e.originX),n*=-1,e.signX=l),e.signY!==h&&"x"!==d&&(e.originY=.5-ey(e.originY),o*=-1,e.signY=h)}let f=u.scaleX,p=u.scaleY;return d?("x"===d&&u.set(G,n),"y"===d&&u.set(N,o)):(eb(u,"lockScalingX")||u.set(G,n),eb(u,"lockScalingY")||u.set(N,o)),f!==u.scaleX||p!==u.scaleY}let iX=iO(I,iD((t,e,i,r)=>iB(t,e,i,r))),iY=iO(I,iD((t,e,i,r)=>iB(t,e,i,r,{by:"x"}))),iW=iO(I,iD((t,e,i,r)=>iB(t,e,i,r,{by:"y"}))),iV=["target","ex","ey","skewingSide"],iH={x:{counterAxis:"y",scale:G,skew:U,lockSkewing:"lockSkewingX",origin:"originX",flip:"flipX"},y:{counterAxis:"x",scale:N,skew:q,lockSkewing:"lockSkewingY",origin:"originY",flip:"flipY"}},iz=["ns","nesw","ew","nwse"],iG=(t,e,i)=>{if(0!==e.x&&eb(i,"lockSkewingY")||0!==e.y&&eb(i,"lockSkewingX"))return ex;let r=ew(i,e)%4;return"".concat(iz[r],"-resize")};function iN(t,e,i,r,s){let{target:n}=i,{counterAxis:o,origin:a,lockSkewing:c,skew:u,flip:d}=iH[t];if(eb(n,c))return!1;let{origin:g,flip:f}=iH[o],p=-Math.sign(ey(i[g])*(n[f]?-1:1))*(n[d]?-1:1),m=-(.5*((0===n[u]&&eT(i,E,E,r,s)[t]>0||n[u]>0?1:-1)*p))+.5;return iO(Y,iD((e,i,r,s)=>(function(t,e,i){let{target:r,ex:s,ey:n,skewingSide:o}=e,a=h(e,iV),{skew:l}=iH[t],c=i.subtract(new tn(s,n)).divide(new tn(r.scaleX,r.scaleY))[t],u=r[l],d=Math.tan(ty(a[l])),g=tx(Math.atan(2*c*o/Math.max("y"===t?r._getTransformedDimensions({scaleX:1,scaleY:1,skewX:0}).x:r._getTransformedDimensions({scaleX:1,scaleY:1}).y,1)+d));r.set(l,g);let f=u!==r[l];if(f&&"y"===t){let{skewX:t,scaleX:e}=r,i=r._getTransformedDimensions({skewY:u}),s=r._getTransformedDimensions(),n=0!==t?i.x/s.x:1;1!==n&&r.set(G,n*e)}return f})(t,i,new tn(r,s))))(e,l(l({},i),{},{[a]:m,skewingSide:p}),r,s)}let iU=(t,e,i,r)=>iN("x",t,e,i,r),iq=(t,e,i,r)=>iN("y",t,e,i,r);function iK(t,e){return t[e.canvas.altActionKey]}let iJ=(t,e,i)=>{let r=iK(t,i);return 0===e.x?r?U:N:0===e.y?r?q:G:""},iQ=(t,e,i)=>iK(t,i)?iG(0,e,i):iI(t,e,i),iZ=(t,e,i,r)=>iK(t,e.target)?iq(t,e,i,r):iY(t,e,i,r),i$=(t,e,i,r)=>iK(t,e.target)?iU(t,e,i,r):iW(t,e,i,r),i0=()=>({ml:new iP({x:-.5,y:0,cursorStyleHandler:iQ,actionHandler:iZ,getActionName:iJ}),mr:new iP({x:.5,y:0,cursorStyleHandler:iQ,actionHandler:iZ,getActionName:iJ}),mb:new iP({x:0,y:.5,cursorStyleHandler:iQ,actionHandler:i$,getActionName:iJ}),mt:new iP({x:0,y:-.5,cursorStyleHandler:iQ,actionHandler:i$,getActionName:iJ}),tl:new iP({x:-.5,y:-.5,cursorStyleHandler:iI,actionHandler:iX}),tr:new iP({x:.5,y:-.5,cursorStyleHandler:iI,actionHandler:iX}),bl:new iP({x:-.5,y:.5,cursorStyleHandler:iI,actionHandler:iX}),br:new iP({x:.5,y:.5,cursorStyleHandler:iI,actionHandler:iX}),mtr:new iP({x:0,y:-.5,actionHandler:ij,cursorStyleHandler:iA,offsetY:-40,withConnection:!0,actionName:X})}),i1=()=>({mr:new iP({x:.5,y:0,actionHandler:ik,cursorStyleHandler:iQ,actionName:W}),ml:new iP({x:-.5,y:0,actionHandler:ik,cursorStyleHandler:iQ,actionName:W})}),i2=()=>l(l({},i0()),i1());class i5 extends iT{static getDefaults(){return l(l({},super.getDefaults()),i5.ownDefaults)}constructor(t){super(),Object.assign(this,this.constructor.createControls(),i5.ownDefaults),this.setOptions(t)}static createControls(){return{controls:i0()}}_updateCacheCanvas(){let t=this.canvas;if(this.noScaleCache&&t&&t._currentTransform){let e=t._currentTransform,i=e.target,r=e.action;if(this===i&&r&&r.startsWith(z))return!1}return super._updateCacheCanvas()}getActiveControl(){let t=this.__corner;return t?{key:t,control:this.controls[t],coord:this.oCoords[t]}:void 0}findControl(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.hasControls||!this.canvas)return;this.__corner=void 0;let i=Object.entries(this.oCoords);for(let r=i.length-1;r>=0;r--){let[s,n]=i[r],o=this.controls[s];if(o.shouldActivate(s,this,t,e?n.touchCorner:n.corner))return this.__corner=s,{key:s,control:o,coord:this.oCoords[s]}}}calcOCoords(){let t=this.getViewportTransform(),e=this.getCenterPoint(),i=tw(tk(e.x,e.y),tM({angle:this.getTotalAngle()-(this.group&&this.flipX?180:0)})),r=tw(t,i),s=tw(r,[1/t[0],0,0,1/t[3],0,0]),n=this.group?tD(this.calcTransformMatrix()):void 0;n&&(n.scaleX=Math.abs(n.scaleX),n.scaleY=Math.abs(n.scaleY));let o=this._calculateCurrentDimensions(n),a={};return this.forEachControl((t,e)=>{let i=t.positionHandler(o,s,this,t);a[e]=Object.assign(i,this._calcCornerCoords(t,i))}),a}_calcCornerCoords(t,e){let i=this.getTotalAngle();return{corner:t.calcCornerCoords(i,this.cornerSize,e.x,e.y,!1,this),touchCorner:t.calcCornerCoords(i,this.touchCornerSize,e.x,e.y,!0,this)}}setCoords(){super.setCoords(),this.canvas&&(this.oCoords=this.calcOCoords())}forEachControl(t){for(let e in this.controls)t(this.controls[e],e,this)}drawSelectionBackground(t){if(!this.selectionBackgroundColor||this.canvas&&this.canvas._activeObject!==this)return;t.save();let e=this.getRelativeCenterPoint(),i=this._calculateCurrentDimensions(),r=this.getViewportTransform();t.translate(e.x,e.y),t.scale(1/r[0],1/r[3]),t.rotate(ty(this.angle)),t.fillStyle=this.selectionBackgroundColor,t.fillRect(-i.x/2,-i.y/2,i.x,i.y),t.restore()}strokeBorders(t,e){t.strokeRect(-e.x/2,-e.y/2,e.x,e.y)}_drawBorders(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=l({hasControls:this.hasControls,borderColor:this.borderColor,borderDashArray:this.borderDashArray},i);t.save(),t.strokeStyle=r.borderColor,this._setLineDash(t,r.borderDashArray),this.strokeBorders(t,e),r.hasControls&&this.drawControlsConnectingLines(t,e),t.restore()}_renderControls(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{hasBorders:i,hasControls:r}=this,s=l({hasBorders:i,hasControls:r},e),n=this.getViewportTransform(),o=s.hasBorders,a=s.hasControls,h=tD(tw(n,this.calcTransformMatrix()));t.save(),t.translate(h.translateX,h.translateY),t.lineWidth=this.borderScaleFactor,this.group===this.parent&&(t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1),this.flipX&&(h.angle-=180),t.rotate(ty(this.group?h.angle:this.angle)),o&&this.drawBorders(t,h,e),a&&this.drawControls(t,e),t.restore()}drawBorders(t,e,i){let r;if(i&&i.forActiveSelection||this.group){let t=ed(this.width,this.height,tF(e)),i=this.isStrokeAccountedForInDimensions()?to:(this.strokeUniform?(new tn).scalarAdd(this.canvas?this.canvas.getZoom():1):new tn(e.scaleX,e.scaleY)).scalarMultiply(this.strokeWidth);r=t.add(i).scalarAdd(this.borderScaleFactor).scalarAdd(2*this.padding)}else r=this._calculateCurrentDimensions().scalarAdd(this.borderScaleFactor);this._drawBorders(t,r,i)}drawControlsConnectingLines(t,e){let i=!1;t.beginPath(),this.forEachControl((r,s)=>{r.withConnection&&r.getVisibility(this,s)&&(i=!0,t.moveTo(r.x*e.x,r.y*e.y),t.lineTo(r.x*e.x+r.offsetX,r.y*e.y+r.offsetY))}),i&&t.stroke()}drawControls(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t.save();let i=this.getCanvasRetinaScaling(),{cornerStrokeColor:r,cornerDashArray:s,cornerColor:n}=this,o=l({cornerStrokeColor:r,cornerDashArray:s,cornerColor:n},e);t.setTransform(i,0,0,i,0,0),t.strokeStyle=t.fillStyle=o.cornerColor,this.transparentCorners||(t.strokeStyle=o.cornerStrokeColor),this._setLineDash(t,o.cornerDashArray),this.forEachControl((e,i)=>{if(e.getVisibility(this,i)){let r=this.oCoords[i];e.render(t,r.x,r.y,o,this)}}),t.restore()}isControlVisible(t){return this.controls[t]&&this.controls[t].getVisibility(this,t)}setControlVisible(t,e){this._controlsVisibility||(this._controlsVisibility={}),this._controlsVisibility[t]=e}setControlsVisibility(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Object.entries(t).forEach(t=>{let[e,i]=t;return this.setControlVisible(e,i)})}clearContextTop(t){if(!this.canvas)return;let e=this.canvas.contextTop;if(!e)return;let i=this.canvas.viewportTransform;e.save(),e.transform(i[0],i[1],i[2],i[3],i[4],i[5]),this.transform(e);let r=this.width+4,s=this.height+4;return e.clearRect(-r/2,-s/2,r,s),t||e.restore(),e}onDeselect(t){return!1}onSelect(t){return!1}shouldStartDragging(t){return!1}onDragStart(t){return!1}canDrop(t){return!1}renderDragSourceEffect(t){}renderDropTargetEffect(t){}}function i3(t,e){return e.forEach(e=>{Object.getOwnPropertyNames(e.prototype).forEach(i=>{"constructor"!==i&&Object.defineProperty(t.prototype,i,Object.getOwnPropertyDescriptor(e.prototype,i)||Object.create(null))})}),t}o(i5,"ownDefaults",{noScaleCache:!0,lockMovementX:!1,lockMovementY:!1,lockRotation:!1,lockScalingX:!1,lockScalingY:!1,lockSkewingX:!1,lockSkewingY:!1,lockScalingFlip:!1,cornerSize:13,touchCornerSize:24,transparentCorners:!0,cornerColor:"rgb(178,204,255)",cornerStrokeColor:"",cornerStyle:"rect",cornerDashArray:null,hasControls:!0,borderColor:"rgb(178,204,255)",borderDashArray:null,borderOpacityWhenMoving:.4,borderScaleFactor:1,hasBorders:!0,selectionBackgroundColor:"",selectable:!0,evented:!0,perPixelTargetFind:!1,activeOn:"down",hoverCursor:null,moveCursor:null});class i4 extends i5{}i3(i4,[eD]),$.setClass(i4),$.setClass(i4,"object");let i8=(t,e,i,r)=>{let s=2*(r=Math.round(r))+1,{data:n}=t.getImageData(e-r,i-r,s,s);for(let t=3;t<n.length;t+=4)if(n[t]>0)return!1;return!0};class i6{constructor(t){this.options=t,this.strokeProjectionMagnitude=this.options.strokeWidth/2,this.scale=new tn(this.options.scaleX,this.options.scaleY),this.strokeUniformScalar=this.options.strokeUniform?new tn(1/this.options.scaleX,1/this.options.scaleY):new tn(1,1)}createSideVector(t,e){let i=eV(t,e);return this.options.strokeUniform?i.multiply(this.scale):i}projectOrthogonally(t,e,i){return this.applySkew(t.add(this.calcOrthogonalProjection(t,e,i)))}isSkewed(){return 0!==this.options.skewX||0!==this.options.skewY}applySkew(t){let e=new tn(t);return e.y+=e.x*Math.tan(ty(this.options.skewY)),e.x+=e.y*Math.tan(ty(this.options.skewX)),e}scaleUnitVector(t,e){return t.multiply(this.strokeUniformScalar).scalarMultiply(e)}}let i9=new tn;class i7 extends i6{static getOrthogonalRotationFactor(t,e){return Math.abs(e?ez(t,e):eG(t))<O?-1:1}constructor(t,e,i,r){super(r),o(this,"AB",void 0),o(this,"AC",void 0),o(this,"alpha",void 0),o(this,"bisector",void 0),this.A=new tn(t),this.B=new tn(e),this.C=new tn(i),this.AB=this.createSideVector(this.A,this.B),this.AC=this.createSideVector(this.A,this.C),this.alpha=ez(this.AB,this.AC),this.bisector=eN(eW(this.AB.eq(i9)?this.AC:this.AB,this.alpha/2))}calcOrthogonalProjection(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.strokeProjectionMagnitude,r=eU(this.createSideVector(t,e)),s=i7.getOrthogonalRotationFactor(r,this.bisector);return this.scaleUnitVector(r,i*s)}projectBevel(){let t=[];return(this.alpha%D==0?[this.B]:[this.B,this.C]).forEach(e=>{t.push(this.projectOrthogonally(this.A,e)),t.push(this.projectOrthogonally(this.A,e,-this.strokeProjectionMagnitude))}),t}projectMiter(){let t=[],e=1/Math.sin(Math.abs(this.alpha)/2),i=this.scaleUnitVector(this.bisector,-this.strokeProjectionMagnitude*e),r=this.options.strokeUniform?eH(this.scaleUnitVector(this.bisector,this.options.strokeMiterLimit)):this.options.strokeMiterLimit;return eH(i)/this.strokeProjectionMagnitude<=r&&t.push(this.applySkew(this.A.add(i))),t.push(...this.projectBevel()),t}projectRoundNoSkew(t,e){let i=[],r=new tn(i7.getOrthogonalRotationFactor(this.bisector),i7.getOrthogonalRotationFactor(new tn(this.bisector.y,this.bisector.x)));return[new tn(1,0).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(r),new tn(0,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(r)].forEach(r=>{eJ(r,t,e)&&i.push(this.A.add(r))}),i}projectRoundWithSkew(t,e){let i=[],{skewX:r,skewY:s,scaleX:n,scaleY:o,strokeUniform:a}=this.options,l=new tn(Math.tan(ty(r)),Math.tan(ty(s))),h=this.strokeProjectionMagnitude,c=a?h/o/Math.sqrt(1/o**2+1/n**2*l.y**2):h/Math.sqrt(1+l.y**2),u=new tn(Math.sqrt(Math.max(h**2-c**2,0)),c),d=a?h/Math.sqrt(1+l.x**2*(1/o)**2/(1/n+1/n*l.x*l.y)**2):h/Math.sqrt(1+l.x**2/(1+l.x*l.y)**2),g=new tn(d,Math.sqrt(Math.max(h**2-d**2,0)));return[g,g.scalarMultiply(-1),u,u.scalarMultiply(-1)].map(t=>this.applySkew(a?t.multiply(this.strokeUniformScalar):t)).forEach(r=>{eJ(r,t,e)&&i.push(this.applySkew(this.A).add(r))}),i}projectRound(){let t=[];t.push(...this.projectBevel());let e=this.alpha%D==0,i=this.applySkew(this.A),r=t[2*!e].subtract(i),s=t[+!!e].subtract(i),n=eq(r,e?this.applySkew(this.AB.scalarMultiply(-1)):this.applySkew(this.bisector.multiply(this.strokeUniformScalar).scalarMultiply(-1)))>0,o=n?r:s,a=n?s:r;return this.isSkewed()?t.push(...this.projectRoundWithSkew(o,a)):t.push(...this.projectRoundNoSkew(o,a)),t}projectPoints(){switch(this.options.strokeLineJoin){case"miter":return this.projectMiter();case"round":return this.projectRound();default:return this.projectBevel()}}project(){return this.projectPoints().map(t=>({originPoint:this.A,projectedPoint:t,angle:this.alpha,bisector:this.bisector}))}}class rt extends i6{constructor(t,e,i){super(i),this.A=new tn(t),this.T=new tn(e)}calcOrthogonalProjection(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.strokeProjectionMagnitude,r=this.createSideVector(t,e);return this.scaleUnitVector(eU(r),i)}projectButt(){return[this.projectOrthogonally(this.A,this.T,this.strokeProjectionMagnitude),this.projectOrthogonally(this.A,this.T,-this.strokeProjectionMagnitude)]}projectRound(){let t=[];if(!this.isSkewed()&&this.A.eq(this.T)){let e=new tn(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);t.push(this.applySkew(this.A.add(e)),this.applySkew(this.A.subtract(e)))}else t.push(...new i7(this.A,this.T,this.T,this.options).projectRound());return t}projectSquare(){let t=[];if(this.A.eq(this.T)){let e=new tn(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);t.push(this.A.add(e),this.A.subtract(e))}else{let e=this.calcOrthogonalProjection(this.A,this.T,this.strokeProjectionMagnitude),i=this.scaleUnitVector(eN(this.createSideVector(this.A,this.T)),-this.strokeProjectionMagnitude),r=this.A.add(i);t.push(r.add(e),r.subtract(e))}return t.map(t=>this.applySkew(t))}projectPoints(){switch(this.options.strokeLineCap){case"round":return this.projectRound();case"square":return this.projectSquare();default:return this.projectButt()}}project(){return this.projectPoints().map(t=>({originPoint:this.A,projectedPoint:t}))}}let re=function(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=[];if(0===t.length)return r;let s=t.reduce((t,e)=>(t[t.length-1].eq(e)||t.push(new tn(e)),t),[new tn(t[0])]);if(1===s.length)i=!0;else if(!i){let t=s[0],e=((t,e)=>{for(let i=t.length-1;i>=0;i--)if(e(t[i],i,t))return i;return -1})(s,e=>!e.eq(t));s.splice(e+1)}return s.forEach((t,s,n)=>{let o,a;0===s?(a=n[1],o=i?t:n[n.length-1]):s===n.length-1?(o=n[s-1],a=i?t:n[0]):(o=n[s-1],a=n[s+1]),i&&1===n.length?r.push(...new rt(t,t,e).project()):i&&(0===s||s===n.length-1)?r.push(...new rt(t,0===s?a:o,e).project()):r.push(...new i7(t,o,a,e).project())}),r},ri=t=>{let e={};return Object.keys(t).forEach(i=>{e[i]={},Object.keys(t[i]).forEach(r=>{e[i][r]=l({},t[i][r])})}),e},rr=t=>t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),rs=t=>{let e=[];for(let i,r=0;r<t.length;r++)!1!==(i=rn(t,r))&&e.push(i);return e},rn=(t,e)=>{let i=t.charCodeAt(e);if(isNaN(i))return"";if(i<55296||i>57343)return t.charAt(e);if(55296<=i&&i<=56319){if(t.length<=e+1)throw"High surrogate without following low surrogate";let i=t.charCodeAt(e+1);if(56320>i||i>57343)throw"High surrogate without following low surrogate";return t.charAt(e)+t.charAt(e+1)}if(0===e)throw"Low surrogate without preceding high surrogate";let r=t.charCodeAt(e-1);if(55296>r||r>56319)throw"Low surrogate without preceding high surrogate";return!1};var ro=Object.freeze({__proto__:null,capitalize:function(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"".concat(t.charAt(0).toUpperCase()).concat(e?t.slice(1):t.slice(1).toLowerCase())},escapeXml:rr,graphemeSplit:rs});let ra=function(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t.fill!==e.fill||t.stroke!==e.stroke||t.strokeWidth!==e.strokeWidth||t.fontSize!==e.fontSize||t.fontFamily!==e.fontFamily||t.fontWeight!==e.fontWeight||t.fontStyle!==e.fontStyle||t.textBackgroundColor!==e.textBackgroundColor||t.deltaY!==e.deltaY||i&&(t.overline!==e.overline||t.underline!==e.underline||t.linethrough!==e.linethrough)},rl=(t,e)=>{let i=e.split("\n"),r=[],s=-1,n={};t=ri(t);for(let e=0;e<i.length;e++){let o=rs(i[e]);if(t[e])for(let i=0;i<o.length;i++){s++;let o=t[e][i];o&&Object.keys(o).length>0&&(ra(n,o,!0)?r.push({start:s,end:s+1,style:o}):r[r.length-1].end++),n=o||{}}else s+=o.length,n={}}return r},rh=(t,e)=>{if(!Array.isArray(t))return ri(t);let i=e.split(L),r={},s=-1,n=0;for(let e=0;e<i.length;e++){let o=rs(i[e]);for(let i=0;i<o.length;i++)s++,t[n]&&t[n].start<=s&&s<t[n].end&&(r[e]=r[e]||{},r[e][i]=l({},t[n].style),s===t[n].end-1&&n++)}return r},rc=["display","transform",K,"fill-opacity","fill-rule","opacity",J,"stroke-dasharray","stroke-linecap","stroke-dashoffset","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","id","paint-order","vector-effect","instantiated_by_use","clip-path"];function ru(t,e){let i;let r=t.nodeName,s=t.getAttribute("class"),n=t.getAttribute("id"),o="(?![a-zA-Z\\-]+)";if(i=RegExp("^"+r,"i"),e=e.replace(i,""),n&&e.length&&(i=RegExp("#"+n+o,"i"),e=e.replace(i,"")),s&&e.length){let t=s.split(" ");for(let r=t.length;r--;)i=RegExp("\\."+t[r]+o,"i"),e=e.replace(i,"")}return 0===e.length}let rd=t=>{var e;return null!==(e=eA[t])&&void 0!==e?e:t},rg=RegExp("(".concat(eM,")"),"gi"),rf=t=>t.replace(rg," $1 ").replace(/,/gi," ").replace(/\s+/gi," "),rp="(".concat(eM,")"),rm=String.raw(e7||(e7=c(["(skewX)(",")"],["(skewX)\\(","\\)"])),rp),rv=String.raw(it||(it=c(["(skewY)(",")"],["(skewY)\\(","\\)"])),rp),r_=String.raw(ie||(ie=c(["(rotate)(","(?: "," ",")?)"],["(rotate)\\(","(?: "," ",")?\\)"])),rp,rp,rp),ry=String.raw(ii||(ii=c(["(scale)(","(?: ",")?)"],["(scale)\\(","(?: ",")?\\)"])),rp,rp),rx=String.raw(ir||(ir=c(["(translate)(","(?: ",")?)"],["(translate)\\(","(?: ",")?\\)"])),rp,rp),rC=String.raw(is||(is=c(["(matrix)("," "," "," "," "," ",")"],["(matrix)\\("," "," "," "," "," ","\\)"])),rp,rp,rp,rp,rp,rp),rb="(?:".concat(rC,"|").concat(rx,"|").concat(r_,"|").concat(ry,"|").concat(rm,"|").concat(rv,")"),rS=new RegExp(String.raw(io||(io=c(["^s*(?:","?)s*$"],["^\\s*(?:","?)\\s*$"])),"(?:".concat(rb,"*)"))),rw=new RegExp(rb),rT=RegExp(rb,"g");function rO(t){let e=[];if(!(t=rf(t).replace(/\s*([()])\s*/gi,"$1"))||t&&!rS.test(t))return[...M];for(let i of t.matchAll(rT)){let t=rw.exec(i[0]);if(!t)continue;let r=M,[,s,...n]=t.filter(t=>!!t),[o,a,l,h,c,u]=n.map(t=>parseFloat(t));switch(s){case"translate":r=tk(o,a);break;case X:r=tM({angle:o},{x:a,y:l});break;case z:r=tE(o,a);break;case U:r=tA(o);break;case q:r=tj(o);break;case"matrix":r=[o,a,l,h,c,u]}e.push(r)}return tT(e)}function rD(t,e){t.replace(/;\s*$/,"").split(";").forEach(t=>{if(!t)return;let[i,r]=t.split(":");e[i.trim().toLowerCase()]=r.trim()})}let rk={stroke:"strokeOpacity",fill:"fillOpacity"};function rM(t,e,i){if(!t)return{};let r,s={},n=16;t.parentNode&&eI.test(t.parentNode.nodeName)&&(s=rM(t.parentElement,e,i)).fontSize&&(r=n=tK(s.fontSize));let o=l(l(l({},e.reduce((e,i)=>{let r=t.getAttribute(i);return r&&(e[i]=r),e},{})),function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i={};for(let r in e)(function(t,e){let i=!0,r=ru(t,e.pop());return r&&e.length&&(i=function(t,e){let i,r=!0;for(;t.parentElement&&1===t.parentElement.nodeType&&e.length;)r&&(i=e.pop()),r=ru(t=t.parentElement,i);return 0===e.length}(t,e)),r&&i&&0===e.length})(t,r.split(" "))&&(i=l(l({},i),e[r]));return i}(t,i)),function(t){let e={},i=t.getAttribute("style");return i&&("string"==typeof i?rD(i,e):function(t,e){Object.entries(t).forEach(t=>{let[i,r]=t;void 0!==r&&(e[i.toLowerCase()]=r)})}(i,e)),e}(t));o[eF]&&t.setAttribute(eF,o[eF]),o[ej]&&(r=tK(o[ej],n),o[ej]="".concat(r));let a={};for(let t in o){let e=rd(t),i=function(t,e,i,r){let s=Array.isArray(e),n,o=e;if(t!==K&&t!==J||e!==F){if("strokeUniform"===t)return"non-scaling-stroke"===e;if("strokeDashArray"===t)o=e===F?null:e.replace(/,/g," ").split(/\s+/).map(parseFloat);else if("transformMatrix"===t)o=i&&i.transformMatrix?tw(i.transformMatrix,rO(e)):rO(e);else if("visible"===t)o=e!==F&&"hidden"!==e,i&&!1===i.visible&&(o=!1);else if("opacity"===t)o=parseFloat(e),i&&void 0!==i.opacity&&(o*=i.opacity);else if("textAnchor"===t)o="start"===e?P:"end"===e?j:E;else if("charSpacing"===t)n=tK(e,r)/r*1e3;else if("paintFirst"===t){let t=e.indexOf(K),i=e.indexOf(J);o=K,(t>-1&&i>-1&&i<t||-1===t&&i>-1)&&(o=J)}else{if("href"===t||"xlink:href"===t||"font"===t||"id"===t)return e;if("imageSmoothing"===t)return"optimizeQuality"===e;n=s?e.map(tK):tK(e,r)}}else o="";return!s&&isNaN(n)?o:n}(e,o[t],s,r);a[e]=i}a&&a.font&&function(t,e){let i=t.match(eP);if(!i)return;let r=i[1],s=i[3],n=i[4],o=i[5],a=i[6];r&&(e.fontStyle=r),s&&(e.fontWeight=isNaN(parseFloat(s))?s:parseFloat(s)),n&&(e.fontSize=tK(n)),a&&(e.fontFamily=a),o&&(e.lineHeight="normal"===o?1:o)}(a.font,a);let h=l(l({},s),a);return eI.test(t.nodeName)?h:function(t){let e=i4.getDefaults();return Object.entries(rk).forEach(i=>{let[r,s]=i;if(void 0===t[s]||""===t[r])return;if(void 0===t[r]){if(!e[r])return;t[r]=e[r]}if(0===t[r].indexOf("url("))return;let n=new tU(t[r]);t[r]=n.setAlpha(tq(n.getAlpha()*t[s],2)).toRgba()}),t}(h)}let rE=["left","top","width","height","visible"],rP=["rx","ry"];class rA extends i4{static getDefaults(){return l(l({},super.getDefaults()),rA.ownDefaults)}constructor(t){super(),Object.assign(this,rA.ownDefaults),this.setOptions(t),this._initRxRy()}_initRxRy(){let{rx:t,ry:e}=this;t&&!e?this.ry=t:e&&!t&&(this.rx=e)}_render(t){let{width:e,height:i}=this,r=-e/2,s=-i/2,n=this.rx?Math.min(this.rx,e/2):0,o=this.ry?Math.min(this.ry,i/2):0,a=0!==n||0!==o;t.beginPath(),t.moveTo(r+n,s),t.lineTo(r+e-n,s),a&&t.bezierCurveTo(r+e-.4477152502*n,s,r+e,s+.4477152502*o,r+e,s+o),t.lineTo(r+e,s+i-o),a&&t.bezierCurveTo(r+e,s+i-.4477152502*o,r+e-.4477152502*n,s+i,r+e-n,s+i),t.lineTo(r+n,s+i),a&&t.bezierCurveTo(r+.4477152502*n,s+i,r,s+i-.4477152502*o,r,s+i-o),t.lineTo(r,s+o),a&&t.bezierCurveTo(r,s+.4477152502*o,r+.4477152502*n,s,r+n,s),t.closePath(),this._renderPaintInOrder(t)}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return super.toObject([...rP,...t])}_toSVG(){let{width:t,height:e,rx:i,ry:r}=this;return["<rect ","COMMON_PARTS",'x="'.concat(-t/2,'" y="').concat(-e/2,'" rx="').concat(i,'" ry="').concat(r,'" width="').concat(t,'" height="').concat(e,'" />\n')]}static async fromElement(t,e,i){let r=rM(t,this.ATTRIBUTE_NAMES,i),{left:s=0,top:n=0,width:o=0,height:a=0,visible:c=!0}=r,u=h(r,rE);return new this(l(l(l({},e),u),{},{left:s,top:n,width:o,height:a,visible:!!(c&&o&&a)}))}}o(rA,"type","Rect"),o(rA,"cacheProperties",[...e2,...rP]),o(rA,"ownDefaults",{rx:0,ry:0}),o(rA,"ATTRIBUTE_NAMES",[...rc,"x","y","rx","ry","width","height"]),$.setClass(rA),$.setSVGClass(rA);let rj="initialization",rF="added",rL="removed",rR="imperative",rI=(t,e)=>{let{strokeUniform:i,strokeWidth:r,width:s,height:n,group:o}=e,a=o&&o!==t?eg(o.calcTransformMatrix(),t.calcTransformMatrix()):null,l=a?e.getRelativeCenterPoint().transform(a):e.getRelativeCenterPoint(),h=!e.isStrokeAccountedForInDimensions(),c=i&&h?ep(new tn(r,r),void 0,t.calcTransformMatrix()):to,u=!i&&h?r:0,d=ed(s+u,n+u,tT([a,e.calcOwnMatrix()],!0)).add(c).scalarDivide(2);return[l.subtract(d),l.add(d)]};class rB{calcLayoutResult(t,e){if(this.shouldPerformLayout(t))return this.calcBoundingBox(e,t)}shouldPerformLayout(t){let{type:e,prevStrategy:i,strategy:r}=t;return e===rj||e===rR||!!i&&r!==i}shouldLayoutClipPath(t){let{type:e,target:{clipPath:i}}=t;return e!==rj&&i&&!i.absolutePositioned}getInitialSize(t,e){return e.size}calcBoundingBox(t,e){let{type:i,target:r}=e;if(i===rR&&e.overrides)return e.overrides;if(0===t.length)return;let{left:s,top:n,width:o,height:a}=eo(t.map(t=>rI(r,t)).reduce((t,e)=>t.concat(e),[])),l=new tn(o,a),h=new tn(s,n).add(l.scalarDivide(2));if(i===rj){let t=this.getInitialSize(e,{size:l,center:h});return{center:h,relativeCorrection:new tn(0,0),size:t}}return{center:h.transform(r.calcOwnMatrix()),size:l}}}o(rB,"type","strategy");class rX extends rB{shouldPerformLayout(t){return!0}}o(rX,"type","fit-content"),$.setClass(rX);let rY=["strategy"],rW=["target","strategy","bubbles","prevStrategy"],rV="layoutManager";class rH{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new rX;o(this,"strategy",void 0),this.strategy=t,this._subscriptions=new Map}performLayout(t){let e=l(l({bubbles:!0,strategy:this.strategy},t),{},{prevStrategy:this._prevLayoutStrategy,stopPropagation(){this.bubbles=!1}});this.onBeforeLayout(e);let i=this.getLayoutResult(e);i&&this.commitLayout(e,i),this.onAfterLayout(e,i),this._prevLayoutStrategy=e.strategy}attachHandlers(t,e){let{target:i}=e;return[Q,R,W,B,I,Y,H,V,"modifyPath"].map(e=>t.on(e,t=>this.performLayout(e===Q?{type:"object_modified",trigger:e,e:t,target:i}:{type:"object_modifying",trigger:e,e:t,target:i})))}subscribe(t,e){this.unsubscribe(t,e);let i=this.attachHandlers(t,e);this._subscriptions.set(t,i)}unsubscribe(t,e){(this._subscriptions.get(t)||[]).forEach(t=>t()),this._subscriptions.delete(t)}unsubscribeTargets(t){t.targets.forEach(e=>this.unsubscribe(e,t))}subscribeTargets(t){t.targets.forEach(e=>this.subscribe(e,t))}onBeforeLayout(t){let{target:e,type:i}=t,{canvas:r}=e;if(i===rj||i===rF?this.subscribeTargets(t):i===rL&&this.unsubscribeTargets(t),e.fire("layout:before",{context:t}),r&&r.fire("object:layout:before",{target:e,context:t}),i===rR&&t.deep){let i=h(t,rY);e.forEachObject(t=>t.layoutManager&&t.layoutManager.performLayout(l(l({},i),{},{bubbles:!1,target:t})))}}getLayoutResult(t){let{target:e,strategy:i,type:r}=t,s=i.calcLayoutResult(t,e.getObjects());if(!s)return;let n=r===rj?new tn:e.getRelativeCenterPoint(),{center:o,correction:a=new tn,relativeCorrection:l=new tn}=s,h=n.subtract(o).add(a).transform(r===rj?M:tS(e.calcOwnMatrix()),!0).add(l);return{result:s,prevCenter:n,nextCenter:o,offset:h}}commitLayout(t,e){var i,r;let{target:s}=t,{result:{size:n},nextCenter:o}=e;(s.set({width:n.x,height:n.y}),this.layoutObjects(t,e),t.type===rj)?s.set({left:null!==(i=t.x)&&void 0!==i?i:o.x+n.x*ey(s.originX),top:null!==(r=t.y)&&void 0!==r?r:o.y+n.y*ey(s.originY)}):(s.setPositionByOrigin(o,E,E),s.setCoords(),s.set("dirty",!0))}layoutObjects(t,e){let{target:i}=t;i.forEachObject(r=>{r.group===i&&this.layoutObject(t,e,r)}),t.strategy.shouldLayoutClipPath(t)&&this.layoutObject(t,e,i.clipPath)}layoutObject(t,e,i){let{offset:r}=e;i.set({left:i.left+r.x,top:i.top+r.y})}onAfterLayout(t,e){let{target:i,strategy:r,bubbles:s,prevStrategy:n}=t,o=h(t,rW),{canvas:a}=i;i.fire("layout:after",{context:t,result:e}),a&&a.fire("object:layout:after",{context:t,result:e,target:i});let c=i.parent;s&&null!=c&&c.layoutManager&&((o.path||(o.path=[])).push(i),c.layoutManager.performLayout(l(l({},o),{},{target:c}))),i.set("dirty",!0)}dispose(){let{_subscriptions:t}=this;t.forEach(t=>t.forEach(t=>t())),t.clear()}toObject(){return{type:rV,strategy:this.strategy.constructor.type}}toJSON(){return this.toObject()}}$.setClass(rH,rV);let rz=["type","objects","layoutManager"];class rG extends rH{performLayout(){}}class rN extends tl(i4){static getDefaults(){return l(l({},super.getDefaults()),rN.ownDefaults)}constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),o(this,"_activeObjects",[]),o(this,"__objectSelectionTracker",void 0),o(this,"__objectSelectionDisposer",void 0),Object.assign(this,rN.ownDefaults),this.setOptions(e),this.groupInit(t,e)}groupInit(t,e){var i;this._objects=[...t],this.__objectSelectionTracker=this.__objectSelectionMonitor.bind(this,!0),this.__objectSelectionDisposer=this.__objectSelectionMonitor.bind(this,!1),this.forEachObject(t=>{this.enterGroup(t,!1)}),this.layoutManager=null!==(i=e.layoutManager)&&void 0!==i?i:new rH,this.layoutManager.performLayout({type:rj,target:this,targets:[...t],x:e.left,y:e.top})}canEnterGroup(t){return t===this||this.isDescendantOf(t)?(g("error","Group: circular object trees are not supported, this call has no effect"),!1):-1===this._objects.indexOf(t)||(g("error","Group: duplicate objects are not supported inside group, this call has no effect"),!1)}_filterObjectsBeforeEnteringGroup(t){return t.filter((t,e,i)=>this.canEnterGroup(t)&&i.indexOf(t)===e)}add(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];let r=this._filterObjectsBeforeEnteringGroup(e),s=super.add(...r);return this._onAfterObjectsChange(rF,r),s}insertAt(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];let s=this._filterObjectsBeforeEnteringGroup(i),n=super.insertAt(t,...s);return this._onAfterObjectsChange(rF,s),n}remove(){let t=super.remove(...arguments);return this._onAfterObjectsChange(rL,t),t}_onObjectAdded(t){this.enterGroup(t,!0),this.fire("object:added",{target:t}),t.fire("added",{target:this})}_onObjectRemoved(t,e){this.exitGroup(t,e),this.fire("object:removed",{target:t}),t.fire("removed",{target:this})}_onAfterObjectsChange(t,e){this.layoutManager.performLayout({type:t,targets:e,target:this})}_onStackOrderChanged(){this._set("dirty",!0)}_set(t,e){let i=this[t];return super._set(t,e),"canvas"===t&&i!==e&&(this._objects||[]).forEach(i=>{i._set(t,e)}),this}_shouldSetNestedCoords(){return this.subTargetCheck}removeAll(){return this._activeObjects=[],this.remove(...this._objects)}__objectSelectionMonitor(t,e){let{target:i}=e,r=this._activeObjects;if(t)r.push(i),this._set("dirty",!0);else if(r.length>0){let t=r.indexOf(i);t>-1&&(r.splice(t,1),this._set("dirty",!0))}}_watchObject(t,e){t&&this._watchObject(!1,e),t?(e.on("selected",this.__objectSelectionTracker),e.on("deselected",this.__objectSelectionDisposer)):(e.off("selected",this.__objectSelectionTracker),e.off("deselected",this.__objectSelectionDisposer))}enterGroup(t,e){t.group&&t.group.remove(t),t._set("parent",this),this._enterGroup(t,e)}_enterGroup(t,e){e&&eh(t,tw(tS(this.calcTransformMatrix()),t.calcTransformMatrix())),this._shouldSetNestedCoords()&&t.setCoords(),t._set("group",this),t._set("canvas",this.canvas),this._watchObject(!0,t);let i=this.canvas&&this.canvas.getActiveObject&&this.canvas.getActiveObject();i&&(i===t||t.isDescendantOf(i))&&this._activeObjects.push(t)}exitGroup(t,e){this._exitGroup(t,e),t._set("parent",void 0),t._set("canvas",void 0)}_exitGroup(t,e){t._set("group",void 0),e||(eh(t,tw(this.calcTransformMatrix(),t.calcTransformMatrix())),t.setCoords()),this._watchObject(!1,t);let i=this._activeObjects.length>0?this._activeObjects.indexOf(t):-1;i>-1&&this._activeObjects.splice(i,1)}shouldCache(){let t=i4.prototype.shouldCache.call(this);if(t){for(let t=0;t<this._objects.length;t++)if(this._objects[t].willDrawShadow())return this.ownCaching=!1,!1}return t}willDrawShadow(){if(super.willDrawShadow())return!0;for(let t=0;t<this._objects.length;t++)if(this._objects[t].willDrawShadow())return!0;return!1}isOnACache(){return this.ownCaching||!!this.parent&&this.parent.isOnACache()}drawObject(t,e,i){this._renderBackground(t);for(let e=0;e<this._objects.length;e++){var r;let i=this._objects[e];null!==(r=this.canvas)&&void 0!==r&&r.preserveObjectStacking&&i.group!==this?(t.save(),t.transform(...tS(this.calcTransformMatrix())),i.render(t),t.restore()):i.group===this&&i.render(t)}this._drawClipPath(t,this.clipPath,i)}setCoords(){super.setCoords(),this._shouldSetNestedCoords()&&this.forEachObject(t=>t.setCoords())}triggerLayout(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.layoutManager.performLayout(l({target:this,type:rR},t))}render(t){this._transformDone=!0,super.render(t),this._transformDone=!1}__serializeObjects(t,e){let i=this.includeDefaultValues;return this._objects.filter(function(t){return!t.excludeFromExport}).map(function(r){let s=r.includeDefaultValues;r.includeDefaultValues=i;let n=r[t||"toObject"](e);return r.includeDefaultValues=s,n})}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=this.layoutManager.toObject();return l(l(l({},super.toObject(["subTargetCheck","interactive",...t])),"fit-content"!==e.strategy||this.includeDefaultValues?{layoutManager:e}:{}),{},{objects:this.__serializeObjects("toObject",t)})}toString(){return"#<Group: (".concat(this.complexity(),")>")}dispose(){this.layoutManager.unsubscribeTargets({targets:this.getObjects(),target:this}),this._activeObjects=[],this.forEachObject(t=>{this._watchObject(!1,t),t.dispose()}),super.dispose()}_createSVGBgRect(t){if(!this.backgroundColor)return"";let e=rA.prototype._toSVG.call(this),i=e.indexOf("COMMON_PARTS");e[i]='for="group" ';let r=e.join("");return t?t(r):r}_toSVG(t){let e=["<g ","COMMON_PARTS"," >\n"],i=this._createSVGBgRect(t);i&&e.push("		",i);for(let i=0;i<this._objects.length;i++)e.push("		",this._objects[i].toSVG(t));return e.push("</g>\n"),e}getSvgStyles(){let t=void 0!==this.opacity&&1!==this.opacity?"opacity: ".concat(this.opacity,";"):"",e=this.visible?"":" visibility: hidden;";return[t,this.getSvgFilter(),e].join("")}toClipPathSVG(t){let e=[],i=this._createSVGBgRect(t);i&&e.push("	",i);for(let i=0;i<this._objects.length;i++)e.push("	",this._objects[i].toClipPathSVG(t));return this._createBaseClipPathSVGMarkup(e,{reviver:t})}static fromObject(t,e){let{type:i,objects:r=[],layoutManager:s}=t,n=h(t,rz);return Promise.all([tI(r,e),tB(n,e)]).then(t=>{let[e,i]=t,r=new this(e,l(l(l({},n),i),{},{layoutManager:new rG}));if(s){let t=$.getClass(s.type),e=$.getClass(s.strategy);r.layoutManager=new t(new e)}else r.layoutManager=new rH;return r.layoutManager.subscribeTargets({type:rj,target:r,targets:r.getObjects()}),r.setCoords(),r})}}o(rN,"type","Group"),o(rN,"ownDefaults",{strokeWidth:0,subTargetCheck:!1,interactive:!1}),$.setClass(rN);let rU=(t,e)=>Math.min(e.width/t.width,e.height/t.height),rq=(t,e)=>Math.max(e.width/t.width,e.height/t.height),rK="\\s*,?\\s*",rJ="".concat(rK,"(").concat(eM,")"),rQ="".concat(rJ).concat(rJ).concat(rJ).concat(rK,"([01])").concat(rK,"([01])").concat(rJ).concat(rJ),rZ={m:"l",M:"L"},r$=(t,e,i,r,s,n,o,a,l,h,c)=>{let u=tr(t),d=ts(t),g=tr(e),f=ts(e),p=i*s*g-r*n*f+o,m=r*s*g+i*n*f+a;return["C",h+l*(-i*s*d-r*n*u),c+l*(-r*s*d+i*n*u),p+l*(i*s*f+r*n*g),m+l*(r*s*f-i*n*g),p,m]},r0=(t,e,i,r)=>{let s=Math.atan2(e,t),n=Math.atan2(r,i);return n>=s?n-s:2*Math.PI-(s-n)};function r1(t,e,i,r,s,n,o,a){let l;if(d.cachesBoundsOfCurve&&(l=[...arguments].join(),S.boundsOfCurveCache[l]))return S.boundsOfCurveCache[l];let h=Math.sqrt,c=Math.abs,u=[],g=[[0,0],[0,0]],f=6*t-12*i+6*s,p=-3*t+9*i-9*s+3*o,m=3*i-3*t;for(let t=0;t<2;++t){if(t>0&&(f=6*e-12*r+6*n,p=-3*e+9*r-9*n+3*a,m=3*r-3*e),1e-12>c(p)){if(1e-12>c(f))continue;let t=-m/f;0<t&&t<1&&u.push(t);continue}let i=f*f-4*m*p;if(i<0)continue;let s=h(i),o=(-f+s)/(2*p);0<o&&o<1&&u.push(o);let l=(-f-s)/(2*p);0<l&&l<1&&u.push(l)}let v=u.length,_=v,y=r4(t,e,i,r,s,n,o,a);for(;v--;){let{x:t,y:e}=y(u[v]);g[0][v]=t,g[1][v]=e}g[0][_]=t,g[1][_]=e,g[0][_+1]=o,g[1][_+1]=a;let x=[new tn(Math.min(...g[0]),Math.min(...g[1])),new tn(Math.max(...g[0]),Math.max(...g[1]))];return d.cachesBoundsOfCurve&&(S.boundsOfCurveCache[l]=x),x}let r2=(t,e,i)=>{let[r,s,n,o,a,l,h,c]=i,u=((t,e,i,r,s,n,o)=>{if(0===i||0===r)return[];let a=0,l=0,h=0,c=Math.PI,u=o*k,d=ts(u),g=tr(u),f=.5*(-g*t-d*e),p=.5*(-g*e+d*t),m=i**2,v=r**2,_=p**2,y=f**2,x=m*v-m*_-v*y,C=Math.abs(i),b=Math.abs(r);if(x<0){let t=Math.sqrt(1-x/(m*v));C*=t,b*=t}else h=(s===n?-1:1)*Math.sqrt(x/(m*_+v*y));let S=h*C*p/b,w=-h*b*f/C,T=g*S-d*w+.5*t,O=d*S+g*w+.5*e,D=r0(1,0,(f-S)/C,(p-w)/b),M=r0((f-S)/C,(p-w)/b,(-f-S)/C,(-p-w)/b);0===n&&M>0?M-=2*c:1===n&&M<0&&(M+=2*c);let E=Math.ceil(Math.abs(M/c*2)),P=[],A=M/E,j=8/3*Math.sin(A/4)*Math.sin(A/4)/Math.sin(A/2),F=D+A;for(let t=0;t<E;t++)P[t]=r$(D,F,g,d,C,b,T,O,j,a,l),a=P[t][5],l=P[t][6],D=F,F+=A;return P})(h-t,c-e,s,n,a,l,o);for(let i=0,r=u.length;i<r;i++)u[i][1]+=t,u[i][2]+=e,u[i][3]+=t,u[i][4]+=e,u[i][5]+=t,u[i][6]+=e;return u},r5=t=>{let e=0,i=0,r=0,s=0,n=[],o,a=0,l=0;for(let h of t){let t;let c=[...h];switch(c[0]){case"l":c[1]+=e,c[2]+=i;case"L":t=["L",e=c[1],i=c[2]];break;case"h":c[1]+=e;case"H":t=["L",e=c[1],i];break;case"v":c[1]+=i;case"V":t=["L",e,i=c[1]];break;case"m":c[1]+=e,c[2]+=i;case"M":e=c[1],i=c[2],r=c[1],s=c[2],t=["M",e,i];break;case"c":c[1]+=e,c[2]+=i,c[3]+=e,c[4]+=i,c[5]+=e,c[6]+=i;case"C":a=c[3],l=c[4],e=c[5],i=c[6],t=["C",c[1],c[2],a,l,e,i];break;case"s":c[1]+=e,c[2]+=i,c[3]+=e,c[4]+=i;case"S":"C"===o?(a=2*e-a,l=2*i-l):(a=e,l=i),e=c[3],i=c[4],a=(t=["C",a,l,c[1],c[2],e,i])[3],l=t[4];break;case"q":c[1]+=e,c[2]+=i,c[3]+=e,c[4]+=i;case"Q":a=c[1],l=c[2],t=["Q",a,l,e=c[3],i=c[4]];break;case"t":c[1]+=e,c[2]+=i;case"T":"Q"===o?(a=2*e-a,l=2*i-l):(a=e,l=i),t=["Q",a,l,e=c[1],i=c[2]];break;case"a":c[6]+=e,c[7]+=i;case"A":r2(e,i,c).forEach(t=>n.push(t)),e=c[6],i=c[7];break;case"z":case"Z":e=r,i=s,t=["Z"]}t?(n.push(t),o=t[0]):o=""}return n},r3=(t,e,i,r)=>Math.sqrt((i-t)**2+(r-e)**2),r4=(t,e,i,r,s,n,o,a)=>l=>{let h=l**3,c=(t=>3*t**2*(1-t))(l),u=(t=>3*t*(1-t)**2)(l),d=(1-l)**3;return new tn(o*h+s*c+i*u+t*d,a*h+n*c+r*u+e*d)},r8=t=>t**2,r6=t=>2*t*(1-t),r9=t=>(1-t)**2,r7=(t,e,i,r,s,n,o,a)=>l=>{let h=r8(l),c=r6(l),u=r9(l);return Math.atan2(3*(u*(r-e)+c*(n-r)+h*(a-n)),3*(u*(i-t)+c*(s-i)+h*(o-s)))},st=(t,e,i,r,s,n)=>o=>{let a=r8(o),l=r6(o),h=r9(o);return new tn(s*a+i*l+t*h,n*a+r*l+e*h)},se=(t,e,i,r,s,n)=>o=>{let a=1-o;return Math.atan2(2*(a*(r-e)+o*(n-r)),2*(a*(i-t)+o*(s-i)))},si=(t,e,i)=>{let r=new tn(e,i),s=0;for(let e=1;e<=100;e+=1){let i=t(e/100);s+=r3(r.x,r.y,i.x,i.y),r=i}return s},sr=(t,e)=>{let i,r=0,s=0,n={x:t.x,y:t.y},o=l({},n),a=.01,h=0,c=t.iterator,u=t.angleFinder;for(;s<e&&a>1e-4;)o=c(r),h=r,(i=r3(n.x,n.y,o.x,o.y))+s>e?(r-=a,a/=2):(n=o,r+=a,s+=i);return l(l({},o),{},{angle:u(h)})},ss=t=>{let e,i,r=0,s=0,n=0,o=0,a=0,l=[];for(let h of t){let t={x:s,y:n,command:h[0],length:0};switch(h[0]){case"M":(i=t).x=o=s=h[1],i.y=a=n=h[2];break;case"L":(i=t).length=r3(s,n,h[1],h[2]),s=h[1],n=h[2];break;case"C":e=r4(s,n,h[1],h[2],h[3],h[4],h[5],h[6]),(i=t).iterator=e,i.angleFinder=r7(s,n,h[1],h[2],h[3],h[4],h[5],h[6]),i.length=si(e,s,n),s=h[5],n=h[6];break;case"Q":e=st(s,n,h[1],h[2],h[3],h[4]),(i=t).iterator=e,i.angleFinder=se(s,n,h[1],h[2],h[3],h[4]),i.length=si(e,s,n),s=h[3],n=h[4];break;case"Z":(i=t).destX=o,i.destY=a,i.length=r3(s,n,o,a),s=o,n=a}r+=i.length,l.push(i)}return l.push({length:r,x:s,y:n}),l},sn=function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ss(t),r=0;for(;e-i[r].length>0&&r<i.length-2;)e-=i[r].length,r++;let s=i[r],n=e/s.length,o=t[r];switch(s.command){case"M":return{x:s.x,y:s.y,angle:0};case"Z":return l(l({},new tn(s.x,s.y).lerp(new tn(s.destX,s.destY),n)),{},{angle:Math.atan2(s.destY-s.y,s.destX-s.x)});case"L":return l(l({},new tn(s.x,s.y).lerp(new tn(o[1],o[2]),n)),{},{angle:Math.atan2(o[2]-s.y,o[1]-s.x)});case"C":case"Q":return sr(s,e)}},so=RegExp("[mzlhvcsqta][^mzlhvcsqta]*","gi"),sa=RegExp(rQ,"g"),sl=RegExp(eM,"gi"),sh={m:2,l:2,h:1,v:1,c:6,s:4,q:4,t:2,a:7},sc=t=>{var e;let i=[];for(let r of null!==(e=t.match(so))&&void 0!==e?e:[]){let t=r[0];if("z"===t||"Z"===t){i.push([t]);continue}let e=sh[t.toLowerCase()],s=[];if("a"===t||"A"===t){sa.lastIndex=0;for(let t=null;t=sa.exec(r);)s.push(...t.slice(1))}else s=r.match(sl)||[];for(let r=0;r<s.length;r+=e){let n=Array(e),o=rZ[t];n[0]=r>0&&o?o:t;for(let t=0;t<e;t++)n[t+1]=parseFloat(s[r+t]);i.push(n)}}return i},su=function(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=new tn(t[0]),s=new tn(t[1]),n=1,o=0,a=[],l=t.length,h=l>2;for(h&&(n=t[2].x<s.x?-1:+(t[2].x!==s.x),o=t[2].y<s.y?-1:+(t[2].y!==s.y)),a.push(["M",r.x-n*i,r.y-o*i]),e=1;e<l;e++){if(!r.eq(s)){let t=r.midPointFrom(s);a.push(["Q",r.x,r.y,t.x,t.y])}r=t[e],e+1<t.length&&(s=t[e+1])}return h&&(n=r.x>t[e-2].x?1:r.x===t[e-2].x?0:-1,o=r.y>t[e-2].y?1:r.y===t[e-2].y?0:-1),a.push(["L",r.x+n*i,r.y+o*i]),a},sd=(t,e)=>t.map(t=>t.map((t,i)=>0===i||void 0===e?t:tq(t,e)).join(" ")).join(" ");function sg(t,e){let i=t.style;i&&e&&("string"==typeof e?i.cssText+=";"+e:Object.entries(e).forEach(t=>{let[e,r]=t;return i.setProperty(e,r)}))}let sf=(t,e)=>{let i=t._findCenterFromElement();t.transformMatrix&&((t=>{if(t.transformMatrix){let{scaleX:e,scaleY:i,angle:r,skewX:s}=tD(t.transformMatrix);t.flipX=!1,t.flipY=!1,t.set(G,e),t.set(N,i),t.angle=r,t.skewX=s,t.skewY=0}})(t),i=i.transform(t.transformMatrix)),delete t.transformMatrix,e&&(t.scaleX*=e.scaleX,t.scaleY*=e.scaleY,t.cropX=e.cropX,t.cropY=e.cropY,i.x+=e.offsetLeft,i.y+=e.offsetTop,t.width=e.width,t.height=e.height),t.setPositionByOrigin(i,E,E)};Object.freeze({__proto__:null,addTransformToObject:el,animate:iy,animateColor:ix,applyTransformToObject:eh,calcAngleBetweenVectors:ez,calcDimensionsMatrix:tF,calcPlaneChangeMatrix:eg,calcVectorRotation:eG,cancelAnimFrame:tu,capValue:e0,composeMatrix:tL,copyCanvasElement:t=>{var e;let i=tm(t);return null===(e=i.getContext("2d"))||void 0===e||e.drawImage(t,0,0),i},cos:tr,createCanvasElement:tf,createImage:tp,createRotateMatrix:tM,createScaleMatrix:tE,createSkewXMatrix:tA,createSkewYMatrix:tj,createTranslateMatrix:tk,createVector:eV,crossProduct:eq,degreesToRadians:ty,dotProduct:eK,ease:ia,enlivenObjectEnlivables:tB,enlivenObjects:tI,findScaleToCover:rq,findScaleToFit:rU,getBoundsOfCurve:r1,getOrthonormalVector:eU,getPathSegmentsInfo:ss,getPointOnPath:sn,getPointer:er,getRandomInt:(t,e)=>Math.floor(Math.random()*(e-t+1))+t,getRegularPolygonPath:(t,e)=>{let i=2*Math.PI/t,r=-O;t%2==0&&(r+=i/2);let s=Array(t+1);for(let n=0;n<t;n++){let t=n*i+r,{x:o,y:a}=new tn(tr(t),ts(t)).scalarMultiply(e);s[n]=[0===n?"M":"L",o,a]}return s[t]=["Z"],s},getSmoothPathFromPoints:su,getSvgAttributes:t=>{let e=["instantiated_by_use","style","id","class"];switch(t){case"linearGradient":return e.concat(["x1","y1","x2","y2","gradientUnits","gradientTransform"]);case"radialGradient":return e.concat(["gradientUnits","gradientTransform","cx","cy","r","fx","fy","fr"]);case"stop":return e.concat(["offset","stop-color","stop-opacity"])}return e},getUnitVector:eN,groupSVGElements:(t,e)=>t&&1===t.length?t[0]:new rN(t,e),hasStyleChanged:ra,invertTransform:tS,isBetweenVectors:eJ,isIdentityMatrix:tC,isTouchEvent:es,isTransparent:i8,joinPath:sd,loadImage:tR,magnitude:eH,makeBoundingBoxFromPoints:eo,makePathSimpler:r5,matrixToSVG:tQ,mergeClipPaths:(t,e)=>{var i;let r=t,s=e;r.inverted&&!s.inverted&&(r=e,s=t),em(s,null===(i=s.group)||void 0===i?void 0:i.calcTransformMatrix(),r.calcTransformMatrix());let n=r.inverted&&s.inverted;return n&&(r.inverted=s.inverted=!1),new rN([r],{clipPath:s,inverted:n})},multiplyTransformMatrices:tw,multiplyTransformMatrixArray:tT,parsePath:sc,parsePreserveAspectRatioAttribute:tJ,parseUnit:tK,pick:tX,projectStrokeOnPoints:re,qrDecompose:tD,radiansToDegrees:tx,removeFromArray:ti,removeTransformFromObject:(t,e)=>{let i=tw(tS(e),t.calcOwnMatrix());eh(t,i)},removeTransformMatrixForSvgParsing:sf,request:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.onComplete||T,r=new(C()).XMLHttpRequest,s=e.signal,n=function(){r.abort()},o=function(){s&&s.removeEventListener("abort",n),r.onerror=r.ontimeout=T};if(s&&s.aborted)throw new p("request");return s&&s.addEventListener("abort",n,{once:!0}),r.onreadystatechange=function(){4===r.readyState&&(o(),i(r),r.onreadystatechange=T)},r.onerror=r.ontimeout=o,r.open("get",t,!0),r.send(),r},requestAnimFrame:tc,resetObjectTransform:ec,rotatePoint:(t,e,i)=>t.rotate(i,e),rotateVector:eW,saveObjectTransform:eu,sendObjectToPlane:em,sendPointToPlane:ef,sendVectorToPlane:ep,setStyle:sg,sin:ts,sizeAfterTransform:ed,string:ro,stylesFromArray:rh,stylesToArray:rl,toBlob:t_,toDataURL:tv,toFixed:tq,transformPath:(t,e,i)=>(i&&(e=tw(e,[1,0,0,1,-i.x,-i.y])),t.map(t=>{let i=[...t];for(let r=1;r<t.length-1;r+=2){let{x:s,y:n}=tb({x:t[r],y:t[r+1]},e);i[r]=s,i[r+1]=n}return i})),transformPoint:tb});class sp extends t7{constructor(t){let{allowTouchScrolling:e=!1,containerClass:i=""}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(t),o(this,"upper",void 0),o(this,"container",void 0);let{el:r}=this.lower,s=this.createUpperCanvas();this.upper={el:s,ctx:s.getContext("2d")},this.applyCanvasStyle(r,{allowTouchScrolling:e}),this.applyCanvasStyle(s,{allowTouchScrolling:e,styles:{position:"absolute",left:"0",top:"0"}});let n=this.createContainerElement();n.classList.add(i),r.parentNode&&r.parentNode.replaceChild(n,r),n.append(r,s),this.container=n}createUpperCanvas(){let{el:t}=this.lower,e=tf();return e.className=t.className,e.classList.remove("lower-canvas"),e.classList.add("upper-canvas"),e.setAttribute("data-fabric","top"),e.style.cssText=t.style.cssText,e.setAttribute("draggable","true"),e}createContainerElement(){let t=x().createElement("div");return t.setAttribute("data-fabric","wrapper"),sg(t,{position:"relative"}),t9(t),t}applyCanvasStyle(t,e){let{styles:i,allowTouchScrolling:r}=e;sg(t,l(l({},i),{},{"touch-action":r?"manipulation":F})),t9(t)}setDimensions(t,e){super.setDimensions(t,e);let{el:i,ctx:r}=this.upper;t8(i,r,t,e)}setCSSDimensions(t){super.setCSSDimensions(t),t6(this.upper.el,t),t6(this.container,t)}cleanupDOM(t){let e=this.container,{el:i}=this.lower,{el:r}=this.upper;super.cleanupDOM(t),e.removeChild(r),e.removeChild(i),e.parentNode&&e.parentNode.replaceChild(i,e)}dispose(){super.dispose(),y().dispose(this.upper.el),delete this.upper,delete this.container}}class sm extends ee{constructor(){super(...arguments),o(this,"targets",[]),o(this,"_hoveredTargets",[]),o(this,"_objectsToRender",void 0),o(this,"_currentTransform",null),o(this,"_groupSelector",null),o(this,"contextTopDirty",!1)}static getDefaults(){return l(l({},super.getDefaults()),sm.ownDefaults)}get upperCanvasEl(){var t;return null===(t=this.elements.upper)||void 0===t?void 0:t.el}get contextTop(){var t;return null===(t=this.elements.upper)||void 0===t?void 0:t.ctx}get wrapperEl(){return this.elements.container}initElements(t){this.elements=new sp(t,{allowTouchScrolling:this.allowTouchScrolling,containerClass:this.containerClass}),this._createCacheCanvas()}_onObjectAdded(t){this._objectsToRender=void 0,super._onObjectAdded(t)}_onObjectRemoved(t){this._objectsToRender=void 0,t===this._activeObject&&(this.fire("before:selection:cleared",{deselected:[t]}),this._discardActiveObject(),this.fire("selection:cleared",{deselected:[t]}),t.fire("deselected",{target:t})),t===this._hoveredTarget&&(this._hoveredTarget=void 0,this._hoveredTargets=[]),super._onObjectRemoved(t)}_onStackOrderChanged(){this._objectsToRender=void 0,super._onStackOrderChanged()}_chooseObjectsToRender(){let t=this._activeObject;return!this.preserveObjectStacking&&t?this._objects.filter(e=>!e.group&&e!==t).concat(t):this._objects}renderAll(){this.cancelRequestedRender(),this.destroyed||(!this.contextTopDirty||this._groupSelector||this.isDrawingMode||(this.clearContext(this.contextTop),this.contextTopDirty=!1),this.hasLostContext&&(this.renderTopLayer(this.contextTop),this.hasLostContext=!1),this._objectsToRender||(this._objectsToRender=this._chooseObjectsToRender()),this.renderCanvas(this.getContext(),this._objectsToRender))}renderTopLayer(t){t.save(),this.isDrawingMode&&this._isCurrentlyDrawing&&(this.freeDrawingBrush&&this.freeDrawingBrush._render(),this.contextTopDirty=!0),this.selection&&this._groupSelector&&(this._drawSelection(t),this.contextTopDirty=!0),t.restore()}renderTop(){let t=this.contextTop;this.clearContext(t),this.renderTopLayer(t),this.fire("after:render",{ctx:t})}setTargetFindTolerance(t){t=Math.round(t),this.targetFindTolerance=t;let e=this.getRetinaScaling(),i=Math.ceil((2*t+1)*e);this.pixelFindCanvasEl.width=this.pixelFindCanvasEl.height=i,this.pixelFindContext.scale(e,e)}isTargetTransparent(t,e,i){let r=this.targetFindTolerance,s=this.pixelFindContext;this.clearContext(s),s.save(),s.translate(-e+r,-i+r),s.transform(...this.viewportTransform);let n=t.selectionBackgroundColor;t.selectionBackgroundColor="",t.render(s),t.selectionBackgroundColor=n,s.restore();let o=Math.round(r*this.getRetinaScaling());return i8(s,o,o,o)}_isSelectionKeyPressed(t){let e=this.selectionKey;return!!e&&(Array.isArray(e)?!!e.find(e=>!!e&&!0===t[e]):t[e])}_shouldClearSelection(t,e){let i=this.getActiveObjects(),r=this._activeObject;return!!(!e||e&&r&&i.length>1&&-1===i.indexOf(e)&&r!==e&&!this._isSelectionKeyPressed(t)||e&&!e.evented||e&&!e.selectable&&r&&r!==e)}_shouldCenterTransform(t,e,i){let r;if(t)return e===z||e===G||e===N||e===W?r=this.centeredScaling||t.centeredScaling:e===X&&(r=this.centeredRotation||t.centeredRotation),r?!i:i}_getOriginFromCorner(t,e){let i={x:t.originX,y:t.originY};return e&&(["ml","tl","bl"].includes(e)?i.x=j:["mr","tr","br"].includes(e)&&(i.x=P),["tl","mt","tr"].includes(e)?i.y=A:["bl","mb","br"].includes(e)&&(i.y="top")),i}_setupCurrentTransform(t,e,i){var r;let s=e.group?ef(this.getScenePoint(t),void 0,e.group.calcTransformMatrix()):this.getScenePoint(t),{key:n="",control:o}=e.getActiveControl()||{},a=i&&o?null===(r=o.getActionHandler(t,e,o))||void 0===r?void 0:r.bind(o):eO,h=((t,e,i,r)=>{if(!e||!t)return"drag";let s=r.controls[e];return s.getActionName(i,s,r)})(i,n,t,e),c=t[this.centeredKey],u=this._shouldCenterTransform(e,h,c)?{x:E,y:E}:this._getOriginFromCorner(e,n),d={target:e,action:h,actionHandler:a,actionPerformed:!1,corner:n,scaleX:e.scaleX,scaleY:e.scaleY,skewX:e.skewX,skewY:e.skewY,offsetX:s.x-e.left,offsetY:s.y-e.top,originX:u.x,originY:u.y,ex:s.x,ey:s.y,lastX:s.x,lastY:s.y,theta:ty(e.angle),width:e.width,height:e.height,shiftKey:t.shiftKey,altKey:c,original:l(l({},eu(e)),{},{originX:u.x,originY:u.y})};this._currentTransform=d,this.fire("before:transform",{e:t,transform:d})}setCursor(t){this.upperCanvasEl.style.cursor=t}_drawSelection(t){let{x:e,y:i,deltaX:r,deltaY:s}=this._groupSelector,n=new tn(e,i).transform(this.viewportTransform),o=new tn(e+r,i+s).transform(this.viewportTransform),a=this.selectionLineWidth/2,l=Math.min(n.x,o.x),h=Math.min(n.y,o.y),c=Math.max(n.x,o.x),u=Math.max(n.y,o.y);this.selectionColor&&(t.fillStyle=this.selectionColor,t.fillRect(l,h,c-l,u-h)),this.selectionLineWidth&&this.selectionBorderColor&&(t.lineWidth=this.selectionLineWidth,t.strokeStyle=this.selectionBorderColor,l+=a,h+=a,c-=a,u-=a,i4.prototype._setLineDash.call(this,t,this.selectionDashArray),t.strokeRect(l,h,c-l,u-h))}findTarget(t){if(this.skipTargetFind)return;let e=this.getViewportPoint(t),i=this._activeObject,r=this.getActiveObjects();if(this.targets=[],i&&r.length>=1){if(i.findControl(e,es(t))||r.length>1&&this.searchPossibleTargets([i],e))return i;if(i===this.searchPossibleTargets([i],e)){if(this.preserveObjectStacking){let r=this.targets;this.targets=[];let s=this.searchPossibleTargets(this._objects,e);return t[this.altSelectionKey]&&s&&s!==i?(this.targets=r,i):s}return i}}return this.searchPossibleTargets(this._objects,e)}_pointIsInObjectSelectionArea(t,e){let i=t.getCoords(),r=this.getZoom(),s=t.padding/r;if(s){let[t,e,r,n]=i,o=Math.atan2(e.y-t.y,e.x-t.x),a=tr(o)*s,l=ts(o)*s,h=a+l,c=a-l;i=[new tn(t.x-c,t.y-h),new tn(e.x+h,e.y-c),new tn(r.x+c,r.y+h),new tn(n.x-h,n.y+c)]}return iC.isPointInPolygon(e,i)}_checkTarget(t,e){return!!(t&&t.visible&&t.evented&&this._pointIsInObjectSelectionArea(t,ef(e,void 0,this.viewportTransform)))&&(!this.perPixelTargetFind&&!t.perPixelTargetFind||!!t.isEditing||!this.isTargetTransparent(t,e.x,e.y))}_searchPossibleTargets(t,e){let i=t.length;for(;i--;){let r=t[i];if(this._checkTarget(r,e)){if(ta(r)&&r.subTargetCheck){let t=this._searchPossibleTargets(r._objects,e);t&&this.targets.push(t)}return r}}}searchPossibleTargets(t,e){let i=this._searchPossibleTargets(t,e);if(i&&ta(i)&&i.interactive&&this.targets[0]){let t=this.targets;for(let e=t.length-1;e>0;e--){let i=t[e];if(!ta(i)||!i.interactive)return i}return t[0]}return i}getViewportPoint(t){return this._pointer?this._pointer:this.getPointer(t,!0)}getScenePoint(t){return this._absolutePointer?this._absolutePointer:this.getPointer(t)}getPointer(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.upperCanvasEl,r=i.getBoundingClientRect(),s=er(t),n=r.width||0,o=r.height||0;n&&o||("top"in r&&A in r&&(o=Math.abs(r.top-r.bottom)),j in r&&P in r&&(n=Math.abs(r.right-r.left))),this.calcOffset(),s.x=s.x-this._offset.left,s.y=s.y-this._offset.top,e||(s=ef(s,void 0,this.viewportTransform));let a=this.getRetinaScaling();1!==a&&(s.x/=a,s.y/=a);let l=0===n||0===o?new tn(1,1):new tn(i.width/n,i.height/o);return s.multiply(l)}_setDimensionsImpl(t,e){this._resetTransformEventData(),super._setDimensionsImpl(t,e),this._isCurrentlyDrawing&&this.freeDrawingBrush&&this.freeDrawingBrush._setBrushStyles(this.contextTop)}_createCacheCanvas(){this.pixelFindCanvasEl=tf(),this.pixelFindContext=this.pixelFindCanvasEl.getContext("2d",{willReadFrequently:!0}),this.setTargetFindTolerance(this.targetFindTolerance)}getTopContext(){return this.elements.upper.ctx}getSelectionContext(){return this.elements.upper.ctx}getSelectionElement(){return this.elements.upper.el}getActiveObject(){return this._activeObject}getActiveObjects(){let t=this._activeObject;return t2(t)?t.getObjects():t?[t]:[]}_fireSelectionEvents(t,e){let i=!1,r=!1,s=this.getActiveObjects(),n=[],o=[];t.forEach(t=>{s.includes(t)||(i=!0,t.fire("deselected",{e:e,target:t}),o.push(t))}),s.forEach(r=>{t.includes(r)||(i=!0,r.fire("selected",{e:e,target:r}),n.push(r))}),t.length>0&&s.length>0?(r=!0,i&&this.fire("selection:updated",{e:e,selected:n,deselected:o})):s.length>0?(r=!0,this.fire("selection:created",{e:e,selected:n})):t.length>0&&(r=!0,this.fire("selection:cleared",{e:e,deselected:o})),r&&(this._objectsToRender=void 0)}setActiveObject(t,e){let i=this.getActiveObjects(),r=this._setActiveObject(t,e);return this._fireSelectionEvents(i,e),r}_setActiveObject(t,e){let i=this._activeObject;return i!==t&&!(!this._discardActiveObject(e,t)&&this._activeObject)&&!t.onSelect({e:e})&&(this._activeObject=t,t2(t)&&i!==t&&t.set("canvas",this),t.setCoords(),!0)}_discardActiveObject(t,e){let i=this._activeObject;return!!i&&!i.onDeselect({e:t,object:e})&&(this._currentTransform&&this._currentTransform.target===i&&this.endCurrentTransform(t),t2(i)&&i===this._hoveredTarget&&(this._hoveredTarget=void 0),this._activeObject=void 0,!0)}discardActiveObject(t){let e=this.getActiveObjects(),i=this.getActiveObject();e.length&&this.fire("before:selection:cleared",{e:t,deselected:[i]});let r=this._discardActiveObject(t);return this._fireSelectionEvents(e,t),r}endCurrentTransform(t){let e=this._currentTransform;this._finalizeCurrentTransform(t),e&&e.target&&(e.target.isMoving=!1),this._currentTransform=null}_finalizeCurrentTransform(t){let e=this._currentTransform,i=e.target,r={e:t,target:i,transform:e,action:e.action};i._scaling&&(i._scaling=!1),i.setCoords(),e.actionPerformed&&(this.fire("object:modified",r),i.fire(Q,r))}setViewportTransform(t){super.setViewportTransform(t);let e=this._activeObject;e&&e.setCoords()}destroy(){let t=this._activeObject;t2(t)&&(t.removeAll(),t.dispose()),delete this._activeObject,super.destroy(),this.pixelFindContext=null,this.pixelFindCanvasEl=void 0}clear(){this.discardActiveObject(),this._activeObject=void 0,this.clearContext(this.contextTop),super.clear()}drawControls(t){let e=this._activeObject;e&&e._renderControls(t)}_toObject(t,e,i){let r=this._realizeGroupTransformOnObject(t),s=super._toObject(t,e,i);return t.set(r),s}_realizeGroupTransformOnObject(t){let{group:e}=t;if(e&&t2(e)&&this._activeObject===e){let i=tX(t,["angle","flipX","flipY",P,G,N,U,q,"top"]);return el(t,e.calcOwnMatrix()),i}return{}}_setSVGObject(t,e,i){let r=this._realizeGroupTransformOnObject(e);super._setSVGObject(t,e,i),e.set(r)}}o(sm,"ownDefaults",{uniformScaling:!0,uniScaleKey:"shiftKey",centeredScaling:!1,centeredRotation:!1,centeredKey:"altKey",altActionKey:"shiftKey",selection:!0,selectionKey:"shiftKey",selectionColor:"rgba(100, 100, 255, 0.3)",selectionDashArray:[],selectionBorderColor:"rgba(255, 255, 255, 0.3)",selectionLineWidth:1,selectionFullyContained:!1,hoverCursor:"move",moveCursor:"move",defaultCursor:"default",freeDrawingCursor:"crosshair",notAllowedCursor:"not-allowed",perPixelTargetFind:!1,targetFindTolerance:0,skipTargetFind:!1,stopContextMenu:!1,fireRightClick:!1,fireMiddleClick:!1,enablePointerEvents:!1,containerClass:"canvas-container",preserveObjectStacking:!1});class sv{constructor(t){o(this,"targets",[]),o(this,"__disposer",void 0);let e=()=>{let{hiddenTextarea:e}=t.getActiveObject()||{};e&&e.focus()},i=t.upperCanvasEl;i.addEventListener("click",e),this.__disposer=()=>i.removeEventListener("click",e)}exitTextEditing(){this.target=void 0,this.targets.forEach(t=>{t.isEditing&&t.exitEditing()})}add(t){this.targets.push(t)}remove(t){this.unregister(t),ti(this.targets,t)}register(t){this.target=t}unregister(t){t===this.target&&(this.target=void 0)}onMouseMove(t){var e;(null===(e=this.target)||void 0===e?void 0:e.isEditing)&&this.target.updateSelectionOnMouseMove(t)}clear(){this.targets=[],this.target=void 0}dispose(){this.clear(),this.__disposer(),delete this.__disposer}}let s_=["target","oldTarget","fireCanvas","e"],sy={passive:!1},sx=(t,e)=>{let i=t.getViewportPoint(e),r=t.getScenePoint(e);return{viewportPoint:i,scenePoint:r,pointer:i,absolutePointer:r}},sC=function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return t.addEventListener(...i)},sb=function(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];return t.removeEventListener(...i)},sS={mouse:{in:"over",out:"out",targetIn:"mouseover",targetOut:"mouseout",canvasIn:"mouse:over",canvasOut:"mouse:out"},drag:{in:"enter",out:"leave",targetIn:"dragenter",targetOut:"dragleave",canvasIn:"drag:enter",canvasOut:"drag:leave"}};class sw extends sm{constructor(t){super(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}),o(this,"_isClick",void 0),o(this,"textEditingManager",new sv(this)),["_onMouseDown","_onTouchStart","_onMouseMove","_onMouseUp","_onTouchEnd","_onResize","_onMouseWheel","_onMouseOut","_onMouseEnter","_onContextMenu","_onDoubleClick","_onDragStart","_onDragEnd","_onDragProgress","_onDragOver","_onDragEnter","_onDragLeave","_onDrop"].forEach(t=>{this[t]=this[t].bind(this)}),this.addOrRemove(sC,"add")}_getEventPrefix(){return this.enablePointerEvents?"pointer":"mouse"}addOrRemove(t,e){let i=this.upperCanvasEl,r=this._getEventPrefix();t(t4(i),"resize",this._onResize),t(i,r+"down",this._onMouseDown),t(i,"".concat(r,"move"),this._onMouseMove,sy),t(i,"".concat(r,"out"),this._onMouseOut),t(i,"".concat(r,"enter"),this._onMouseEnter),t(i,"wheel",this._onMouseWheel),t(i,"contextmenu",this._onContextMenu),t(i,"dblclick",this._onDoubleClick),t(i,"dragstart",this._onDragStart),t(i,"dragend",this._onDragEnd),t(i,"dragover",this._onDragOver),t(i,"dragenter",this._onDragEnter),t(i,"dragleave",this._onDragLeave),t(i,"drop",this._onDrop),this.enablePointerEvents||t(i,"touchstart",this._onTouchStart,sy)}removeListeners(){this.addOrRemove(sb,"remove");let t=this._getEventPrefix(),e=t3(this.upperCanvasEl);sb(e,"".concat(t,"up"),this._onMouseUp),sb(e,"touchend",this._onTouchEnd,sy),sb(e,"".concat(t,"move"),this._onMouseMove,sy),sb(e,"touchmove",this._onMouseMove,sy),clearTimeout(this._willAddMouseDown)}_onMouseWheel(t){this.__onMouseWheel(t)}_onMouseOut(t){let e=this._hoveredTarget,i=l({e:t},sx(this,t));this.fire("mouse:out",l(l({},i),{},{target:e})),this._hoveredTarget=void 0,e&&e.fire("mouseout",l({},i)),this._hoveredTargets.forEach(t=>{this.fire("mouse:out",l(l({},i),{},{target:t})),t&&t.fire("mouseout",l({},i))}),this._hoveredTargets=[]}_onMouseEnter(t){this._currentTransform||this.findTarget(t)||(this.fire("mouse:over",l({e:t},sx(this,t))),this._hoveredTarget=void 0,this._hoveredTargets=[])}_onDragStart(t){this._isClick=!1;let e=this.getActiveObject();if(e&&e.onDragStart(t)){this._dragSource=e;let i={e:t,target:e};return this.fire("dragstart",i),e.fire("dragstart",i),void sC(this.upperCanvasEl,"drag",this._onDragProgress)}en(t)}_renderDragEffects(t,e,i){let r=!1,s=this._dropTarget;s&&s!==e&&s!==i&&(s.clearContextTop(),r=!0),null==e||e.clearContextTop(),i!==e&&(null==i||i.clearContextTop());let n=this.contextTop;n.save(),n.transform(...this.viewportTransform),e&&(n.save(),e.transform(n),e.renderDragSourceEffect(t),n.restore(),r=!0),i&&(n.save(),i.transform(n),i.renderDropTargetEffect(t),n.restore(),r=!0),n.restore(),r&&(this.contextTopDirty=!0)}_onDragEnd(t){let e=!!t.dataTransfer&&t.dataTransfer.dropEffect!==F,i=e?this._activeObject:void 0,r={e:t,target:this._dragSource,subTargets:this.targets,dragSource:this._dragSource,didDrop:e,dropTarget:i};sb(this.upperCanvasEl,"drag",this._onDragProgress),this.fire("dragend",r),this._dragSource&&this._dragSource.fire("dragend",r),delete this._dragSource,this._onMouseUp(t)}_onDragProgress(t){let e={e:t,target:this._dragSource,dragSource:this._dragSource,dropTarget:this._draggedoverTarget};this.fire("drag",e),this._dragSource&&this._dragSource.fire("drag",e)}findDragTargets(t){return this.targets=[],{target:this._searchPossibleTargets(this._objects,this.getViewportPoint(t)),targets:[...this.targets]}}_onDragOver(t){let e;let i="dragover",{target:r,targets:s}=this.findDragTargets(t),n=this._dragSource,o={e:t,target:r,subTargets:s,dragSource:n,canDrop:!1,dropTarget:void 0};this.fire(i,o),this._fireEnterLeaveEvents(r,o),r&&(r.canDrop(t)&&(e=r),r.fire(i,o));for(let r=0;r<s.length;r++){let n=s[r];n.canDrop(t)&&(e=n),n.fire(i,o)}this._renderDragEffects(t,n,e),this._dropTarget=e}_onDragEnter(t){let{target:e,targets:i}=this.findDragTargets(t),r={e:t,target:e,subTargets:i,dragSource:this._dragSource};this.fire("dragenter",r),this._fireEnterLeaveEvents(e,r)}_onDragLeave(t){let e={e:t,target:this._draggedoverTarget,subTargets:this.targets,dragSource:this._dragSource};this.fire("dragleave",e),this._fireEnterLeaveEvents(void 0,e),this._renderDragEffects(t,this._dragSource),this._dropTarget=void 0,this.targets=[],this._hoveredTargets=[]}_onDrop(t){let{target:e,targets:i}=this.findDragTargets(t),r=this._basicEventHandler("drop:before",l({e:t,target:e,subTargets:i,dragSource:this._dragSource},sx(this,t)));r.didDrop=!1,r.dropTarget=void 0,this._basicEventHandler("drop",r),this.fire("drop:after",r)}_onContextMenu(t){let e=this.findTarget(t),i=this.targets||[],r=this._basicEventHandler("contextmenu:before",{e:t,target:e,subTargets:i});return this.stopContextMenu&&en(t),this._basicEventHandler("contextmenu",r),!1}_onDoubleClick(t){this._cacheTransformEventData(t),this._handleEvent(t,"dblclick"),this._resetTransformEventData()}getPointerId(t){let e=t.changedTouches;return e?e[0]&&e[0].identifier:this.enablePointerEvents?t.pointerId:-1}_isMainEvent(t){return!0===t.isPrimary||!1!==t.isPrimary&&("touchend"===t.type&&0===t.touches.length||!t.changedTouches||t.changedTouches[0].identifier===this.mainTouchId)}_onTouchStart(t){let e=!this.allowTouchScrolling,i=this._activeObject;void 0===this.mainTouchId&&(this.mainTouchId=this.getPointerId(t)),this.__onMouseDown(t),(this.isDrawingMode||i&&this._target===i)&&(e=!0),e&&t.preventDefault(),this._resetTransformEventData();let r=this.upperCanvasEl,s=this._getEventPrefix(),n=t3(r);sC(n,"touchend",this._onTouchEnd,sy),e&&sC(n,"touchmove",this._onMouseMove,sy),sb(r,"".concat(s,"down"),this._onMouseDown)}_onMouseDown(t){this.__onMouseDown(t),this._resetTransformEventData();let e=this.upperCanvasEl,i=this._getEventPrefix();sb(e,"".concat(i,"move"),this._onMouseMove,sy);let r=t3(e);sC(r,"".concat(i,"up"),this._onMouseUp),sC(r,"".concat(i,"move"),this._onMouseMove,sy)}_onTouchEnd(t){if(t.touches.length>0)return;this.__onMouseUp(t),this._resetTransformEventData(),delete this.mainTouchId;let e=this._getEventPrefix(),i=t3(this.upperCanvasEl);sb(i,"touchend",this._onTouchEnd,sy),sb(i,"touchmove",this._onMouseMove,sy),this._willAddMouseDown&&clearTimeout(this._willAddMouseDown),this._willAddMouseDown=setTimeout(()=>{sC(this.upperCanvasEl,"".concat(e,"down"),this._onMouseDown),this._willAddMouseDown=0},400)}_onMouseUp(t){this.__onMouseUp(t),this._resetTransformEventData();let e=this.upperCanvasEl,i=this._getEventPrefix();if(this._isMainEvent(t)){let t=t3(this.upperCanvasEl);sb(t,"".concat(i,"up"),this._onMouseUp),sb(t,"".concat(i,"move"),this._onMouseMove,sy),sC(e,"".concat(i,"move"),this._onMouseMove,sy)}}_onMouseMove(t){let e=this.getActiveObject();this.allowTouchScrolling||e&&e.shouldStartDragging(t)||!t.preventDefault||t.preventDefault(),this.__onMouseMove(t)}_onResize(){this.calcOffset(),this._resetTransformEventData()}_shouldRender(t){let e=this.getActiveObject();return!!e!=!!t||e&&t&&e!==t}__onMouseUp(t){var e;this._cacheTransformEventData(t),this._handleEvent(t,"up:before");let i=this._currentTransform,r=this._isClick,s=this._target,{button:n}=t;if(n)return(this.fireMiddleClick&&1===n||this.fireRightClick&&2===n)&&this._handleEvent(t,"up"),void this._resetTransformEventData();if(this.isDrawingMode&&this._isCurrentlyDrawing)return void this._onMouseUpInDrawingMode(t);if(!this._isMainEvent(t))return;let o,a,l=!1;if(i&&(this._finalizeCurrentTransform(t),l=i.actionPerformed),!r){let e=s===this._activeObject;this.handleSelection(t),l||(l=this._shouldRender(s)||!e&&s===this._activeObject)}if(s){let{key:e,control:r}=s.findControl(this.getViewportPoint(t),es(t))||{};if(a=e,s.selectable&&s!==this._activeObject&&"up"===s.activeOn)this.setActiveObject(s,t),l=!0;else if(r){let e=r.getMouseUpHandler(t,s,r);e&&(o=this.getScenePoint(t),e.call(r,t,i,o.x,o.y))}s.isMoving=!1}if(i&&(i.target!==s||i.corner!==a)){let e=i.target&&i.target.controls[i.corner],r=e&&e.getMouseUpHandler(t,i.target,e);o=o||this.getScenePoint(t),r&&r.call(e,t,i,o.x,o.y)}this._setCursorFromEvent(t,s),this._handleEvent(t,"up"),this._groupSelector=null,this._currentTransform=null,s&&(s.__corner=void 0),l?this.requestRenderAll():r||null!==(e=this._activeObject)&&void 0!==e&&e.isEditing||this.renderTop()}_basicEventHandler(t,e){let{target:i,subTargets:r=[]}=e;this.fire(t,e),i&&i.fire(t,e);for(let s=0;s<r.length;s++)r[s]!==i&&r[s].fire(t,e);return e}_handleEvent(t,e){let i=this._target,r=this.targets||[],s=l(l({e:t,target:i,subTargets:r},sx(this,t)),{},{transform:this._currentTransform},"up:before"===e||"up"===e?{isClick:this._isClick,currentTarget:this.findTarget(t),currentSubTargets:this.targets}:{});this.fire("mouse:".concat(e),s),i&&i.fire("mouse".concat(e),s);for(let t=0;t<r.length;t++)r[t]!==i&&r[t].fire("mouse".concat(e),s)}_onMouseDownInDrawingMode(t){this._isCurrentlyDrawing=!0,this.getActiveObject()&&(this.discardActiveObject(t),this.requestRenderAll());let e=this.getScenePoint(t);this.freeDrawingBrush&&this.freeDrawingBrush.onMouseDown(e,{e:t,pointer:e}),this._handleEvent(t,"down")}_onMouseMoveInDrawingMode(t){if(this._isCurrentlyDrawing){let e=this.getScenePoint(t);this.freeDrawingBrush&&this.freeDrawingBrush.onMouseMove(e,{e:t,pointer:e})}this.setCursor(this.freeDrawingCursor),this._handleEvent(t,"move")}_onMouseUpInDrawingMode(t){let e=this.getScenePoint(t);this.freeDrawingBrush?this._isCurrentlyDrawing=!!this.freeDrawingBrush.onMouseUp({e:t,pointer:e}):this._isCurrentlyDrawing=!1,this._handleEvent(t,"up")}__onMouseDown(t){this._isClick=!0,this._cacheTransformEventData(t),this._handleEvent(t,"down:before");let e=this._target,{button:i}=t;if(i)return(this.fireMiddleClick&&1===i||this.fireRightClick&&2===i)&&this._handleEvent(t,"down"),void this._resetTransformEventData();if(this.isDrawingMode)return void this._onMouseDownInDrawingMode(t);if(!this._isMainEvent(t)||this._currentTransform)return;let r=this._shouldRender(e),s=!1;if(this.handleMultiSelection(t,e)?(e=this._activeObject,s=!0,r=!0):this._shouldClearSelection(t,e)&&this.discardActiveObject(t),this.selection&&(!e||!e.selectable&&!e.isEditing&&e!==this._activeObject)){let e=this.getScenePoint(t);this._groupSelector={x:e.x,y:e.y,deltaY:0,deltaX:0}}if(e){let i=e===this._activeObject;e.selectable&&"down"===e.activeOn&&this.setActiveObject(e,t);let r=e.findControl(this.getViewportPoint(t),es(t));if(e===this._activeObject&&(r||!s)){this._setupCurrentTransform(t,e,i);let s=r?r.control:void 0,n=this.getScenePoint(t),o=s&&s.getMouseDownHandler(t,e,s);o&&o.call(s,t,this._currentTransform,n.x,n.y)}}r&&(this._objectsToRender=void 0),this._handleEvent(t,"down"),r&&this.requestRenderAll()}_resetTransformEventData(){this._target=this._pointer=this._absolutePointer=void 0}_cacheTransformEventData(t){this._resetTransformEventData(),this._pointer=this.getViewportPoint(t),this._absolutePointer=ef(this._pointer,void 0,this.viewportTransform),this._target=this._currentTransform?this._currentTransform.target:this.findTarget(t)}__onMouseMove(t){if(this._isClick=!1,this._cacheTransformEventData(t),this._handleEvent(t,"move:before"),this.isDrawingMode)return void this._onMouseMoveInDrawingMode(t);if(!this._isMainEvent(t))return;let e=this._groupSelector;if(e){let i=this.getScenePoint(t);e.deltaX=i.x-e.x,e.deltaY=i.y-e.y,this.renderTop()}else if(this._currentTransform)this._transformObject(t);else{let e=this.findTarget(t);this._setCursorFromEvent(t,e),this._fireOverOutEvents(t,e)}this.textEditingManager.onMouseMove(t),this._handleEvent(t,"move"),this._resetTransformEventData()}_fireOverOutEvents(t,e){let i=this._hoveredTarget,r=this._hoveredTargets,s=this.targets,n=Math.max(r.length,s.length);this.fireSyntheticInOutEvents("mouse",{e:t,target:e,oldTarget:i,fireCanvas:!0});for(let e=0;e<n;e++)this.fireSyntheticInOutEvents("mouse",{e:t,target:s[e],oldTarget:r[e]});this._hoveredTarget=e,this._hoveredTargets=this.targets.concat()}_fireEnterLeaveEvents(t,e){let i=this._draggedoverTarget,r=this._hoveredTargets,s=this.targets,n=Math.max(r.length,s.length);this.fireSyntheticInOutEvents("drag",l(l({},e),{},{target:t,oldTarget:i,fireCanvas:!0}));for(let t=0;t<n;t++)this.fireSyntheticInOutEvents("drag",l(l({},e),{},{target:s[t],oldTarget:r[t]}));this._draggedoverTarget=t}fireSyntheticInOutEvents(t,e){let{target:i,oldTarget:r,fireCanvas:s,e:n}=e,o=h(e,s_),{targetIn:a,targetOut:c,canvasIn:u,canvasOut:d}=sS[t],g=r!==i;if(r&&g){let t=l(l({},o),{},{e:n,target:r,nextTarget:i},sx(this,n));s&&this.fire(d,t),r.fire(c,t)}if(i&&g){let t=l(l({},o),{},{e:n,target:i,previousTarget:r},sx(this,n));s&&this.fire(u,t),i.fire(a,t)}}__onMouseWheel(t){this._cacheTransformEventData(t),this._handleEvent(t,"wheel"),this._resetTransformEventData()}_transformObject(t){let e=this.getScenePoint(t),i=this._currentTransform,r=i.target,s=r.group?ef(e,void 0,r.group.calcTransformMatrix()):e;i.shiftKey=t.shiftKey,i.altKey=!!this.centeredKey&&t[this.centeredKey],this._performTransformAction(t,i,s),i.actionPerformed&&this.requestRenderAll()}_performTransformAction(t,e,i){let{action:r,actionHandler:s,target:n}=e,o=!!s&&s(t,e,i.x,i.y);o&&n.setCoords(),"drag"===r&&o&&(e.target.isMoving=!0,this.setCursor(e.target.moveCursor||this.moveCursor)),e.actionPerformed=e.actionPerformed||o}_setCursorFromEvent(t,e){if(!e)return void this.setCursor(this.defaultCursor);let i=e.hoverCursor||this.hoverCursor,r=t2(this._activeObject)?this._activeObject:null,s=(!r||e.group!==r)&&e.findControl(this.getViewportPoint(t));if(s){let i=s.control;this.setCursor(i.cursorStyleHandler(t,i,e))}else e.subTargetCheck&&this.targets.concat().reverse().map(t=>{i=t.hoverCursor||i}),this.setCursor(i)}handleMultiSelection(t,e){let i=this._activeObject,r=t2(i);if(i&&this._isSelectionKeyPressed(t)&&this.selection&&e&&e.selectable&&(i!==e||r)&&(r||!e.isDescendantOf(i)&&!i.isDescendantOf(e))&&!e.onSelect({e:t})&&!i.getActiveControl()){if(r){let r=i.getObjects();if(e===i){let i=this.getViewportPoint(t);if(!(e=this.searchPossibleTargets(r,i)||this.searchPossibleTargets(this._objects,i))||!e.selectable)return!1}e.group===i?(i.remove(e),this._hoveredTarget=e,this._hoveredTargets=[...this.targets],1===i.size()&&this._setActiveObject(i.item(0),t)):(i.multiSelectAdd(e),this._hoveredTarget=i,this._hoveredTargets=[...this.targets]),this._fireSelectionEvents(r,t)}else{i.isEditing&&i.exitEditing();let r=new($.getClass("ActiveSelection"))([],{canvas:this});r.multiSelectAdd(i,e),this._hoveredTarget=r,this._setActiveObject(r,t),this._fireSelectionEvents([i],t)}return!0}return!1}handleSelection(t){if(!this.selection||!this._groupSelector)return!1;let{x:e,y:i,deltaX:r,deltaY:s}=this._groupSelector,n=new tn(e,i),o=n.add(new tn(r,s)),a=n.min(o),l=n.max(o).subtract(a),h=this.collectObjects({left:a.x,top:a.y,width:l.x,height:l.y},{includeIntersecting:!this.selectionFullyContained}),c=n.eq(o)?h[0]?[h[0]]:[]:h.length>1?h.filter(e=>!e.onSelect({e:t})).reverse():h;if(1===c.length)this.setActiveObject(c[0],t);else if(c.length>1){let e=$.getClass("ActiveSelection");this.setActiveObject(new e(c,{canvas:this}),t)}return this._groupSelector=null,!0}clear(){this.textEditingManager.clear(),super.clear()}destroy(){this.removeListeners(),this.textEditingManager.dispose(),super.destroy()}}let sT={x1:0,y1:0,x2:0,y2:0},sO=l(l({},sT),{},{r1:0,r2:0}),sD=(t,e)=>isNaN(t)&&"number"==typeof e?e:t,sk=/^(\d+\.\d+)%|(\d+)%$/;function sM(t){return t&&sk.test(t)}function sE(t,e){return e0(0,sD("number"==typeof t?t:"string"==typeof t?parseFloat(t)/(sM(t)?100:1):NaN,e),1)}let sP=/\s*;\s*/,sA=/\s*:\s*/;function sj(t){return"linearGradient"===t.nodeName||"LINEARGRADIENT"===t.nodeName?"linear":"radial"}function sF(t){return"userSpaceOnUse"===t.getAttribute("gradientUnits")?"pixels":"percentage"}function sL(t,e){return t.getAttribute(e)}class sR{constructor(t){let{type:e="linear",gradientUnits:i="pixels",coords:r={},colorStops:s=[],offsetX:n=0,offsetY:o=0,gradientTransform:a,id:h}=t||{};Object.assign(this,{type:e,gradientUnits:i,coords:l(l({},"radial"===e?sO:sT),r),colorStops:s,offsetX:n,offsetY:o,gradientTransform:a,id:h?"".concat(h,"_").concat(tg()):tg()})}addColorStop(t){for(let e in t){let i=new tU(t[e]);this.colorStops.push({offset:parseFloat(e),color:i.toRgb(),opacity:i.getAlpha()})}return this}toObject(t){return l(l({},tX(this,t)),{},{type:this.type,coords:l({},this.coords),colorStops:this.colorStops.map(t=>l({},t)),offsetX:this.offsetX,offsetY:this.offsetY,gradientUnits:this.gradientUnits,gradientTransform:this.gradientTransform?[...this.gradientTransform]:void 0})}toSVG(t){let{additionalTransform:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=[],r=this.gradientTransform?this.gradientTransform.concat():M.concat(),s="pixels"===this.gradientUnits?"userSpaceOnUse":"objectBoundingBox",n=this.colorStops.map(t=>l({},t)).sort((t,e)=>t.offset-e.offset),o=-this.offsetX,a=-this.offsetY;"objectBoundingBox"===s?(o/=t.width,a/=t.height):(o+=t.width/2,a+=t.height/2),t&&"function"==typeof t._renderPathCommands&&"percentage"!==this.gradientUnits&&(o-=t.pathOffset.x,a-=t.pathOffset.y),r[4]-=o,r[5]-=a;let h=['id="SVGID_'.concat(this.id,'"'),'gradientUnits="'.concat(s,'"'),'gradientTransform="'.concat(e?e+" ":"").concat(tQ(r),'"'),""].join(" ");if("linear"===this.type){let{x1:t,y1:e,x2:r,y2:s}=this.coords;i.push("<linearGradient ",h,' x1="',t,'" y1="',e,'" x2="',r,'" y2="',s,'">\n')}else if("radial"===this.type){let{x1:t,y1:e,x2:r,y2:s,r1:o,r2:a}=this.coords,l=o>a;i.push("<radialGradient ",h,' cx="',l?t:r,'" cy="',l?e:s,'" r="',l?o:a,'" fx="',l?r:t,'" fy="',l?s:e,'">\n'),l&&(n.reverse(),n.forEach(t=>{t.offset=1-t.offset}));let c=Math.min(o,a);if(c>0){let t=c/Math.max(o,a);n.forEach(e=>{e.offset+=t*(1-e.offset)})}}return n.forEach(t=>{let{color:e,offset:r,opacity:s}=t;i.push("<stop ",'offset="',100*r+"%",'" style="stop-color:',e,void 0!==s?";stop-opacity: "+s:";",'"/>\n')}),i.push("linear"===this.type?"</linearGradient>":"</radialGradient>","\n"),i.join("")}toLive(t){let{x1:e,y1:i,x2:r,y2:s,r1:n,r2:o}=this.coords,a="linear"===this.type?t.createLinearGradient(e,i,r,s):t.createRadialGradient(e,i,n,r,s,o);return this.colorStops.forEach(t=>{let{color:e,opacity:i,offset:r}=t;a.addColorStop(r,void 0!==i?new tU(e).setAlpha(i).toRgba():e)}),a}static async fromObject(t){let{colorStops:e,gradientTransform:i}=t;return new this(l(l({},t),{},{colorStops:e?e.map(t=>l({},t)):void 0,gradientTransform:i?[...i]:void 0}))}static fromElement(t,e,i){var r;let s=sF(t),n=e._findCenterFromElement();return new this(l({id:t.getAttribute("id")||void 0,type:sj(t),coords:(r={width:i.viewBoxWidth||i.width,height:i.viewBoxHeight||i.height},function(t,e){let i,{width:r,height:s,gradientUnits:n}=e;return Object.keys(t).reduce((e,o)=>{let a=t[o];return"Infinity"===a?i=1:"-Infinity"===a?i=0:(i="string"==typeof a?parseFloat(a):a,"string"==typeof a&&sM(a)&&(i*=.01,"pixels"===n&&("x1"!==o&&"x2"!==o&&"r2"!==o||(i*=r),"y1"!==o&&"y2"!==o||(i*=s)))),e[o]=i,e},{})}("linear"===sj(t)?{x1:sL(t,"x1")||0,y1:sL(t,"y1")||0,x2:sL(t,"x2")||"100%",y2:sL(t,"y2")||0}:{x1:sL(t,"fx")||sL(t,"cx")||"50%",y1:sL(t,"fy")||sL(t,"cy")||"50%",r1:0,x2:sL(t,"cx")||"50%",y2:sL(t,"cy")||"50%",r2:sL(t,"r")||"50%"},l(l({},r),{},{gradientUnits:sF(t)}))),colorStops:function(t,e){let i=[],r=t.getElementsByTagName("stop"),s=sE(e,1);for(let t=r.length;t--;)i.push(function(t,e){let i,r;let s=t.getAttribute("style");if(s){let t=s.split(sP);""===t[t.length-1]&&t.pop();for(let e=t.length;e--;){let[s,n]=t[e].split(sA).map(t=>t.trim());"stop-color"===s?i=n:"stop-opacity"===s&&(r=n)}}let n=new tU(i||t.getAttribute("stop-color")||"rgb(0,0,0)");return{offset:sE(t.getAttribute("offset"),0),color:n.toRgb(),opacity:sD(parseFloat(r||t.getAttribute("stop-opacity")||""),1)*n.getAlpha()*e}}(r[t],s));return i}(t,i.opacity),gradientUnits:s,gradientTransform:rO(t.getAttribute("gradientTransform")||"")},"pixels"===s?{offsetX:e.width/2-n.x,offsetY:e.height/2-n.y}:{offsetX:0,offsetY:0}))}}o(sR,"type","Gradient"),$.setClass(sR,"gradient"),$.setClass(sR,"linear"),$.setClass(sR,"radial");let sI=["type","source","patternTransform"];class sB{get type(){return"pattern"}set type(t){g("warn","Setting type has no effect",t)}constructor(t){o(this,"repeat","repeat"),o(this,"offsetX",0),o(this,"offsetY",0),o(this,"crossOrigin",""),this.id=tg(),Object.assign(this,t)}isImageSource(){return!!this.source&&"string"==typeof this.source.src}isCanvasSource(){return!!this.source&&!!this.source.toDataURL}sourceToString(){return this.isImageSource()?this.source.src:this.isCanvasSource()?this.source.toDataURL():""}toLive(t){return this.source&&(!this.isImageSource()||this.source.complete&&0!==this.source.naturalWidth&&0!==this.source.naturalHeight)?t.createPattern(this.source,this.repeat):null}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],{repeat:e,crossOrigin:i}=this;return l(l({},tX(this,t)),{},{type:"pattern",source:this.sourceToString(),repeat:e,crossOrigin:i,offsetX:tq(this.offsetX,d.NUM_FRACTION_DIGITS),offsetY:tq(this.offsetY,d.NUM_FRACTION_DIGITS),patternTransform:this.patternTransform?[...this.patternTransform]:null})}toSVG(t){let{width:e,height:i}=t,{source:r,repeat:s,id:n}=this,o=sD(this.offsetX/e,0),a=sD(this.offsetY/i,0),l="repeat-y"===s||"no-repeat"===s?1+Math.abs(o||0):sD(r.width/e,0),h="repeat-x"===s||"no-repeat"===s?1+Math.abs(a||0):sD(r.height/i,0);return['<pattern id="SVGID_'.concat(n,'" x="').concat(o,'" y="').concat(a,'" width="').concat(l,'" height="').concat(h,'">'),'<image x="0" y="0" width="'.concat(r.width,'" height="').concat(r.height,'" xlink:href="').concat(this.sourceToString(),'"></image>'),"</pattern>",""].join("\n")}static async fromObject(t,e){let{type:i,source:r,patternTransform:s}=t,n=h(t,sI),o=await tR(r,l(l({},e),{},{crossOrigin:n.crossOrigin}));return new this(l(l({},n),{},{patternTransform:s&&s.slice(0),source:o}))}}o(sB,"type","Pattern"),$.setClass(sB),$.setClass(sB,"pattern");class sX{constructor(t){o(this,"color","rgb(0, 0, 0)"),o(this,"width",1),o(this,"shadow",null),o(this,"strokeLineCap","round"),o(this,"strokeLineJoin","round"),o(this,"strokeMiterLimit",10),o(this,"strokeDashArray",null),o(this,"limitedToCanvasSize",!1),this.canvas=t}_setBrushStyles(t){t.strokeStyle=this.color,t.lineWidth=this.width,t.lineCap=this.strokeLineCap,t.miterLimit=this.strokeMiterLimit,t.lineJoin=this.strokeLineJoin,t.setLineDash(this.strokeDashArray||[])}_saveAndTransform(t){let e=this.canvas.viewportTransform;t.save(),t.transform(e[0],e[1],e[2],e[3],e[4],e[5])}needsFullRender(){return 1>new tU(this.color).getAlpha()||!!this.shadow}_setShadow(){if(!this.shadow||!this.canvas)return;let t=this.canvas,e=this.shadow,i=t.contextTop,r=t.getZoom()*t.getRetinaScaling();i.shadowColor=e.color,i.shadowBlur=e.blur*r,i.shadowOffsetX=e.offsetX*r,i.shadowOffsetY=e.offsetY*r}_resetShadow(){let t=this.canvas.contextTop;t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0}_isOutSideCanvas(t){return t.x<0||t.x>this.canvas.getWidth()||t.y<0||t.y>this.canvas.getHeight()}}let sY=["path","left","top"],sW=["d"];class sV extends i4{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{path:i,left:r,top:s}=e,n=h(e,sY);super(),Object.assign(this,sV.ownDefaults),this.setOptions(n),this._setPath(t||[],!0),"number"==typeof r&&this.set(P,r),"number"==typeof s&&this.set("top",s)}_setPath(t,e){this.path=r5(Array.isArray(t)?t:sc(t)),this.setBoundingBox(e)}_findCenterFromElement(){let t=this._calcBoundsFromPath();return new tn(t.left+t.width/2,t.top+t.height/2)}_renderPathCommands(t){let e=-this.pathOffset.x,i=-this.pathOffset.y;for(let r of(t.beginPath(),this.path))switch(r[0]){case"L":t.lineTo(r[1]+e,r[2]+i);break;case"M":t.moveTo(r[1]+e,r[2]+i);break;case"C":t.bezierCurveTo(r[1]+e,r[2]+i,r[3]+e,r[4]+i,r[5]+e,r[6]+i);break;case"Q":t.quadraticCurveTo(r[1]+e,r[2]+i,r[3]+e,r[4]+i);break;case"Z":t.closePath()}}_render(t){this._renderPathCommands(t),this._renderPaintInOrder(t)}toString(){return"#<Path (".concat(this.complexity(),'): { "top": ').concat(this.top,', "left": ').concat(this.left," }>")}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return l(l({},super.toObject(t)),{},{path:this.path.map(t=>t.slice())})}toDatalessObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=this.toObject(t);return this.sourcePath&&(delete e.path,e.sourcePath=this.sourcePath),e}_toSVG(){let t=sd(this.path,d.NUM_FRACTION_DIGITS);return["<path ","COMMON_PARTS",'d="'.concat(t,'" stroke-linecap="round" />\n')]}_getOffsetTransform(){let t=d.NUM_FRACTION_DIGITS;return" translate(".concat(tq(-this.pathOffset.x,t),", ").concat(tq(-this.pathOffset.y,t),")")}toClipPathSVG(t){let e=this._getOffsetTransform();return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}toSVG(t){let e=this._getOffsetTransform();return this._createBaseSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}complexity(){return this.path.length}setDimensions(){this.setBoundingBox()}setBoundingBox(t){let{width:e,height:i,pathOffset:r}=this._calcDimensions();this.set({width:e,height:i,pathOffset:r}),t&&this.setPositionByOrigin(r,E,E)}_calcBoundsFromPath(){let t=[],e=0,i=0,r=0,s=0;for(let n of this.path)switch(n[0]){case"L":r=n[1],s=n[2],t.push({x:e,y:i},{x:r,y:s});break;case"M":r=n[1],s=n[2],e=r,i=s;break;case"C":t.push(...r1(r,s,n[1],n[2],n[3],n[4],n[5],n[6])),r=n[5],s=n[6];break;case"Q":t.push(...r1(r,s,n[1],n[2],n[1],n[2],n[3],n[4])),r=n[3],s=n[4];break;case"Z":r=e,s=i}return eo(t)}_calcDimensions(){let t=this._calcBoundsFromPath();return l(l({},t),{},{pathOffset:new tn(t.left+t.width/2,t.top+t.height/2)})}static fromObject(t){return this._fromObject(t,{extraParam:"path"})}static async fromElement(t,e,i){let r=rM(t,this.ATTRIBUTE_NAMES,i),{d:s}=r;return new this(s,l(l(l({},h(r,sW)),e),{},{left:void 0,top:void 0}))}}o(sV,"type","Path"),o(sV,"cacheProperties",[...e2,"path","fillRule"]),o(sV,"ATTRIBUTE_NAMES",[...rc,"d"]),$.setClass(sV),$.setSVGClass(sV);class sH extends sX{constructor(t){super(t),o(this,"decimate",.4),o(this,"drawStraightLine",!1),o(this,"straightLineKey","shiftKey"),this._points=[],this._hasStraightLine=!1}needsFullRender(){return super.needsFullRender()||this._hasStraightLine}static drawSegment(t,e,i){let r=e.midPointFrom(i);return t.quadraticCurveTo(e.x,e.y,r.x,r.y),r}onMouseDown(t,e){let{e:i}=e;this.canvas._isMainEvent(i)&&(this.drawStraightLine=!!this.straightLineKey&&i[this.straightLineKey],this._prepareForDrawing(t),this._addPoint(t),this._render())}onMouseMove(t,e){let{e:i}=e;if(this.canvas._isMainEvent(i)&&(this.drawStraightLine=!!this.straightLineKey&&i[this.straightLineKey],(!0!==this.limitedToCanvasSize||!this._isOutSideCanvas(t))&&this._addPoint(t)&&this._points.length>1)){if(this.needsFullRender())this.canvas.clearContext(this.canvas.contextTop),this._render();else{let t=this._points,e=t.length,i=this.canvas.contextTop;this._saveAndTransform(i),this.oldEnd&&(i.beginPath(),i.moveTo(this.oldEnd.x,this.oldEnd.y)),this.oldEnd=sH.drawSegment(i,t[e-2],t[e-1]),i.stroke(),i.restore()}}}onMouseUp(t){let{e:e}=t;return!this.canvas._isMainEvent(e)||(this.drawStraightLine=!1,this.oldEnd=void 0,this._finalizeAndAddPath(),!1)}_prepareForDrawing(t){this._reset(),this._addPoint(t),this.canvas.contextTop.moveTo(t.x,t.y)}_addPoint(t){return!(this._points.length>1&&t.eq(this._points[this._points.length-1]))&&(this.drawStraightLine&&this._points.length>1&&(this._hasStraightLine=!0,this._points.pop()),this._points.push(t),!0)}_reset(){this._points=[],this._setBrushStyles(this.canvas.contextTop),this._setShadow(),this._hasStraightLine=!1}_render(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.canvas.contextTop,e=this._points[0],i=this._points[1];if(this._saveAndTransform(t),t.beginPath(),2===this._points.length&&e.x===i.x&&e.y===i.y){let t=this.width/1e3;e.x-=t,i.x+=t}t.moveTo(e.x,e.y);for(let r=1;r<this._points.length;r++)sH.drawSegment(t,e,i),e=this._points[r],i=this._points[r+1];t.lineTo(e.x,e.y),t.stroke(),t.restore()}convertPointsToSVGPath(t){return su(t,this.width/1e3)}createPath(t){let e=new sV(t,{fill:null,stroke:this.color,strokeWidth:this.width,strokeLineCap:this.strokeLineCap,strokeMiterLimit:this.strokeMiterLimit,strokeLineJoin:this.strokeLineJoin,strokeDashArray:this.strokeDashArray});return this.shadow&&(this.shadow.affectStroke=!0,e.shadow=new e$(this.shadow)),e}decimatePoints(t,e){if(t.length<=2)return t;let i=t[0],r=Math.pow(e/this.canvas.getZoom(),2),s=t.length-1,n=[i];for(let e=1;e<s-1;e++)Math.pow(i.x-t[e].x,2)+Math.pow(i.y-t[e].y,2)>=r&&n.push(i=t[e]);return n.push(t[s]),n}_finalizeAndAddPath(){this.canvas.contextTop.closePath(),this.decimate&&(this._points=this.decimatePoints(this._points,this.decimate));let t=this.convertPointsToSVGPath(this._points);if("M 0 0 Q 0 0 0 0 L 0 0"===sd(t))return void this.canvas.requestRenderAll();let e=this.createPath(t);this.canvas.clearContext(this.canvas.contextTop),this.canvas.fire("before:path:created",{path:e}),this.canvas.add(e),this.canvas.requestRenderAll(),e.setCoords(),this._resetShadow(),this.canvas.fire("path:created",{path:e})}}let sz=["left","top","radius"],sG=["radius","startAngle","endAngle","counterClockwise"];class sN extends i4{static getDefaults(){return l(l({},super.getDefaults()),sN.ownDefaults)}constructor(t){super(),Object.assign(this,sN.ownDefaults),this.setOptions(t)}_set(t,e){return super._set(t,e),"radius"===t&&this.setRadius(e),this}_render(t){t.beginPath(),t.arc(0,0,this.radius,ty(this.startAngle),ty(this.endAngle),this.counterClockwise),this._renderPaintInOrder(t)}getRadiusX(){return this.get("radius")*this.get(G)}getRadiusY(){return this.get("radius")*this.get(N)}setRadius(t){this.radius=t,this.set({width:2*t,height:2*t})}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return super.toObject([...sG,...t])}_toSVG(){let t=(this.endAngle-this.startAngle)%360;if(0===t)return["<circle ","COMMON_PARTS",'cx="0" cy="0" ','r="',"".concat(this.radius),'" />\n'];{let{radius:e}=this,i=ty(this.startAngle),r=ty(this.endAngle),s=tr(i)*e,n=ts(i)*e,o=tr(r)*e,a=ts(r)*e,l=+!this.counterClockwise;return['<path d="M '.concat(s," ").concat(n," A ").concat(e," ").concat(e," 0 ").concat(+(t>180)," ").concat(l," ").concat(o," ").concat(a,'" '),"COMMON_PARTS"," />\n"]}}static async fromElement(t,e,i){let r=rM(t,this.ATTRIBUTE_NAMES,i),{left:s=0,top:n=0,radius:o=0}=r;return new this(l(l({},h(r,sz)),{},{radius:o,left:s-o,top:n-o}))}static fromObject(t){return super._fromObject(t)}}o(sN,"type","Circle"),o(sN,"cacheProperties",[...e2,...sG]),o(sN,"ownDefaults",{radius:0,startAngle:0,endAngle:360,counterClockwise:!1}),o(sN,"ATTRIBUTE_NAMES",["cx","cy","r",...rc]),$.setClass(sN),$.setSVGClass(sN);let sU=["x1","y1","x2","y2"],sq=["x1","y1","x2","y2"],sK=["x1","x2","y1","y2"];class sJ extends i4{constructor(){let[t,e,i,r]=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[0,0,0,0],s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),Object.assign(this,sJ.ownDefaults),this.setOptions(s),this.x1=t,this.x2=i,this.y1=e,this.y2=r,this._setWidthHeight();let{left:n,top:o}=s;"number"==typeof n&&this.set(P,n),"number"==typeof o&&this.set("top",o)}_setWidthHeight(){let{x1:t,y1:e,x2:i,y2:r}=this;this.width=Math.abs(i-t),this.height=Math.abs(r-e);let{left:s,top:n,width:o,height:a}=eo([{x:t,y:e},{x:i,y:r}]),l=new tn(s+o/2,n+a/2);this.setPositionByOrigin(l,E,E)}_set(t,e){return super._set(t,e),sK.includes(t)&&this._setWidthHeight(),this}_render(t){var e;t.beginPath();let i=this.calcLinePoints();t.moveTo(i.x1,i.y1),t.lineTo(i.x2,i.y2),t.lineWidth=this.strokeWidth;let r=t.strokeStyle;t$(this.stroke)?t.strokeStyle=this.stroke.toLive(t):t.strokeStyle=null!==(e=this.stroke)&&void 0!==e?e:t.fillStyle,this.stroke&&this._renderStroke(t),t.strokeStyle=r}_findCenterFromElement(){return new tn((this.x1+this.x2)/2,(this.y1+this.y2)/2)}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return l(l({},super.toObject(t)),this.calcLinePoints())}_getNonTransformedDimensions(){let t=super._getNonTransformedDimensions();return"butt"===this.strokeLineCap&&(0===this.width&&(t.y-=this.strokeWidth),0===this.height&&(t.x-=this.strokeWidth)),t}calcLinePoints(){let{x1:t,x2:e,y1:i,y2:r,width:s,height:n}=this,o=t<=e?-1:1,a=i<=r?-1:1;return{x1:o*s/2,x2:-(o*s)/2,y1:a*n/2,y2:-(a*n)/2}}_toSVG(){let{x1:t,x2:e,y1:i,y2:r}=this.calcLinePoints();return["<line ","COMMON_PARTS",'x1="'.concat(t,'" y1="').concat(i,'" x2="').concat(e,'" y2="').concat(r,'" />\n')]}static async fromElement(t,e,i){let r=rM(t,this.ATTRIBUTE_NAMES,i),{x1:s=0,y1:n=0,x2:o=0,y2:a=0}=r;return new this([s,n,o,a],h(r,sU))}static fromObject(t){let{x1:e,y1:i,x2:r,y2:s}=t,n=h(t,sq);return this._fromObject(l(l({},n),{},{points:[e,i,r,s]}),{extraParam:"points"})}}o(sJ,"type","Line"),o(sJ,"cacheProperties",[...e2,...sK]),o(sJ,"ATTRIBUTE_NAMES",rc.concat(sK)),$.setClass(sJ),$.setSVGClass(sJ);class sQ extends i4{static getDefaults(){return l(l({},super.getDefaults()),sQ.ownDefaults)}constructor(t){super(),Object.assign(this,sQ.ownDefaults),this.setOptions(t)}_render(t){let e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,i),t.lineTo(0,-i),t.lineTo(e,i),t.closePath(),this._renderPaintInOrder(t)}_toSVG(){let t=this.width/2,e=this.height/2;return["<polygon ","COMMON_PARTS",'points="',"".concat(-t," ").concat(e,",0 ").concat(-e,",").concat(t," ").concat(e),'" />']}}o(sQ,"type","Triangle"),o(sQ,"ownDefaults",{width:100,height:100}),$.setClass(sQ),$.setSVGClass(sQ);let sZ=["rx","ry"];class s$ extends i4{static getDefaults(){return l(l({},super.getDefaults()),s$.ownDefaults)}constructor(t){super(),Object.assign(this,s$.ownDefaults),this.setOptions(t)}_set(t,e){switch(super._set(t,e),t){case"rx":this.rx=e,this.set("width",2*e);break;case"ry":this.ry=e,this.set("height",2*e)}return this}getRx(){return this.get("rx")*this.get(G)}getRy(){return this.get("ry")*this.get(N)}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return super.toObject([...sZ,...t])}_toSVG(){return["<ellipse ","COMMON_PARTS",'cx="0" cy="0" rx="'.concat(this.rx,'" ry="').concat(this.ry,'" />\n')]}_render(t){t.beginPath(),t.save(),t.transform(1,0,0,this.ry/this.rx,0,0),t.arc(0,0,this.rx,0,D,!1),t.restore(),this._renderPaintInOrder(t)}static async fromElement(t,e,i){let r=rM(t,this.ATTRIBUTE_NAMES,i);return r.left=(r.left||0)-r.rx,r.top=(r.top||0)-r.ry,new this(r)}}o(s$,"type","Ellipse"),o(s$,"cacheProperties",[...e2,...sZ]),o(s$,"ownDefaults",{rx:0,ry:0}),o(s$,"ATTRIBUTE_NAMES",[...rc,"cx","cy","rx","ry"]),$.setClass(s$),$.setSVGClass(s$);let s0=["left","top"],s1={exactBoundingBox:!1};class s2 extends i4{static getDefaults(){return l(l({},super.getDefaults()),s2.ownDefaults)}constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),o(this,"strokeDiff",void 0),Object.assign(this,s2.ownDefaults),this.setOptions(e),this.points=t;let{left:i,top:r}=e;this.initialized=!0,this.setBoundingBox(!0),"number"==typeof i&&this.set(P,i),"number"==typeof r&&this.set("top",r)}isOpen(){return!0}_projectStrokeOnPoints(t){return re(this.points,t,this.isOpen())}_calcDimensions(t){t=l({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,strokeLineCap:this.strokeLineCap,strokeLineJoin:this.strokeLineJoin,strokeMiterLimit:this.strokeMiterLimit,strokeUniform:this.strokeUniform,strokeWidth:this.strokeWidth},t||{});let e=this.exactBoundingBox?this._projectStrokeOnPoints(t).map(t=>t.projectedPoint):this.points;if(0===e.length)return{left:0,top:0,width:0,height:0,pathOffset:new tn,strokeOffset:new tn,strokeDiff:new tn};let i=eo(e),r=tF(l(l({},t),{},{scaleX:1,scaleY:1})),s=eo(this.points.map(t=>tb(t,r,!0))),n=new tn(this.scaleX,this.scaleY),o=i.left+i.width/2,a=i.top+i.height/2;return this.exactBoundingBox&&(o-=a*Math.tan(ty(this.skewX)),a-=o*Math.tan(ty(this.skewY))),l(l({},i),{},{pathOffset:new tn(o,a),strokeOffset:new tn(s.left,s.top).subtract(new tn(i.left,i.top)).multiply(n),strokeDiff:new tn(i.width,i.height).subtract(new tn(s.width,s.height)).multiply(n)})}_findCenterFromElement(){let t=eo(this.points);return new tn(t.left+t.width/2,t.top+t.height/2)}setDimensions(){this.setBoundingBox()}setBoundingBox(t){let{left:e,top:i,width:r,height:s,pathOffset:n,strokeOffset:o,strokeDiff:a}=this._calcDimensions();this.set({width:r,height:s,pathOffset:n,strokeOffset:o,strokeDiff:a}),t&&this.setPositionByOrigin(new tn(e+r/2,i+s/2),E,E)}isStrokeAccountedForInDimensions(){return this.exactBoundingBox}_getNonTransformedDimensions(){return this.exactBoundingBox?new tn(this.width,this.height):super._getNonTransformedDimensions()}_getTransformedDimensions(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.exactBoundingBox){var e,i,r,s;let n;if(Object.keys(t).some(t=>this.strokeUniform||this.constructor.layoutProperties.includes(t))){let{width:r,height:s}=this._calcDimensions(t);n=new tn(null!==(e=t.width)&&void 0!==e?e:r,null!==(i=t.height)&&void 0!==i?i:s)}else n=new tn(null!==(r=t.width)&&void 0!==r?r:this.width,null!==(s=t.height)&&void 0!==s?s:this.height);return n.multiply(new tn(t.scaleX||this.scaleX,t.scaleY||this.scaleY))}return super._getTransformedDimensions(t)}_set(t,e){let i=this.initialized&&this[t]!==e,r=super._set(t,e);return this.exactBoundingBox&&i&&((t===G||t===N)&&this.strokeUniform&&this.constructor.layoutProperties.includes("strokeUniform")||this.constructor.layoutProperties.includes(t))&&this.setDimensions(),r}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return l(l({},super.toObject(t)),{},{points:this.points.map(t=>{let{x:e,y:i}=t;return{x:e,y:i}})})}_toSVG(){let t=[],e=this.pathOffset.x,i=this.pathOffset.y,r=d.NUM_FRACTION_DIGITS;for(let s=0,n=this.points.length;s<n;s++)t.push(tq(this.points[s].x-e,r),",",tq(this.points[s].y-i,r)," ");return["<".concat(this.constructor.type.toLowerCase()," "),"COMMON_PARTS",'points="'.concat(t.join(""),'" />\n')]}_render(t){let e=this.points.length,i=this.pathOffset.x,r=this.pathOffset.y;if(e&&!isNaN(this.points[e-1].y)){t.beginPath(),t.moveTo(this.points[0].x-i,this.points[0].y-r);for(let s=0;s<e;s++){let e=this.points[s];t.lineTo(e.x-i,e.y-r)}this.isOpen()||t.closePath(),this._renderPaintInOrder(t)}}complexity(){return this.points.length}static async fromElement(t,e,i){return new this(function(t){if(!t)return[];let e=t.replace(/,/g," ").trim().split(/\s+/),i=[];for(let t=0;t<e.length;t+=2)i.push({x:parseFloat(e[t]),y:parseFloat(e[t+1])});return i}(t.getAttribute("points")),l(l({},h(rM(t,this.ATTRIBUTE_NAMES,i),s0)),e))}static fromObject(t){return this._fromObject(t,{extraParam:"points"})}}o(s2,"ownDefaults",s1),o(s2,"type","Polyline"),o(s2,"layoutProperties",[U,q,"strokeLineCap","strokeLineJoin","strokeMiterLimit","strokeWidth","strokeUniform","points"]),o(s2,"cacheProperties",[...e2,"points"]),o(s2,"ATTRIBUTE_NAMES",[...rc]),$.setClass(s2),$.setSVGClass(s2);class s5 extends s2{isOpen(){return!1}}o(s5,"ownDefaults",s1),o(s5,"type","Polygon"),$.setClass(s5),$.setSVGClass(s5);let s3=["fontSize","fontWeight","fontFamily","fontStyle"],s4=["underline","overline","linethrough"],s8=[...s3,"lineHeight","text","charSpacing","textAlign","styles","path","pathStartOffset","pathSide","pathAlign"],s6=[...s8,...s4,"textBackgroundColor","direction"],s9=[...s3,...s4,J,"strokeWidth",K,"deltaY","textBackgroundColor"],s7="justify",nt="justify-left",ne="justify-right",ni="justify-center";class nr extends i4{isEmptyStyles(t){if(!this.styles||void 0!==t&&!this.styles[t])return!0;let e=void 0===t?this.styles:{line:this.styles[t]};for(let t in e)for(let i in e[t])for(let r in e[t][i])return!1;return!0}styleHas(t,e){if(!this.styles||void 0!==e&&!this.styles[e])return!1;let i=void 0===e?this.styles:{0:this.styles[e]};for(let e in i)for(let r in i[e])if(void 0!==i[e][r][t])return!0;return!1}cleanStyle(t){if(!this.styles)return!1;let e=this.styles,i,r,s=0,n=!0,o=0;for(let o in e){for(let a in i=0,e[o]){let l=e[o][a]||{};s++,void 0!==l[t]?(r?l[t]!==r&&(n=!1):r=l[t],l[t]===this[t]&&delete l[t]):n=!1,0!==Object.keys(l).length?i++:delete e[o][a]}0===i&&delete e[o]}for(let t=0;t<this._textLines.length;t++)o+=this._textLines[t].length;n&&s===o&&(this[t]=r,this.removeStyle(t))}removeStyle(t){let e,i,r;if(!this.styles)return;let s=this.styles;for(i in s){for(r in e=s[i])delete e[r][t],0===Object.keys(e[r]).length&&delete e[r];0===Object.keys(e).length&&delete s[i]}}_extendStyles(t,e){let{lineIndex:i,charIndex:r}=this.get2DCursorLocation(t);this._getLineStyle(i)||this._setLineStyle(i);let s=tY(l(l({},this._getStyleDeclaration(i,r)),e),t=>void 0!==t);this._setStyleDeclaration(i,r,s)}getSelectionStyles(t,e,i){let r=[];for(let s=t;s<(e||t);s++)r.push(this.getStyleAtPosition(s,i));return r}getStyleAtPosition(t,e){let{lineIndex:i,charIndex:r}=this.get2DCursorLocation(t);return e?this.getCompleteStyleDeclaration(i,r):this._getStyleDeclaration(i,r)}setSelectionStyles(t,e,i){for(let r=e;r<(i||e);r++)this._extendStyles(r,t);this._forceClearCache=!0}_getStyleDeclaration(t,e){var i;let r=this.styles&&this.styles[t];return r&&null!==(i=r[e])&&void 0!==i?i:{}}getCompleteStyleDeclaration(t,e){return l(l({},tX(this,this.constructor._styleProperties)),this._getStyleDeclaration(t,e))}_setStyleDeclaration(t,e,i){this.styles[t][e]=i}_deleteStyleDeclaration(t,e){delete this.styles[t][e]}_getLineStyle(t){return!!this.styles[t]}_setLineStyle(t){this.styles[t]={}}_deleteLineStyle(t){delete this.styles[t]}}o(nr,"_styleProperties",s9);let ns=/  +/g,nn=/"/g;function no(t,e,i,r,s){return"		".concat(function(t,e){let{left:i,top:r,width:s,height:n}=e,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:d.NUM_FRACTION_DIGITS,a=tZ(K,t,!1),[l,h,c,u]=[i,r,s,n].map(t=>tq(t,o));return"<rect ".concat(a,' x="').concat(l,'" y="').concat(h,'" width="').concat(c,'" height="').concat(u,'"></rect>')}(t,{left:e,top:i,width:r,height:s}),"\n")}let na=["textAnchor","textDecoration","dx","dy","top","left","fontSize","strokeWidth"];class nl extends nr{static getDefaults(){return l(l({},super.getDefaults()),nl.ownDefaults)}constructor(t,e){super(),o(this,"__charBounds",[]),Object.assign(this,nl.ownDefaults),this.setOptions(e),this.styles||(this.styles={}),this.text=t,this.initialized=!0,this.path&&this.setPathInfo(),this.initDimensions(),this.setCoords()}setPathInfo(){let t=this.path;t&&(t.segmentsInfo=ss(t.path))}_splitText(){let t=this._splitTextIntoLines(this.text);return this.textLines=t.lines,this._textLines=t.graphemeLines,this._unwrappedTextLines=t._unwrappedLines,this._text=t.graphemeText,t}initDimensions(){this._splitText(),this._clearCache(),this.dirty=!0,this.path?(this.width=this.path.width,this.height=this.path.height):(this.width=this.calcTextWidth()||this.cursorWidth||this.MIN_TEXT_WIDTH,this.height=this.calcTextHeight()),this.textAlign.includes(s7)&&this.enlargeSpaces()}enlargeSpaces(){let t,e,i,r,s,n,o;for(let a=0,l=this._textLines.length;a<l;a++)if((this.textAlign===s7||a!==l-1&&!this.isEndOfWrapping(a))&&(r=0,s=this._textLines[a],(e=this.getLineWidth(a))<this.width&&(o=this.textLines[a].match(this._reSpacesAndTabs)))){i=o.length,t=(this.width-e)/i;for(let e=0;e<=s.length;e++)n=this.__charBounds[a][e],this._reSpaceAndTab.test(s[e])?(n.width+=t,n.kernedWidth+=t,n.left+=r,r+=t):n.left+=r}}isEndOfWrapping(t){return t===this._textLines.length-1}missingNewlineOffset(t){return 1}get2DCursorLocation(t,e){let i;let r=e?this._unwrappedTextLines:this._textLines;for(i=0;i<r.length;i++){if(t<=r[i].length)return{lineIndex:i,charIndex:t};t-=r[i].length+this.missingNewlineOffset(i,e)}return{lineIndex:i-1,charIndex:r[i-1].length<t?r[i-1].length:t}}toString(){return"#<Text (".concat(this.complexity(),'): { "text": "').concat(this.text,'", "fontFamily": "').concat(this.fontFamily,'" }>')}_getCacheCanvasDimensions(){let t=super._getCacheCanvasDimensions(),e=this.fontSize;return t.width+=e*t.zoomX,t.height+=e*t.zoomY,t}_render(t){let e=this.path;e&&!e.isNotVisible()&&e._render(t),this._setTextStyles(t),this._renderTextLinesBackground(t),this._renderTextDecoration(t,"underline"),this._renderText(t),this._renderTextDecoration(t,"overline"),this._renderTextDecoration(t,"linethrough")}_renderText(t){this.paintFirst===J?(this._renderTextStroke(t),this._renderTextFill(t)):(this._renderTextFill(t),this._renderTextStroke(t))}_setTextStyles(t,e,i){if(t.textBaseline="alphabetic",this.path)switch(this.pathAlign){case E:t.textBaseline="middle";break;case"ascender":t.textBaseline="top";break;case"descender":t.textBaseline=A}t.font=this._getFontDeclaration(e,i)}calcTextWidth(){let t=this.getLineWidth(0);for(let e=1,i=this._textLines.length;e<i;e++){let i=this.getLineWidth(e);i>t&&(t=i)}return t}_renderTextLine(t,e,i,r,s,n){this._renderChars(t,e,i,r,s,n)}_renderTextLinesBackground(t){if(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor"))return;let e=t.fillStyle,i=this._getLeftOffset(),r=this._getTopOffset();for(let e=0,s=this._textLines.length;e<s;e++){let s=this.getHeightOfLine(e);if(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor",e)){r+=s;continue}let n=this._textLines[e].length,o=this._getLineLeftOffset(e),a,l,h=0,c=0,u=this.getValueOfPropertyAt(e,0,"textBackgroundColor");for(let d=0;d<n;d++){let n=this.__charBounds[e][d];l=this.getValueOfPropertyAt(e,d,"textBackgroundColor"),this.path?(t.save(),t.translate(n.renderLeft,n.renderTop),t.rotate(n.angle),t.fillStyle=l,l&&t.fillRect(-n.width/2,-s/this.lineHeight*(1-this._fontSizeFraction),n.width,s/this.lineHeight),t.restore()):l!==u?(a=i+o+c,"rtl"===this.direction&&(a=this.width-a-h),t.fillStyle=u,u&&t.fillRect(a,r,h,s/this.lineHeight),c=n.left,h=n.width,u=l):h+=n.kernedWidth}l&&!this.path&&(a=i+o+c,"rtl"===this.direction&&(a=this.width-a-h),t.fillStyle=l,t.fillRect(a,r,h,s/this.lineHeight)),r+=s}t.fillStyle=e,this._removeShadow(t)}_measureChar(t,e,i,r){let n,o,a,l;let h=S.getFontCache(e),c=this._getFontDeclaration(e),u=i+t,d=i&&c===this._getFontDeclaration(r),g=e.fontSize/this.CACHE_FONT_SIZE;if(i&&void 0!==h[i]&&(a=h[i]),void 0!==h[t]&&(l=n=h[t]),d&&void 0!==h[u]&&(l=(o=h[u])-a),void 0===n||void 0===a||void 0===o){let r=(s||(s=tm({width:0,height:0}).getContext("2d")),s);this._setTextStyles(r,e,!0),void 0===n&&(l=n=r.measureText(t).width,h[t]=n),void 0===a&&d&&i&&(a=r.measureText(i).width,h[i]=a),d&&void 0===o&&(o=r.measureText(u).width,h[u]=o,l=o-a)}return{width:n*g,kernedWidth:l*g}}getHeightOfChar(t,e){return this.getValueOfPropertyAt(t,e,"fontSize")}measureLine(t){let e=this._measureLine(t);return 0!==this.charSpacing&&(e.width-=this._getWidthOfCharSpacing()),e.width<0&&(e.width=0),e}_measureLine(t){let e,i,r=0,s=this.pathSide===j,n=this.path,o=this._textLines[t],a=o.length,l=Array(a);this.__charBounds[t]=l;for(let s=0;s<a;s++){let n=o[s];i=this._getGraphemeBox(n,t,s,e),l[s]=i,r+=i.kernedWidth,e=n}if(l[a]={left:i?i.left+i.width:0,width:0,kernedWidth:0,height:this.fontSize,deltaY:0},n&&n.segmentsInfo){let t=0,e=n.segmentsInfo[n.segmentsInfo.length-1].length;switch(this.textAlign){case P:t=s?e-r:0;break;case E:t=(e-r)/2;break;case j:t=s?0:e-r}t+=this.pathStartOffset*(s?-1:1);for(let r=s?a-1:0;s?r>=0:r<a;s?r--:r++)i=l[r],t>e?t%=e:t<0&&(t+=e),this._setGraphemeOnPath(t,i),t+=i.kernedWidth}return{width:r,numOfSpaces:0}}_setGraphemeOnPath(t,e){let i=t+e.kernedWidth/2,r=this.path,s=sn(r.path,i,r.segmentsInfo);e.renderLeft=s.x-r.pathOffset.x,e.renderTop=s.y-r.pathOffset.y,e.angle=s.angle+(this.pathSide===j?Math.PI:0)}_getGraphemeBox(t,e,i,r,s){let n=this.getCompleteStyleDeclaration(e,i),o=r?this.getCompleteStyleDeclaration(e,i-1):{},a=this._measureChar(t,n,r,o),l,h=a.kernedWidth,c=a.width;0!==this.charSpacing&&(c+=l=this._getWidthOfCharSpacing(),h+=l);let u={width:c,left:0,height:n.fontSize,kernedWidth:h,deltaY:n.deltaY};if(i>0&&!s){let t=this.__charBounds[e][i-1];u.left=t.left+t.width+a.kernedWidth-a.width}return u}getHeightOfLine(t){if(this.__lineHeights[t])return this.__lineHeights[t];let e=this.getHeightOfChar(t,0);for(let i=1,r=this._textLines[t].length;i<r;i++)e=Math.max(this.getHeightOfChar(t,i),e);return this.__lineHeights[t]=e*this.lineHeight*this._fontSizeMult}calcTextHeight(){let t,e=0;for(let i=0,r=this._textLines.length;i<r;i++)t=this.getHeightOfLine(i),e+=i===r-1?t/this.lineHeight:t;return e}_getLeftOffset(){return"ltr"===this.direction?-this.width/2:this.width/2}_getTopOffset(){return-this.height/2}_renderTextCommon(t,e){t.save();let i=0,r=this._getLeftOffset(),s=this._getTopOffset();for(let n=0,o=this._textLines.length;n<o;n++){let o=this.getHeightOfLine(n),a=o/this.lineHeight,l=this._getLineLeftOffset(n);this._renderTextLine(e,t,this._textLines[n],r+l,s+i+a,n),i+=o}t.restore()}_renderTextFill(t){(this.fill||this.styleHas(K))&&this._renderTextCommon(t,"fillText")}_renderTextStroke(t){(this.stroke&&0!==this.strokeWidth||!this.isEmptyStyles())&&(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this._setLineDash(t,this.strokeDashArray),t.beginPath(),this._renderTextCommon(t,"strokeText"),t.closePath(),t.restore())}_renderChars(t,e,i,r,s,n){let o=this.getHeightOfLine(n),a=this.textAlign.includes(s7),l=this.path,h=!a&&0===this.charSpacing&&this.isEmptyStyles(n)&&!l,c="ltr"===this.direction,u="ltr"===this.direction?1:-1,d=e.direction,g,f,p,m,v,_="",y=0;if(e.save(),d!==this.direction&&(e.canvas.setAttribute("dir",c?"ltr":"rtl"),e.direction=c?"ltr":"rtl",e.textAlign=c?P:j),s-=o*this._fontSizeFraction/this.lineHeight,h)return this._renderChar(t,e,n,0,i.join(""),r,s),void e.restore();for(let o=0,h=i.length-1;o<=h;o++)m=o===h||this.charSpacing||l,_+=i[o],p=this.__charBounds[n][o],0===y?(r+=u*(p.kernedWidth-p.width),y+=p.width):y+=p.kernedWidth,a&&!m&&this._reSpaceAndTab.test(i[o])&&(m=!0),m||(m=ra(g=g||this.getCompleteStyleDeclaration(n,o),f=this.getCompleteStyleDeclaration(n,o+1),!1)),m&&(l?(e.save(),e.translate(p.renderLeft,p.renderTop),e.rotate(p.angle),this._renderChar(t,e,n,o,_,-y/2,0),e.restore()):(v=r,this._renderChar(t,e,n,o,_,v,s)),_="",g=f,r+=u*y,y=0);e.restore()}_applyPatternGradientTransformText(t){let e=this.width+this.strokeWidth,i=this.height+this.strokeWidth,r=tm({width:e,height:i}),s=r.getContext("2d");return r.width=e,r.height=i,s.beginPath(),s.moveTo(0,0),s.lineTo(e,0),s.lineTo(e,i),s.lineTo(0,i),s.closePath(),s.translate(e/2,i/2),s.fillStyle=t.toLive(s),this._applyPatternGradientTransform(s,t),s.fill(),s.createPattern(r,"no-repeat")}handleFiller(t,e,i){let r,s;return t$(i)?"percentage"===i.gradientUnits||i.gradientTransform||i.patternTransform?(r=-this.width/2,s=-this.height/2,t.translate(r,s),t[e]=this._applyPatternGradientTransformText(i),{offsetX:r,offsetY:s}):(t[e]=i.toLive(t),this._applyPatternGradientTransform(t,i)):(t[e]=i,{offsetX:0,offsetY:0})}_setStrokeStyles(t,e){let{stroke:i,strokeWidth:r}=e;return t.lineWidth=r,t.lineCap=this.strokeLineCap,t.lineDashOffset=this.strokeDashOffset,t.lineJoin=this.strokeLineJoin,t.miterLimit=this.strokeMiterLimit,this.handleFiller(t,"strokeStyle",i)}_setFillStyles(t,e){let{fill:i}=e;return this.handleFiller(t,"fillStyle",i)}_renderChar(t,e,i,r,s,n,o){let a=this._getStyleDeclaration(i,r),l=this.getCompleteStyleDeclaration(i,r),h="fillText"===t&&l.fill,c="strokeText"===t&&l.stroke&&l.strokeWidth;if(c||h){if(e.save(),e.font=this._getFontDeclaration(l),a.textBackgroundColor&&this._removeShadow(e),a.deltaY&&(o+=a.deltaY),h){let t=this._setFillStyles(e,l);e.fillText(s,n-t.offsetX,o-t.offsetY)}if(c){let t=this._setStrokeStyles(e,l);e.strokeText(s,n-t.offsetX,o-t.offsetY)}e.restore()}}setSuperscript(t,e){this._setScript(t,e,this.superscript)}setSubscript(t,e){this._setScript(t,e,this.subscript)}_setScript(t,e,i){let r=this.get2DCursorLocation(t,!0),s=this.getValueOfPropertyAt(r.lineIndex,r.charIndex,"fontSize"),n=this.getValueOfPropertyAt(r.lineIndex,r.charIndex,"deltaY"),o={fontSize:s*i.size,deltaY:n+s*i.baseline};this.setSelectionStyles(o,t,e)}_getLineLeftOffset(t){let e=this.getLineWidth(t),i=this.width-e,r=this.textAlign,s=this.direction,n=this.isEndOfWrapping(t),o=0;return r!==s7&&(r!==ni||n)&&(r!==ne||n)&&(r!==nt||n)?(r===E&&(o=i/2),r===j&&(o=i),r===ni&&(o=i/2),r===ne&&(o=i),"rtl"===s&&(r===j||r===s7||r===ne?o=0:r===P||r===nt?o=-i:r!==E&&r!==ni||(o=-i/2)),o):0}_clearCache(){this._forceClearCache=!1,this.__lineWidths=[],this.__lineHeights=[],this.__charBounds=[]}getLineWidth(t){if(void 0!==this.__lineWidths[t])return this.__lineWidths[t];let{width:e}=this.measureLine(t);return this.__lineWidths[t]=e,e}_getWidthOfCharSpacing(){return 0!==this.charSpacing?this.fontSize*this.charSpacing/1e3:0}getValueOfPropertyAt(t,e,i){var r;return null!==(r=this._getStyleDeclaration(t,e)[i])&&void 0!==r?r:this[i]}_renderTextDecoration(t,e){if(!this[e]&&!this.styleHas(e))return;let i=this._getTopOffset(),r=this._getLeftOffset(),s=this.path,n=this._getWidthOfCharSpacing(),o=this.offsets[e];for(let a=0,l=this._textLines.length;a<l;a++){let l=this.getHeightOfLine(a);if(!this[e]&&!this.styleHas(e,a)){i+=l;continue}let h=this._textLines[a],c=l/this.lineHeight,u=this._getLineLeftOffset(a),d,g,f=0,p=0,m=this.getValueOfPropertyAt(a,0,e),v=this.getValueOfPropertyAt(a,0,K),_=i+c*(1-this._fontSizeFraction),y=this.getHeightOfChar(a,0),x=this.getValueOfPropertyAt(a,0,"deltaY");for(let i=0,n=h.length;i<n;i++){let n=this.__charBounds[a][i];d=this.getValueOfPropertyAt(a,i,e),g=this.getValueOfPropertyAt(a,i,K);let l=this.getHeightOfChar(a,i),h=this.getValueOfPropertyAt(a,i,"deltaY");if(s&&d&&g)t.save(),t.fillStyle=v,t.translate(n.renderLeft,n.renderTop),t.rotate(n.angle),t.fillRect(-n.kernedWidth/2,o*l+h,n.kernedWidth,this.fontSize/15),t.restore();else if((d!==m||g!==v||l!==y||h!==x)&&p>0){let e=r+u+f;"rtl"===this.direction&&(e=this.width-e-p),m&&v&&(t.fillStyle=v,t.fillRect(e,_+o*y+x,p,this.fontSize/15)),f=n.left,p=n.width,m=d,v=g,y=l,x=h}else p+=n.kernedWidth}let C=r+u+f;"rtl"===this.direction&&(C=this.width-C-p),t.fillStyle=g,d&&g&&t.fillRect(C,_+o*y+x,p-n,this.fontSize/15),i+=l}this._removeShadow(t)}_getFontDeclaration(){let{fontFamily:t=this.fontFamily,fontStyle:e=this.fontStyle,fontWeight:i=this.fontWeight,fontSize:r=this.fontSize}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=arguments.length>1?arguments[1]:void 0,n=t.includes("'")||t.includes('"')||t.includes(",")||nl.genericFonts.includes(t.toLowerCase())?t:'"'.concat(t,'"');return[e,i,"".concat(s?this.CACHE_FONT_SIZE:r,"px"),n].join(" ")}render(t){this.visible&&(this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(this._forceClearCache&&this.initDimensions(),super.render(t)))}graphemeSplit(t){return rs(t)}_splitTextIntoLines(t){let e=t.split(this._reNewline),i=Array(e.length),r=["\n"],s=[];for(let t=0;t<e.length;t++)i[t]=this.graphemeSplit(e[t]),s=s.concat(i[t],r);return s.pop(),{_unwrappedLines:i,lines:e,graphemeText:s,graphemeLines:i}}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return l(l({},super.toObject([...s6,...t])),{},{styles:rl(this.styles,this.text)},this.path?{path:this.path.toObject()}:{})}set(t,e){let{textLayoutProperties:i}=this.constructor;super.set(t,e);let r=!1,s=!1;if("object"==typeof t)for(let e in t)"path"===e&&this.setPathInfo(),r=r||i.includes(e),s=s||"path"===e;else r=i.includes(t),s="path"===t;return s&&this.setPathInfo(),r&&this.initialized&&(this.initDimensions(),this.setCoords()),this}complexity(){return 1}static async fromElement(t,e,i){let r=rM(t,nl.ATTRIBUTE_NAMES,i),s=l(l({},e),r),{textAnchor:n=P,textDecoration:o="",dx:a=0,dy:c=0,top:u=0,left:d=0,fontSize:g=16,strokeWidth:f=1}=s,p=h(s,na),m=new this((t.textContent||"").replace(/^\s+|\s+$|\n+/g,"").replace(/\s+/g," "),l({left:d+a,top:u+c,underline:o.includes("underline"),overline:o.includes("overline"),linethrough:o.includes("line-through"),strokeWidth:0,fontSize:g},p)),v=m.getScaledHeight()/m.height,_=((m.height+m.strokeWidth)*m.lineHeight-m.height)*v,y=m.getScaledHeight()+_,x=0;return n===E&&(x=m.getScaledWidth()/2),n===j&&(x=m.getScaledWidth()),m.set({left:m.left-x,top:m.top-(y-m.fontSize*(.07+m._fontSizeFraction))/m.lineHeight,strokeWidth:f}),m}static fromObject(t){return this._fromObject(l(l({},t),{},{styles:rh(t.styles||{},t.text)}),{extraParam:"text"})}}o(nl,"textLayoutProperties",s8),o(nl,"cacheProperties",[...e2,...s6]),o(nl,"ownDefaults",{_reNewline:L,_reSpacesAndTabs:/[ \t\r]/g,_reSpaceAndTab:/[ \t\r]/,_reWords:/\S+/g,fontSize:40,fontWeight:"normal",fontFamily:"Times New Roman",underline:!1,overline:!1,linethrough:!1,textAlign:P,fontStyle:"normal",lineHeight:1.16,superscript:{size:.6,baseline:-.35},subscript:{size:.6,baseline:.11},textBackgroundColor:"",stroke:null,shadow:null,path:void 0,pathStartOffset:0,pathSide:P,pathAlign:"baseline",_fontSizeFraction:.222,offsets:{underline:.1,linethrough:-.315,overline:-.88},_fontSizeMult:1.13,charSpacing:0,deltaY:0,direction:"ltr",CACHE_FONT_SIZE:400,MIN_TEXT_WIDTH:2}),o(nl,"type","Text"),o(nl,"genericFonts",["serif","sans-serif","monospace","cursive","fantasy","system-ui","ui-serif","ui-sans-serif","ui-monospace","ui-rounded","math","emoji","fangsong"]),o(nl,"ATTRIBUTE_NAMES",rc.concat("x","y","dx","dy","font-family","font-style","font-weight","font-size","letter-spacing","text-decoration","text-anchor")),i3(nl,[class extends eD{_toSVG(){let t=this._getSVGLeftTopOffsets(),e=this._getSVGTextAndBg(t.textTop,t.textLeft);return this._wrapSVGTextAndBg(e)}toSVG(t){return this._createBaseSVGMarkup(this._toSVG(),{reviver:t,noStyle:!0,withShadow:!0})}_getSVGLeftTopOffsets(){return{textLeft:-this.width/2,textTop:-this.height/2,lineTop:this.getHeightOfLine(0)}}_wrapSVGTextAndBg(t){let{textBgRects:e,textSpans:i}=t,r=this.getSvgTextDecoration(this);return[e.join(""),'		<text xml:space="preserve" ',this.fontFamily?'font-family="'.concat(this.fontFamily.replace(nn,"'"),'" '):"",this.fontSize?'font-size="'.concat(this.fontSize,'" '):"",this.fontStyle?'font-style="'.concat(this.fontStyle,'" '):"",this.fontWeight?'font-weight="'.concat(this.fontWeight,'" '):"",r?'text-decoration="'.concat(r,'" '):"","rtl"===this.direction?'direction="'.concat(this.direction,'" '):"",'style="',this.getSvgStyles(!0),'"',this.addPaintOrder()," >",i.join(""),"</text>\n"]}_getSVGTextAndBg(t,e){let i=[],r=[],s,n=t;this.backgroundColor&&r.push(...no(this.backgroundColor,-this.width/2,-this.height/2,this.width,this.height));for(let t=0,o=this._textLines.length;t<o;t++)s=this._getLineLeftOffset(t),"rtl"===this.direction&&(s+=this.width),(this.textBackgroundColor||this.styleHas("textBackgroundColor",t))&&this._setSVGTextLineBg(r,t,e+s,n),this._setSVGTextLineText(i,t,e+s,n),n+=this.getHeightOfLine(t);return{textSpans:i,textBgRects:r}}_createTextCharSpan(t,e,i,r){let s=this.getSvgSpanStyles(e,t!==t.trim()||!!t.match(ns)),n=e.deltaY,o=n?' dy="'.concat(tq(n,d.NUM_FRACTION_DIGITS),'" '):"";return'<tspan x="'.concat(tq(i,d.NUM_FRACTION_DIGITS),'" y="').concat(tq(r,d.NUM_FRACTION_DIGITS),'" ').concat(o).concat(s?'style="'.concat(s,'"'):"",">").concat(rr(t),"</tspan>")}_setSVGTextLineText(t,e,i,r){let s=this.getHeightOfLine(e),n=this.textAlign.includes(s7),o=this._textLines[e],a,l,h,c,u,d="",g=0;r+=s*(1-this._fontSizeFraction)/this.lineHeight;for(let s=0,f=o.length-1;s<=f;s++)u=s===f||this.charSpacing,d+=o[s],h=this.__charBounds[e][s],0===g?(i+=h.kernedWidth-h.width,g+=h.width):g+=h.kernedWidth,n&&!u&&this._reSpaceAndTab.test(o[s])&&(u=!0),u||(u=ra(a=a||this.getCompleteStyleDeclaration(e,s),l=this.getCompleteStyleDeclaration(e,s+1),!0)),u&&(c=this._getStyleDeclaration(e,s),t.push(this._createTextCharSpan(d,c,i,r)),d="",a=l,"rtl"===this.direction?i-=g:i+=g,g=0)}_setSVGTextLineBg(t,e,i,r){let s=this._textLines[e],n=this.getHeightOfLine(e)/this.lineHeight,o,a=0,l=0,h=this.getValueOfPropertyAt(e,0,"textBackgroundColor");for(let c=0;c<s.length;c++){let{left:s,width:u,kernedWidth:d}=this.__charBounds[e][c];(o=this.getValueOfPropertyAt(e,c,"textBackgroundColor"))!==h?(h&&t.push(...no(h,i+l,r,a,n)),l=s,a=u,h=o):a+=d}o&&t.push(...no(h,i+l,r,a,n))}_getSVGLineTopOffset(t){let e,i=0;for(e=0;e<t;e++)i+=this.getHeightOfLine(e);let r=this.getHeightOfLine(e);return{lineTop:i,offset:(this._fontSizeMult-this._fontSizeFraction)*r/(this.lineHeight*this._fontSizeMult)}}getSvgStyles(t){return"".concat(super.getSvgStyles(t)," white-space: pre;")}getSvgSpanStyles(t,e){let{fontFamily:i,strokeWidth:r,stroke:s,fill:n,fontSize:o,fontStyle:a,fontWeight:l,deltaY:h}=t,c=this.getSvgTextDecoration(t);return[s?tZ(J,s):"",r?"stroke-width: ".concat(r,"; "):"",i?"font-family: ".concat(i.includes("'")||i.includes('"')?i:"'".concat(i,"'"),"; "):"",o?"font-size: ".concat(o,"px; "):"",a?"font-style: ".concat(a,"; "):"",l?"font-weight: ".concat(l,"; "):"",c?"text-decoration: ".concat(c,"; "):c,n?tZ(K,n):"",h?"baseline-shift: ".concat(-h,"; "):"",e?"white-space: pre; ":""].join("")}getSvgTextDecoration(t){return["overline","underline","line-through"].filter(e=>t[e.replace("-","")]).join(" ")}}]),$.setClass(nl),$.setSVGClass(nl);class nh{constructor(t){o(this,"target",void 0),o(this,"__mouseDownInPlace",!1),o(this,"__dragStartFired",!1),o(this,"__isDraggingOver",!1),o(this,"__dragStartSelection",void 0),o(this,"__dragImageDisposer",void 0),o(this,"_dispose",void 0),this.target=t;let e=[this.target.on("dragenter",this.dragEnterHandler.bind(this)),this.target.on("dragover",this.dragOverHandler.bind(this)),this.target.on("dragleave",this.dragLeaveHandler.bind(this)),this.target.on("dragend",this.dragEndHandler.bind(this)),this.target.on("drop",this.dropHandler.bind(this))];this._dispose=()=>{e.forEach(t=>t()),this._dispose=void 0}}isPointerOverSelection(t){let e=this.target,i=e.getSelectionStartFromPointer(t);return e.isEditing&&i>=e.selectionStart&&i<=e.selectionEnd&&e.selectionStart<e.selectionEnd}start(t){return this.__mouseDownInPlace=this.isPointerOverSelection(t)}isActive(){return this.__mouseDownInPlace}end(t){let e=this.isActive();return e&&!this.__dragStartFired&&(this.target.setCursorByClick(t),this.target.initDelayedCursor(!0)),this.__mouseDownInPlace=!1,this.__dragStartFired=!1,this.__isDraggingOver=!1,e}getDragStartSelection(){return this.__dragStartSelection}setDragImage(t,e){var i;let{selectionStart:r,selectionEnd:s}=e,n=this.target,o=n.canvas,a=new tn(n.flipX?-1:1,n.flipY?-1:1),l=n._getCursorBoundaries(r),h=new tn(l.left+l.leftOffset,l.top+l.topOffset).multiply(a).transform(n.calcTransformMatrix()),c=o.getScenePoint(t).subtract(h),u=n.getCanvasRetinaScaling(),d=n.getBoundingRect(),g=h.subtract(new tn(d.left,d.top)),f=o.viewportTransform,p=g.add(c).transform(f,!0),m=n.backgroundColor,v=ri(n.styles);n.backgroundColor="";let _={stroke:"transparent",fill:"transparent",textBackgroundColor:"transparent"};n.setSelectionStyles(_,0,r),n.setSelectionStyles(_,s,n.text.length),n.dirty=!0;let y=n.toCanvasElement({enableRetinaScaling:o.enableRetinaScaling,viewportTransform:!0});n.backgroundColor=m,n.styles=v,n.dirty=!0,sg(y,{position:"fixed",left:"".concat(-y.width,"px"),border:F,width:"".concat(y.width/u,"px"),height:"".concat(y.height/u,"px")}),this.__dragImageDisposer&&this.__dragImageDisposer(),this.__dragImageDisposer=()=>{y.remove()},t3(t.target||this.target.hiddenTextarea).body.appendChild(y),null===(i=t.dataTransfer)||void 0===i||i.setDragImage(y,p.x,p.y)}onDragStart(t){this.__dragStartFired=!0;let e=this.target,i=this.isActive();if(i&&t.dataTransfer){let i=this.__dragStartSelection={selectionStart:e.selectionStart,selectionEnd:e.selectionEnd},r=e._text.slice(i.selectionStart,i.selectionEnd).join(""),s=l({text:e.text,value:r},i);t.dataTransfer.setData("text/plain",r),t.dataTransfer.setData("application/fabric",JSON.stringify({value:r,styles:e.getSelectionStyles(i.selectionStart,i.selectionEnd,!0)})),t.dataTransfer.effectAllowed="copyMove",this.setDragImage(t,s)}return e.abortCursorAnimation(),i}canDrop(t){if(this.target.editable&&!this.target.getActiveControl()&&!t.defaultPrevented){if(this.isActive()&&this.__dragStartSelection){let e=this.target.getSelectionStartFromPointer(t),i=this.__dragStartSelection;return e<i.selectionStart||e>i.selectionEnd}return!0}return!1}targetCanDrop(t){return this.target.canDrop(t)}dragEnterHandler(t){let{e:e}=t,i=this.targetCanDrop(e);!this.__isDraggingOver&&i&&(this.__isDraggingOver=!0)}dragOverHandler(t){let{e:e}=t,i=this.targetCanDrop(e);!this.__isDraggingOver&&i?this.__isDraggingOver=!0:this.__isDraggingOver&&!i&&(this.__isDraggingOver=!1),this.__isDraggingOver&&(e.preventDefault(),t.canDrop=!0,t.dropTarget=this.target)}dragLeaveHandler(){(this.__isDraggingOver||this.isActive())&&(this.__isDraggingOver=!1)}dropHandler(t){var e;let{e:i}=t,r=i.defaultPrevented;this.__isDraggingOver=!1,i.preventDefault();let s=null===(e=i.dataTransfer)||void 0===e?void 0:e.getData("text/plain");if(s&&!r){let e=this.target,r=e.canvas,n=e.getSelectionStartFromPointer(i),{styles:o}=i.dataTransfer.types.includes("application/fabric")?JSON.parse(i.dataTransfer.getData("application/fabric")):{},a=s[Math.max(0,s.length-1)];if(this.__dragStartSelection){let t=this.__dragStartSelection.selectionStart,i=this.__dragStartSelection.selectionEnd;n>t&&n<=i?n=t:n>i&&(n-=i-t),e.removeChars(t,i),delete this.__dragStartSelection}e._reNewline.test(a)&&(e._reNewline.test(e._text[n])||n===e._text.length)&&(s=s.trimEnd()),t.didDrop=!0,t.dropTarget=e,e.insertChars(s,o,n),r.setActiveObject(e),e.enterEditing(i),e.selectionStart=Math.min(n+0,e._text.length),e.selectionEnd=Math.min(e.selectionStart+s.length,e._text.length),e.hiddenTextarea.value=e.text,e._updateTextarea(),e.hiddenTextarea.focus(),e.fire(H,{index:n+0,action:"drop"}),r.fire("text:changed",{target:e}),r.contextTopDirty=!0,r.requestRenderAll()}}dragEndHandler(t){let{e:e}=t;if(this.isActive()&&this.__dragStartFired&&this.__dragStartSelection){var i;let t=this.target,r=this.target.canvas,{selectionStart:s,selectionEnd:n}=this.__dragStartSelection,o=(null===(i=e.dataTransfer)||void 0===i?void 0:i.dropEffect)||F;o===F?(t.selectionStart=s,t.selectionEnd=n,t._updateTextarea(),t.hiddenTextarea.focus()):(t.clearContextTop(),"move"===o&&(t.removeChars(s,n),t.selectionStart=t.selectionEnd=s,t.hiddenTextarea&&(t.hiddenTextarea.value=t.text),t._updateTextarea(),t.fire(H,{index:s,action:"dragend"}),r.fire("text:changed",{target:t}),r.requestRenderAll()),t.exitEditing())}this.__dragImageDisposer&&this.__dragImageDisposer(),delete this.__dragImageDisposer,delete this.__dragStartSelection,this.__isDraggingOver=!1}dispose(){this._dispose&&this._dispose()}}let nc=/[ \n\.,;!\?\-]/;class nu extends nl{constructor(){super(...arguments),o(this,"_currentCursorOpacity",1)}initBehavior(){this._tick=this._tick.bind(this),this._onTickComplete=this._onTickComplete.bind(this),this.updateSelectionOnMouseMove=this.updateSelectionOnMouseMove.bind(this)}onDeselect(t){return this.isEditing&&this.exitEditing(),this.selected=!1,super.onDeselect(t)}_animateCursor(t){let{toValue:e,duration:i,delay:r,onComplete:s}=t;return iy({startValue:this._currentCursorOpacity,endValue:e,duration:i,delay:r,onComplete:s,abort:()=>!this.canvas||this.selectionStart!==this.selectionEnd,onChange:t=>{this._currentCursorOpacity=t,this.renderCursorOrSelection()}})}_tick(t){this._currentTickState=this._animateCursor({toValue:0,duration:this.cursorDuration/2,delay:Math.max(t||0,100),onComplete:this._onTickComplete})}_onTickComplete(){var t;null===(t=this._currentTickCompleteState)||void 0===t||t.abort(),this._currentTickCompleteState=this._animateCursor({toValue:1,duration:this.cursorDuration,onComplete:this._tick})}initDelayedCursor(t){this.abortCursorAnimation(),this._tick(t?0:this.cursorDelay)}abortCursorAnimation(){let t=!1;[this._currentTickState,this._currentTickCompleteState].forEach(e=>{e&&!e.isDone()&&(t=!0,e.abort())}),this._currentCursorOpacity=1,t&&this.clearContextTop()}restartCursorIfNeeded(){[this._currentTickState,this._currentTickCompleteState].some(t=>!t||t.isDone())&&this.initDelayedCursor()}selectAll(){return this.selectionStart=0,this.selectionEnd=this._text.length,this._fireSelectionChanged(),this._updateTextarea(),this}getSelectedText(){return this._text.slice(this.selectionStart,this.selectionEnd).join("")}findWordBoundaryLeft(t){let e=0,i=t-1;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i--;for(;/\S/.test(this._text[i])&&i>-1;)e++,i--;return t-e}findWordBoundaryRight(t){let e=0,i=t;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i++;for(;/\S/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e}findLineBoundaryLeft(t){let e=0,i=t-1;for(;!/\n/.test(this._text[i])&&i>-1;)e++,i--;return t-e}findLineBoundaryRight(t){let e=0,i=t;for(;!/\n/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e}searchWordBoundary(t,e){let i=this._text,r=t>0&&this._reSpace.test(i[t])&&(-1===e||!L.test(i[t-1]))?t-1:t,s=i[r];for(;r>0&&r<i.length&&!nc.test(s);)r+=e,s=i[r];return -1===e&&nc.test(s)&&r++,r}selectWord(t){t=t||this.selectionStart;let e=this.searchWordBoundary(t,-1),i=Math.max(e,this.searchWordBoundary(t,1));this.selectionStart=e,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()}selectLine(t){t=t||this.selectionStart;let e=this.findLineBoundaryLeft(t),i=this.findLineBoundaryRight(t);return this.selectionStart=e,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea(),this}enterEditing(t){!this.isEditing&&this.editable&&(this.enterEditingImpl(),this.fire("editing:entered",t?{e:t}:void 0),this._fireSelectionChanged(),this.canvas&&(this.canvas.fire("text:editing:entered",{target:this,e:t}),this.canvas.requestRenderAll()))}enterEditingImpl(){this.canvas&&(this.canvas.calcOffset(),this.canvas.textEditingManager.exitTextEditing()),this.isEditing=!0,this.initHiddenTextarea(),this.hiddenTextarea.focus(),this.hiddenTextarea.value=this.text,this._updateTextarea(),this._saveEditingProps(),this._setEditingProps(),this._textBeforeEdit=this.text,this._tick()}updateSelectionOnMouseMove(t){if(this.getActiveControl())return;let e=this.hiddenTextarea;t3(e).activeElement!==e&&e.focus();let i=this.getSelectionStartFromPointer(t),r=this.selectionStart,s=this.selectionEnd;(i===this.__selectionStartOnMouseDown&&r!==s||r!==i&&s!==i)&&(i>this.__selectionStartOnMouseDown?(this.selectionStart=this.__selectionStartOnMouseDown,this.selectionEnd=i):(this.selectionStart=i,this.selectionEnd=this.__selectionStartOnMouseDown),this.selectionStart===r&&this.selectionEnd===s||(this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()))}_setEditingProps(){this.hoverCursor="text",this.canvas&&(this.canvas.defaultCursor=this.canvas.moveCursor="text"),this.borderColor=this.editingBorderColor,this.hasControls=this.selectable=!1,this.lockMovementX=this.lockMovementY=!0}fromStringToGraphemeSelection(t,e,i){let r=i.slice(0,t),s=this.graphemeSplit(r).length;if(t===e)return{selectionStart:s,selectionEnd:s};let n=i.slice(t,e);return{selectionStart:s,selectionEnd:s+this.graphemeSplit(n).length}}fromGraphemeToStringSelection(t,e,i){let r=i.slice(0,t).join("").length;return t===e?{selectionStart:r,selectionEnd:r}:{selectionStart:r,selectionEnd:r+i.slice(t,e).join("").length}}_updateTextarea(){if(this.cursorOffsetCache={},this.hiddenTextarea){if(!this.inCompositionMode){let t=this.fromGraphemeToStringSelection(this.selectionStart,this.selectionEnd,this._text);this.hiddenTextarea.selectionStart=t.selectionStart,this.hiddenTextarea.selectionEnd=t.selectionEnd}this.updateTextareaPosition()}}updateFromTextArea(){if(!this.hiddenTextarea)return;this.cursorOffsetCache={};let t=this.hiddenTextarea;this.text=t.value,this.set("dirty",!0),this.initDimensions(),this.setCoords();let e=this.fromStringToGraphemeSelection(t.selectionStart,t.selectionEnd,t.value);this.selectionEnd=this.selectionStart=e.selectionEnd,this.inCompositionMode||(this.selectionStart=e.selectionStart),this.updateTextareaPosition()}updateTextareaPosition(){if(this.selectionStart===this.selectionEnd){let t=this._calcTextareaPosition();this.hiddenTextarea.style.left=t.left,this.hiddenTextarea.style.top=t.top}}_calcTextareaPosition(){if(!this.canvas)return{left:"1px",top:"1px"};let t=this.inCompositionMode?this.compositionStart:this.selectionStart,e=this._getCursorBoundaries(t),i=this.get2DCursorLocation(t),r=i.lineIndex,s=i.charIndex,n=this.getValueOfPropertyAt(r,s,"fontSize")*this.lineHeight,o=e.leftOffset,a=this.getCanvasRetinaScaling(),l=this.canvas.upperCanvasEl,h=l.width/a,c=l.height/a,u=h-n,d=c-n,g=new tn(e.left+o,e.top+e.topOffset+n).transform(this.calcTransformMatrix()).transform(this.canvas.viewportTransform).multiply(new tn(l.clientWidth/h,l.clientHeight/c));return g.x<0&&(g.x=0),g.x>u&&(g.x=u),g.y<0&&(g.y=0),g.y>d&&(g.y=d),g.x+=this.canvas._offset.left,g.y+=this.canvas._offset.top,{left:"".concat(g.x,"px"),top:"".concat(g.y,"px"),fontSize:"".concat(n,"px"),charHeight:n}}_saveEditingProps(){this._savedProps={hasControls:this.hasControls,borderColor:this.borderColor,lockMovementX:this.lockMovementX,lockMovementY:this.lockMovementY,hoverCursor:this.hoverCursor,selectable:this.selectable,defaultCursor:this.canvas&&this.canvas.defaultCursor,moveCursor:this.canvas&&this.canvas.moveCursor}}_restoreEditingProps(){this._savedProps&&(this.hoverCursor=this._savedProps.hoverCursor,this.hasControls=this._savedProps.hasControls,this.borderColor=this._savedProps.borderColor,this.selectable=this._savedProps.selectable,this.lockMovementX=this._savedProps.lockMovementX,this.lockMovementY=this._savedProps.lockMovementY,this.canvas&&(this.canvas.defaultCursor=this._savedProps.defaultCursor||this.canvas.defaultCursor,this.canvas.moveCursor=this._savedProps.moveCursor||this.canvas.moveCursor),delete this._savedProps)}_exitEditing(){let t=this.hiddenTextarea;this.selected=!1,this.isEditing=!1,t&&(t.blur&&t.blur(),t.parentNode&&t.parentNode.removeChild(t)),this.hiddenTextarea=null,this.abortCursorAnimation(),this.selectionStart!==this.selectionEnd&&this.clearContextTop()}exitEditingImpl(){this._exitEditing(),this.selectionEnd=this.selectionStart,this._restoreEditingProps(),this._forceClearCache&&(this.initDimensions(),this.setCoords())}exitEditing(){let t=this._textBeforeEdit!==this.text;return this.exitEditingImpl(),this.fire("editing:exited"),t&&this.fire(Q),this.canvas&&(this.canvas.fire("text:editing:exited",{target:this}),t&&this.canvas.fire("object:modified",{target:this})),this}_removeExtraneousStyles(){for(let t in this.styles)this._textLines[t]||delete this.styles[t]}removeStyleFromTo(t,e){let{lineIndex:i,charIndex:r}=this.get2DCursorLocation(t,!0),{lineIndex:s,charIndex:n}=this.get2DCursorLocation(e,!0);if(i!==s){if(this.styles[i])for(let t=r;t<this._unwrappedTextLines[i].length;t++)delete this.styles[i][t];if(this.styles[s])for(let t=n;t<this._unwrappedTextLines[s].length;t++){let e=this.styles[s][t];e&&(this.styles[i]||(this.styles[i]={}),this.styles[i][r+t-n]=e)}for(let t=i+1;t<=s;t++)delete this.styles[t];this.shiftLineStyles(s,i-s)}else if(this.styles[i]){let t=this.styles[i],e=n-r;for(let e=r;e<n;e++)delete t[e];for(let r in this.styles[i]){let i=parseInt(r,10);i>=n&&(t[i-e]=t[r],delete t[r])}}}shiftLineStyles(t,e){let i=Object.assign({},this.styles);for(let r in this.styles){let s=parseInt(r,10);s>t&&(this.styles[s+e]=i[s],i[s-e]||delete this.styles[s])}}insertNewlineStyleObject(t,e,i,r){let s={},n=this._unwrappedTextLines[t].length,o=n===e,a=!1;i||(i=1),this.shiftLineStyles(t,i);let h=this.styles[t]?this.styles[t][0===e?e:e-1]:void 0;for(let i in this.styles[t]){let r=parseInt(i,10);r>=e&&(a=!0,s[r-e]=this.styles[t][i],o&&0===e||delete this.styles[t][i])}let c=!1;for(a&&!o&&(this.styles[t+i]=s,c=!0),(c||n>e)&&i--;i>0;)r&&r[i-1]?this.styles[t+i]={0:l({},r[i-1])}:h?this.styles[t+i]={0:l({},h)}:delete this.styles[t+i],i--;this._forceClearCache=!0}insertCharStyleObject(t,e,i,r){this.styles||(this.styles={});let s=this.styles[t],n=s?l({},s):{};for(let t in i||(i=1),n){let r=parseInt(t,10);r>=e&&(s[r+i]=n[r],n[r-i]||delete s[r])}if(this._forceClearCache=!0,r){for(;i--;)Object.keys(r[i]).length&&(this.styles[t]||(this.styles[t]={}),this.styles[t][e+i]=l({},r[i]));return}if(!s)return;let o=s[e?e-1:1];for(;o&&i--;)this.styles[t][e+i]=l({},o)}insertNewStyleBlock(t,e,i){let r=this.get2DCursorLocation(e,!0),s=[0],n,o=0;for(let e=0;e<t.length;e++)"\n"===t[e]?s[++o]=0:s[o]++;for(s[0]>0&&(this.insertCharStyleObject(r.lineIndex,r.charIndex,s[0],i),i=i&&i.slice(s[0]+1)),o&&this.insertNewlineStyleObject(r.lineIndex,r.charIndex+s[0],o),n=1;n<o;n++)s[n]>0?this.insertCharStyleObject(r.lineIndex+n,0,s[n],i):i&&this.styles[r.lineIndex+n]&&i[0]&&(this.styles[r.lineIndex+n][0]=i[0]),i=i&&i.slice(s[n]+1);s[n]>0&&this.insertCharStyleObject(r.lineIndex+n,0,s[n],i)}removeChars(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t+1;this.removeStyleFromTo(t,e),this._text.splice(t,e-t),this.text=this._text.join(""),this.set("dirty",!0),this.initDimensions(),this.setCoords(),this._removeExtraneousStyles()}insertChars(t,e,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:i;r>i&&this.removeStyleFromTo(i,r);let s=this.graphemeSplit(t);this.insertNewStyleBlock(s,i,e),this._text=[...this._text.slice(0,i),...s,...this._text.slice(r)],this.text=this._text.join(""),this.set("dirty",!0),this.initDimensions(),this.setCoords(),this._removeExtraneousStyles()}setSelectionStartEndWithShift(t,e,i){i<=t?(e===t?this._selectionDirection=P:this._selectionDirection===j&&(this._selectionDirection=P,this.selectionEnd=t),this.selectionStart=i):i>t&&i<e?this._selectionDirection===j?this.selectionEnd=i:this.selectionStart=i:(e===t?this._selectionDirection=j:this._selectionDirection===P&&(this._selectionDirection=j,this.selectionStart=e),this.selectionEnd=i)}}class nd extends nu{initHiddenTextarea(){let t=this.canvas&&t3(this.canvas.getElement())||x(),e=t.createElement("textarea");Object.entries({autocapitalize:"off",autocorrect:"off",autocomplete:"off",spellcheck:"false","data-fabric":"textarea",wrap:"off"}).map(t=>{let[i,r]=t;return e.setAttribute(i,r)});let{top:i,left:r,fontSize:s}=this._calcTextareaPosition();e.style.cssText="position: absolute; top: ".concat(i,"; left: ").concat(r,"; z-index: -999; opacity: 0; width: 1px; height: 1px; font-size: 1px; padding-top: ").concat(s,";"),(this.hiddenTextareaContainer||t.body).appendChild(e),Object.entries({blur:"blur",keydown:"onKeyDown",keyup:"onKeyUp",input:"onInput",copy:"copy",cut:"copy",paste:"paste",compositionstart:"onCompositionStart",compositionupdate:"onCompositionUpdate",compositionend:"onCompositionEnd"}).map(t=>{let[i,r]=t;return e.addEventListener(i,this[r].bind(this))}),this.hiddenTextarea=e}blur(){this.abortCursorAnimation()}onKeyDown(t){if(!this.isEditing)return;let e="rtl"===this.direction?this.keysMapRtl:this.keysMap;if(t.keyCode in e)this[e[t.keyCode]](t);else{if(!(t.keyCode in this.ctrlKeysMapDown)||!t.ctrlKey&&!t.metaKey)return;this[this.ctrlKeysMapDown[t.keyCode]](t)}t.stopImmediatePropagation(),t.preventDefault(),t.keyCode>=33&&t.keyCode<=40?(this.inCompositionMode=!1,this.clearContextTop(),this.renderCursorOrSelection()):this.canvas&&this.canvas.requestRenderAll()}onKeyUp(t){!this.isEditing||this._copyDone||this.inCompositionMode?this._copyDone=!1:t.keyCode in this.ctrlKeysMapUp&&(t.ctrlKey||t.metaKey)&&(this[this.ctrlKeysMapUp[t.keyCode]](t),t.stopImmediatePropagation(),t.preventDefault(),this.canvas&&this.canvas.requestRenderAll())}onInput(t){let e=this.fromPaste;if(this.fromPaste=!1,t&&t.stopPropagation(),!this.isEditing)return;let i=()=>{this.updateFromTextArea(),this.fire(H),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll())};if(""===this.hiddenTextarea.value)return this.styles={},void i();let r=this._splitTextIntoLines(this.hiddenTextarea.value).graphemeText,s=this._text.length,n=r.length,o=this.selectionStart,a=this.selectionEnd,l=o!==a,h,c,u,g,f=n-s,p=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value),m=o>p.selectionStart;l?(c=this._text.slice(o,a),f+=a-o):n<s&&(c=m?this._text.slice(a+f,a):this._text.slice(o,o-f));let v=r.slice(p.selectionEnd-f,p.selectionEnd);if(c&&c.length&&(v.length&&(h=this.getSelectionStyles(o,o+1,!1),h=v.map(()=>h[0])),l?(u=o,g=a):m?(u=a-c.length,g=a):(u=a,g=a+c.length),this.removeStyleFromTo(u,g)),v.length){let{copyPasteData:t}=y();e&&v.join("")===t.copiedText&&!d.disableStyleCopyPaste&&(h=t.copiedTextStyle),this.insertNewStyleBlock(v,o,h)}i()}onCompositionStart(){this.inCompositionMode=!0}onCompositionEnd(){this.inCompositionMode=!1}onCompositionUpdate(t){let{target:e}=t,{selectionStart:i,selectionEnd:r}=e;this.compositionStart=i,this.compositionEnd=r,this.updateTextareaPosition()}copy(){if(this.selectionStart===this.selectionEnd)return;let{copyPasteData:t}=y();t.copiedText=this.getSelectedText(),d.disableStyleCopyPaste?t.copiedTextStyle=void 0:t.copiedTextStyle=this.getSelectionStyles(this.selectionStart,this.selectionEnd,!0),this._copyDone=!0}paste(){this.fromPaste=!0}_getWidthBeforeCursor(t,e){let i,r=this._getLineLeftOffset(t);return e>0&&(r+=(i=this.__charBounds[t][e-1]).left+i.width),r}getDownCursorOffset(t,e){let i=this._getSelectionForOffset(t,e),r=this.get2DCursorLocation(i),s=r.lineIndex;if(s===this._textLines.length-1||t.metaKey||34===t.keyCode)return this._text.length-i;let n=r.charIndex,o=this._getWidthBeforeCursor(s,n),a=this._getIndexOnLine(s+1,o);return this._textLines[s].slice(n).length+a+1+this.missingNewlineOffset(s)}_getSelectionForOffset(t,e){return t.shiftKey&&this.selectionStart!==this.selectionEnd&&e?this.selectionEnd:this.selectionStart}getUpCursorOffset(t,e){let i=this._getSelectionForOffset(t,e),r=this.get2DCursorLocation(i),s=r.lineIndex;if(0===s||t.metaKey||33===t.keyCode)return-i;let n=r.charIndex,o=this._getWidthBeforeCursor(s,n),a=this._getIndexOnLine(s-1,o),l=this._textLines[s].slice(0,n),h=this.missingNewlineOffset(s-1);return-this._textLines[s-1].length+a-l.length+(1-h)}_getIndexOnLine(t,e){let i=this._textLines[t],r,s,n=this._getLineLeftOffset(t),o=0;for(let a=0,l=i.length;a<l;a++)if((n+=r=this.__charBounds[t][a].width)>e){s=!0;let t=Math.abs(n-r-e);o=Math.abs(n-e)<t?a:a-1;break}return s||(o=i.length-1),o}moveCursorDown(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorUpOrDown("Down",t)}moveCursorUp(t){0===this.selectionStart&&0===this.selectionEnd||this._moveCursorUpOrDown("Up",t)}_moveCursorUpOrDown(t,e){let i=this["get".concat(t,"CursorOffset")](e,this._selectionDirection===j);if(e.shiftKey?this.moveCursorWithShift(i):this.moveCursorWithoutShift(i),0!==i){let t=this.text.length;this.selectionStart=e0(0,this.selectionStart,t),this.selectionEnd=e0(0,this.selectionEnd,t),this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea()}}moveCursorWithShift(t){let e=this._selectionDirection===P?this.selectionStart+t:this.selectionEnd+t;return this.setSelectionStartEndWithShift(this.selectionStart,this.selectionEnd,e),0!==t}moveCursorWithoutShift(t){return t<0?(this.selectionStart+=t,this.selectionEnd=this.selectionStart):(this.selectionEnd+=t,this.selectionStart=this.selectionEnd),0!==t}moveCursorLeft(t){0===this.selectionStart&&0===this.selectionEnd||this._moveCursorLeftOrRight("Left",t)}_move(t,e,i){let r;if(t.altKey)r=this["findWordBoundary".concat(i)](this[e]);else{if(!t.metaKey&&35!==t.keyCode&&36!==t.keyCode)return this[e]+="Left"===i?-1:1,!0;r=this["findLineBoundary".concat(i)](this[e])}return void 0!==r&&this[e]!==r&&(this[e]=r,!0)}_moveLeft(t,e){return this._move(t,e,"Left")}_moveRight(t,e){return this._move(t,e,"Right")}moveCursorLeftWithoutShift(t){let e=!0;return this._selectionDirection=P,this.selectionEnd===this.selectionStart&&0!==this.selectionStart&&(e=this._moveLeft(t,"selectionStart")),this.selectionEnd=this.selectionStart,e}moveCursorLeftWithShift(t){return this._selectionDirection===j&&this.selectionStart!==this.selectionEnd?this._moveLeft(t,"selectionEnd"):0!==this.selectionStart?(this._selectionDirection=P,this._moveLeft(t,"selectionStart")):void 0}moveCursorRight(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorLeftOrRight("Right",t)}_moveCursorLeftOrRight(t,e){let i="moveCursor".concat(t).concat(e.shiftKey?"WithShift":"WithoutShift");this._currentCursorOpacity=1,this[i](e)&&(this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())}moveCursorRightWithShift(t){return this._selectionDirection===P&&this.selectionStart!==this.selectionEnd?this._moveRight(t,"selectionStart"):this.selectionEnd!==this._text.length?(this._selectionDirection=j,this._moveRight(t,"selectionEnd")):void 0}moveCursorRightWithoutShift(t){let e=!0;return this._selectionDirection=j,this.selectionStart===this.selectionEnd?(e=this._moveRight(t,"selectionStart"),this.selectionEnd=this.selectionStart):this.selectionStart=this.selectionEnd,e}}let ng=t=>!!t.button;class nf extends nd{constructor(){super(...arguments),o(this,"draggableTextDelegate",void 0)}initBehavior(){this.on("mousedown",this._mouseDownHandler),this.on("mousedown:before",this._mouseDownHandlerBefore),this.on("mouseup",this.mouseUpHandler),this.on("mousedblclick",this.doubleClickHandler),this.on("tripleclick",this.tripleClickHandler),this.__lastClickTime=+new Date,this.__lastLastClickTime=+new Date,this.__lastPointer={},this.on("mousedown",this.onMouseDown),this.draggableTextDelegate=new nh(this),super.initBehavior()}shouldStartDragging(){return this.draggableTextDelegate.isActive()}onDragStart(t){return this.draggableTextDelegate.onDragStart(t)}canDrop(t){return this.draggableTextDelegate.canDrop(t)}onMouseDown(t){if(!this.canvas)return;this.__newClickTime=+new Date;let e=t.pointer;this.isTripleClick(e)&&(this.fire("tripleclick",t),en(t.e)),this.__lastLastClickTime=this.__lastClickTime,this.__lastClickTime=this.__newClickTime,this.__lastPointer=e,this.__lastSelected=this.selected&&!this.getActiveControl()}isTripleClick(t){return this.__newClickTime-this.__lastClickTime<500&&this.__lastClickTime-this.__lastLastClickTime<500&&this.__lastPointer.x===t.x&&this.__lastPointer.y===t.y}doubleClickHandler(t){this.isEditing&&this.selectWord(this.getSelectionStartFromPointer(t.e))}tripleClickHandler(t){this.isEditing&&this.selectLine(this.getSelectionStartFromPointer(t.e))}_mouseDownHandler(t){let{e:e}=t;this.canvas&&this.editable&&!ng(e)&&!this.getActiveControl()&&(this.draggableTextDelegate.start(e)||(this.canvas.textEditingManager.register(this),this.selected&&(this.inCompositionMode=!1,this.setCursorByClick(e)),this.isEditing&&(this.__selectionStartOnMouseDown=this.selectionStart,this.selectionStart===this.selectionEnd&&this.abortCursorAnimation(),this.renderCursorOrSelection())))}_mouseDownHandlerBefore(t){let{e:e}=t;this.canvas&&this.editable&&!ng(e)&&(this.selected=this===this.canvas._activeObject)}mouseUpHandler(t){let{e:e,transform:i}=t,r=this.draggableTextDelegate.end(e);if(this.canvas){this.canvas.textEditingManager.unregister(this);let t=this.canvas._activeObject;if(t&&t!==this)return}!this.editable||this.group&&!this.group.interactive||i&&i.actionPerformed||ng(e)||r||(this.__lastSelected&&!this.getActiveControl()?(this.selected=!1,this.__lastSelected=!1,this.enterEditing(e),this.selectionStart===this.selectionEnd?this.initDelayedCursor(!0):this.renderCursorOrSelection()):this.selected=!0)}setCursorByClick(t){let e=this.getSelectionStartFromPointer(t),i=this.selectionStart,r=this.selectionEnd;t.shiftKey?this.setSelectionStartEndWithShift(i,r,e):(this.selectionStart=e,this.selectionEnd=e),this.isEditing&&(this._fireSelectionChanged(),this._updateTextarea())}getSelectionStartFromPointer(t){let e=this.canvas.getScenePoint(t).transform(tS(this.calcTransformMatrix())).add(new tn(-this._getLeftOffset(),-this._getTopOffset())),i=0,r=0,s=0;for(let t=0;t<this._textLines.length&&i<=e.y;t++)i+=this.getHeightOfLine(t),s=t,t>0&&(r+=this._textLines[t-1].length+this.missingNewlineOffset(t-1));let n=Math.abs(this._getLineLeftOffset(s)),o=this._textLines[s].length,a=this.__charBounds[s];for(let t=0;t<o;t++){let i=n+a[t].kernedWidth;if(e.x<=i){Math.abs(e.x-i)<=Math.abs(e.x-n)&&r++;break}n=i,r++}return Math.min(this.flipX?o-r:r,this._text.length)}}let np="moveCursorUp",nm="moveCursorDown",nv="moveCursorLeft",n_="moveCursorRight",ny="exitEditing",nx=l({selectionStart:0,selectionEnd:0,selectionColor:"rgba(17,119,255,0.3)",isEditing:!1,editable:!0,editingBorderColor:"rgba(102,153,255,0.25)",cursorWidth:2,cursorColor:"",cursorDelay:1e3,cursorDuration:600,caching:!0,hiddenTextareaContainer:null,keysMap:{9:ny,27:ny,33:np,34:nm,35:n_,36:nv,37:nv,38:np,39:n_,40:nm},keysMapRtl:{9:ny,27:ny,33:np,34:nm,35:nv,36:n_,37:n_,38:np,39:nv,40:nm},ctrlKeysMapDown:{65:"selectAll"},ctrlKeysMapUp:{67:"copy",88:"cut"}},{_selectionDirection:null,_reSpace:/\s|\r?\n/,inCompositionMode:!1});class nC extends nf{static getDefaults(){return l(l({},super.getDefaults()),nC.ownDefaults)}get type(){let t=super.type;return"itext"===t?"i-text":t}constructor(t,e){super(t,l(l({},nC.ownDefaults),e)),this.initBehavior()}_set(t,e){return this.isEditing&&this._savedProps&&t in this._savedProps?(this._savedProps[t]=e,this):("canvas"===t&&(this.canvas instanceof sw&&this.canvas.textEditingManager.remove(this),e instanceof sw&&e.textEditingManager.add(this)),super._set(t,e))}setSelectionStart(t){t=Math.max(t,0),this._updateAndFire("selectionStart",t)}setSelectionEnd(t){t=Math.min(t,this.text.length),this._updateAndFire("selectionEnd",t)}_updateAndFire(t,e){this[t]!==e&&(this._fireSelectionChanged(),this[t]=e),this._updateTextarea()}_fireSelectionChanged(){this.fire("selection:changed"),this.canvas&&this.canvas.fire("text:selection:changed",{target:this})}initDimensions(){this.isEditing&&this.initDelayedCursor(),super.initDimensions()}getSelectionStyles(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.selectionStart||0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selectionEnd,i=arguments.length>2?arguments[2]:void 0;return super.getSelectionStyles(t,e,i)}setSelectionStyles(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.selectionStart||0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.selectionEnd;return super.setSelectionStyles(t,e,i)}get2DCursorLocation(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.selectionStart,e=arguments.length>1?arguments[1]:void 0;return super.get2DCursorLocation(t,e)}render(t){super.render(t),this.cursorOffsetCache={},this.renderCursorOrSelection()}toCanvasElement(t){let e=this.isEditing;this.isEditing=!1;let i=super.toCanvasElement(t);return this.isEditing=e,i}renderCursorOrSelection(){if(!this.isEditing)return;let t=this.clearContextTop(!0);if(!t)return;let e=this._getCursorBoundaries();this.selectionStart!==this.selectionEnd||this.inCompositionMode?this.renderSelection(t,e):this.renderCursor(t,e),this.canvas.contextTopDirty=!0,t.restore()}_getCursorBoundaries(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.selectionStart,e=arguments.length>1?arguments[1]:void 0,i=this._getLeftOffset(),r=this._getTopOffset(),s=this._getCursorBoundariesOffsets(t,e);return{left:i,top:r,leftOffset:s.left,topOffset:s.top}}_getCursorBoundariesOffsets(t,e){return e?this.__getCursorBoundariesOffsets(t):this.cursorOffsetCache&&"top"in this.cursorOffsetCache?this.cursorOffsetCache:this.cursorOffsetCache=this.__getCursorBoundariesOffsets(t)}__getCursorBoundariesOffsets(t){let e=0,i=0,{charIndex:r,lineIndex:s}=this.get2DCursorLocation(t);for(let t=0;t<s;t++)e+=this.getHeightOfLine(t);let n=this._getLineLeftOffset(s),o=this.__charBounds[s][r];o&&(i=o.left),0!==this.charSpacing&&r===this._textLines[s].length&&(i-=this._getWidthOfCharSpacing());let a={top:e,left:n+(i>0?i:0)};return"rtl"===this.direction&&(this.textAlign===j||this.textAlign===s7||this.textAlign===ne?a.left*=-1:this.textAlign===P||this.textAlign===nt?a.left=n-(i>0?i:0):this.textAlign!==E&&this.textAlign!==ni||(a.left=n-(i>0?i:0))),a}renderCursorAt(t){this._renderCursor(this.canvas.contextTop,this._getCursorBoundaries(t,!0),t)}renderCursor(t,e){this._renderCursor(t,e,this.selectionStart)}getCursorRenderingData(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.selectionStart,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._getCursorBoundaries(t),i=this.get2DCursorLocation(t),r=i.lineIndex,s=i.charIndex>0?i.charIndex-1:0,n=this.getValueOfPropertyAt(r,s,"fontSize"),o=this.getObjectScaling().x*this.canvas.getZoom(),a=this.cursorWidth/o,l=this.getValueOfPropertyAt(r,s,"deltaY"),h=e.topOffset+(1-this._fontSizeFraction)*this.getHeightOfLine(r)/this.lineHeight-n*(1-this._fontSizeFraction);return{color:this.cursorColor||this.getValueOfPropertyAt(r,s,"fill"),opacity:this._currentCursorOpacity,left:e.left+e.leftOffset-a/2,top:h+e.top+l,width:a,height:n}}_renderCursor(t,e,i){let{color:r,opacity:s,left:n,top:o,width:a,height:l}=this.getCursorRenderingData(i,e);t.fillStyle=r,t.globalAlpha=s,t.fillRect(n,o,a,l)}renderSelection(t,e){let i={selectionStart:this.inCompositionMode?this.hiddenTextarea.selectionStart:this.selectionStart,selectionEnd:this.inCompositionMode?this.hiddenTextarea.selectionEnd:this.selectionEnd};this._renderSelection(t,i,e)}renderDragSourceEffect(){let t=this.draggableTextDelegate.getDragStartSelection();this._renderSelection(this.canvas.contextTop,t,this._getCursorBoundaries(t.selectionStart,!0))}renderDropTargetEffect(t){let e=this.getSelectionStartFromPointer(t);this.renderCursorAt(e)}_renderSelection(t,e,i){let r=e.selectionStart,s=e.selectionEnd,n=this.textAlign.includes(s7),o=this.get2DCursorLocation(r),a=this.get2DCursorLocation(s),l=o.lineIndex,h=a.lineIndex,c=o.charIndex<0?0:o.charIndex,u=a.charIndex<0?0:a.charIndex;for(let e=l;e<=h;e++){let r=this._getLineLeftOffset(e)||0,s=this.getHeightOfLine(e),o=0,a=0,d=0;if(e===l&&(a=this.__charBounds[l][c].left),e>=l&&e<h)d=n&&!this.isEndOfWrapping(e)?this.width:this.getLineWidth(e)||5;else if(e===h){if(0===u)d=this.__charBounds[h][u].left;else{let t=this._getWidthOfCharSpacing();d=this.__charBounds[h][u-1].left+this.__charBounds[h][u-1].width-t}}o=s,(this.lineHeight<1||e===h&&this.lineHeight>1)&&(s/=this.lineHeight);let g=i.left+r+a,f=s,p=0,m=d-a;this.inCompositionMode?(t.fillStyle=this.compositionColor||"black",f=1,p=s):t.fillStyle=this.selectionColor,"rtl"===this.direction&&(this.textAlign===j||this.textAlign===s7||this.textAlign===ne?g=this.width-g-m:this.textAlign===P||this.textAlign===nt?g=i.left+r-d:this.textAlign!==E&&this.textAlign!==ni||(g=i.left+r-d)),t.fillRect(g,i.top+i.topOffset+p,m,f),i.topOffset+=o}}getCurrentCharFontSize(){let t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fontSize")}getCurrentCharColor(){let t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,K)}_getCurrentCharIndex(){let t=this.get2DCursorLocation(this.selectionStart,!0),e=t.charIndex>0?t.charIndex-1:0;return{l:t.lineIndex,c:e}}dispose(){this.exitEditingImpl(),this.draggableTextDelegate.dispose(),super.dispose()}}o(nC,"ownDefaults",nx),o(nC,"type","IText"),$.setClass(nC),$.setClass(nC,"i-text");class nb extends nC{static getDefaults(){return l(l({},super.getDefaults()),nb.ownDefaults)}constructor(t,e){super(t,l(l({},nb.ownDefaults),e))}static createControls(){return{controls:i2()}}initDimensions(){this.initialized&&(this.isEditing&&this.initDelayedCursor(),this._clearCache(),this.dynamicMinWidth=0,this._styleMap=this._generateStyleMap(this._splitText()),this.dynamicMinWidth>this.width&&this._set("width",this.dynamicMinWidth),this.textAlign.includes(s7)&&this.enlargeSpaces(),this.height=this.calcTextHeight())}_generateStyleMap(t){let e=0,i=0,r=0,s={};for(let n=0;n<t.graphemeLines.length;n++)"\n"===t.graphemeText[r]&&n>0?(i=0,r++,e++):!this.splitByGrapheme&&this._reSpaceAndTab.test(t.graphemeText[r])&&n>0&&(i++,r++),s[n]={line:e,offset:i},r+=t.graphemeLines[n].length,i+=t.graphemeLines[n].length;return s}styleHas(t,e){if(this._styleMap&&!this.isWrapping){let t=this._styleMap[e];t&&(e=t.line)}return super.styleHas(t,e)}isEmptyStyles(t){if(!this.styles)return!0;let e,i=0,r=t+1,s=!1,n=this._styleMap[t],o=this._styleMap[t+1];n&&(t=n.line,i=n.offset),o&&(s=o.line===t,e=o.offset);let a=void 0===t?this.styles:{line:this.styles[t]};for(let t in a)for(let r in a[t]){let n=parseInt(r,10);if(n>=i&&(!s||n<e))for(let e in a[t][r])return!1}return!0}_getStyleDeclaration(t,e){if(this._styleMap&&!this.isWrapping){let i=this._styleMap[t];if(!i)return{};t=i.line,e=i.offset+e}return super._getStyleDeclaration(t,e)}_setStyleDeclaration(t,e,i){let r=this._styleMap[t];super._setStyleDeclaration(r.line,r.offset+e,i)}_deleteStyleDeclaration(t,e){let i=this._styleMap[t];super._deleteStyleDeclaration(i.line,i.offset+e)}_getLineStyle(t){let e=this._styleMap[t];return!!this.styles[e.line]}_setLineStyle(t){let e=this._styleMap[t];super._setLineStyle(e.line)}_wrapText(t,e){this.isWrapping=!0;let i=this.getGraphemeDataForRender(t),r=[];for(let t=0;t<i.wordsData.length;t++)r.push(...this._wrapLine(t,e,i));return this.isWrapping=!1,r}getGraphemeDataForRender(t){let e=this.splitByGrapheme,i=e?"":" ",r=0;return{wordsData:t.map((t,s)=>{let n=0,o=e?this.graphemeSplit(t):this.wordSplit(t);return 0===o.length?[{word:[],width:0}]:o.map(t=>{let o=e?[t]:this.graphemeSplit(t),a=this._measureWord(o,s,n);return r=Math.max(a,r),n+=o.length+i.length,{word:o,width:a}})}),largestWordWidth:r}}_measureWord(t,e){let i,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=0;for(let n=0,o=t.length;n<o;n++)s+=this._getGraphemeBox(t[n],e,n+r,i,!0).kernedWidth,i=t[n];return s}wordSplit(t){return t.split(this._wordJoiners)}_wrapLine(t,e,i){let r,{largestWordWidth:s,wordsData:n}=i,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=this._getWidthOfCharSpacing(),l=this.splitByGrapheme,h=[],c=l?"":" ",u=0,d=[],g=0,f=0,p=!0,m=Math.max(e-=o,s,this.dynamicMinWidth),v=n[t];for(g=0,r=0;r<v.length;r++){let{word:e,width:i}=v[r];g+=e.length,(u+=f+i-a)>m&&!p?(h.push(d),d=[],u=i,p=!0):u+=a,p||l||d.push(c),d=d.concat(e),f=l?0:this._measureWord([c],t,g),g++,p=!1}return r&&h.push(d),s+o>this.dynamicMinWidth&&(this.dynamicMinWidth=s-a+o),h}isEndOfWrapping(t){return!this._styleMap[t+1]||this._styleMap[t+1].line!==this._styleMap[t].line}missingNewlineOffset(t,e){return this.splitByGrapheme&&!e?+!!this.isEndOfWrapping(t):1}_splitTextIntoLines(t){let e=super._splitTextIntoLines(t),i=this._wrapText(e.lines,this.width),r=Array(i.length);for(let t=0;t<i.length;t++)r[t]=i[t].join("");return e.lines=r,e.graphemeLines=i,e}getMinWidth(){return Math.max(this.minWidth,this.dynamicMinWidth)}_removeExtraneousStyles(){let t=new Map;for(let e in this._styleMap){let i=parseInt(e,10);if(this._textLines[i]){let i=this._styleMap[e].line;t.set("".concat(i),!0)}}for(let e in this.styles)t.has(e)||delete this.styles[e]}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return super.toObject(["minWidth","splitByGrapheme",...t])}}o(nb,"type","Textbox"),o(nb,"textLayoutProperties",[...nC.textLayoutProperties,"width"]),o(nb,"ownDefaults",{minWidth:20,dynamicMinWidth:2,lockScalingFlip:!0,noScaleCache:!1,_wordJoiners:/[ \t\r]/,splitByGrapheme:!1}),$.setClass(nb);class nS extends rB{shouldPerformLayout(t){return!!t.target.clipPath&&super.shouldPerformLayout(t)}shouldLayoutClipPath(){return!1}calcLayoutResult(t,e){let{target:i}=t,{clipPath:r,group:s}=i;if(!r||!this.shouldPerformLayout(t))return;let{width:n,height:o}=eo(rI(i,r)),a=new tn(n,o);if(r.absolutePositioned)return{center:ef(r.getRelativeCenterPoint(),void 0,s?s.calcTransformMatrix():void 0),size:a};{let s=r.getRelativeCenterPoint().transform(i.calcOwnMatrix(),!0);if(this.shouldPerformLayout(t)){let{center:i=new tn,correction:r=new tn}=this.calcBoundingBox(e,t)||{};return{center:i.add(s),correction:r.subtract(s),size:a}}return{center:i.getRelativeCenterPoint().add(s),size:a}}}}o(nS,"type","clip-path"),$.setClass(nS);class nw extends rB{getInitialSize(t,e){let{target:i}=t,{size:r}=e;return new tn(i.width||r.x,i.height||r.y)}}o(nw,"type","fixed"),$.setClass(nw);class nT extends rH{subscribeTargets(t){let e=t.target;t.targets.reduce((t,e)=>(e.parent&&t.add(e.parent),t),new Set).forEach(t=>{t.layoutManager.subscribeTargets({target:t,targets:[e]})})}unsubscribeTargets(t){let e=t.target,i=e.getObjects();t.targets.reduce((t,e)=>(e.parent&&t.add(e.parent),t),new Set).forEach(t=>{i.some(e=>e.parent===t)||t.layoutManager.unsubscribeTargets({target:t,targets:[e]})})}}class nO extends rN{static getDefaults(){return l(l({},super.getDefaults()),nO.ownDefaults)}constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),Object.assign(this,nO.ownDefaults),this.setOptions(e);let{left:i,top:r,layoutManager:s}=e;this.groupInit(t,{left:i,top:r,layoutManager:null!=s?s:new nT})}_shouldSetNestedCoords(){return!0}__objectSelectionMonitor(){}multiSelectAdd(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];"selection-order"===this.multiSelectionStacking?this.add(...e):e.forEach(t=>{let e=this._objects.findIndex(e=>e.isInFrontOf(t)),i=-1===e?this.size():e;this.insertAt(i,t)})}canEnterGroup(t){return this.getObjects().some(e=>e.isDescendantOf(t)||t.isDescendantOf(e))?(g("error","ActiveSelection: circular object trees are not supported, this call has no effect"),!1):super.canEnterGroup(t)}enterGroup(t,e){t.parent&&t.parent===t.group?t.parent._exitGroup(t):t.group&&t.parent!==t.group&&t.group.remove(t),this._enterGroup(t,e)}exitGroup(t,e){this._exitGroup(t,e),t.parent&&t.parent._enterGroup(t,!0)}_onAfterObjectsChange(t,e){super._onAfterObjectsChange(t,e);let i=new Set;e.forEach(t=>{let{parent:e}=t;e&&i.add(e)}),t===rL?i.forEach(t=>{t._onAfterObjectsChange(rF,e)}):i.forEach(t=>{t._set("dirty",!0)})}onDeselect(){return this.removeAll(),!1}toString(){return"#<ActiveSelection: (".concat(this.complexity(),")>")}shouldCache(){return!1}isOnACache(){return!1}_renderControls(t,e,i){t.save(),t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1;let r=l(l({hasControls:!1},i),{},{forActiveSelection:!0});for(let e=0;e<this._objects.length;e++)this._objects[e]._renderControls(t,r);super._renderControls(t,e),t.restore()}}o(nO,"type","ActiveSelection"),o(nO,"ownDefaults",{multiSelectionStacking:"canvas-stacking"}),$.setClass(nO),$.setClass(nO,"activeSelection");class nD{constructor(){o(this,"resources",{})}applyFilters(t,e,i,r,s){let n=s.getContext("2d");if(!n)return;n.drawImage(e,0,0,i,r);let o={sourceWidth:i,sourceHeight:r,imageData:n.getImageData(0,0,i,r),originalEl:e,originalImageData:n.getImageData(0,0,i,r),canvasEl:s,ctx:n,filterBackend:this};t.forEach(t=>{t.applyTo(o)});let{imageData:a}=o;return a.width===i&&a.height===r||(s.width=a.width,s.height=a.height),n.putImageData(a,0,0),o}}class nk{constructor(){let{tileSize:t=d.textureSize}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};o(this,"aPosition",new Float32Array([0,0,0,1,1,0,1,1])),o(this,"resources",{}),this.tileSize=t,this.setupGLContext(t,t),this.captureGPUInfo()}setupGLContext(t,e){this.dispose(),this.createWebGLCanvas(t,e)}createWebGLCanvas(t,e){let i=tm({width:t,height:e}),r=i.getContext("webgl",{alpha:!0,premultipliedAlpha:!1,depth:!1,stencil:!1,antialias:!1});r&&(r.clearColor(0,0,0,0),this.canvas=i,this.gl=r)}applyFilters(t,e,i,r,s,n){let o;let a=this.gl,l=s.getContext("2d");if(!a||!l)return;n&&(o=this.getCachedTexture(n,e));let h={originalWidth:e.width||e.naturalWidth||0,originalHeight:e.height||e.naturalHeight||0,sourceWidth:i,sourceHeight:r,destinationWidth:i,destinationHeight:r,context:a,sourceTexture:this.createTexture(a,i,r,o?void 0:e),targetTexture:this.createTexture(a,i,r),originalTexture:o||this.createTexture(a,i,r,o?void 0:e),passes:t.length,webgl:!0,aPosition:this.aPosition,programCache:this.programCache,pass:0,filterBackend:this,targetCanvas:s},c=a.createFramebuffer();return a.bindFramebuffer(a.FRAMEBUFFER,c),t.forEach(t=>{t&&t.applyTo(h)}),function(t){let e=t.targetCanvas,i=e.width,r=e.height,s=t.destinationWidth,n=t.destinationHeight;i===s&&r===n||(e.width=s,e.height=n)}(h),this.copyGLTo2D(a,h),a.bindTexture(a.TEXTURE_2D,null),a.deleteTexture(h.sourceTexture),a.deleteTexture(h.targetTexture),a.deleteFramebuffer(c),l.setTransform(1,0,0,1,0,0),h}dispose(){this.canvas&&(this.canvas=null,this.gl=null),this.clearWebGLCaches()}clearWebGLCaches(){this.programCache={},this.textureCache={}}createTexture(t,e,i,r,s){let{NEAREST:n,TEXTURE_2D:o,RGBA:a,UNSIGNED_BYTE:l,CLAMP_TO_EDGE:h,TEXTURE_MAG_FILTER:c,TEXTURE_MIN_FILTER:u,TEXTURE_WRAP_S:d,TEXTURE_WRAP_T:g}=t,f=t.createTexture();return t.bindTexture(o,f),t.texParameteri(o,c,s||n),t.texParameteri(o,u,s||n),t.texParameteri(o,d,h),t.texParameteri(o,g,h),r?t.texImage2D(o,0,a,a,l,r):t.texImage2D(o,0,a,e,i,0,a,l,null),f}getCachedTexture(t,e,i){let{textureCache:r}=this;if(r[t])return r[t];{let s=this.createTexture(this.gl,e.width,e.height,e,i);return s&&(r[t]=s),s}}evictCachesForKey(t){this.textureCache[t]&&(this.gl.deleteTexture(this.textureCache[t]),delete this.textureCache[t])}copyGLTo2D(t,e){let i=t.canvas,r=e.targetCanvas,s=r.getContext("2d");if(!s)return;s.translate(0,r.height),s.scale(1,-1);let n=i.height-r.height;s.drawImage(i,0,n,r.width,r.height,0,0,r.width,r.height)}copyGLTo2DPutImageData(t,e){let i=e.targetCanvas.getContext("2d"),r=e.destinationWidth,s=e.destinationHeight,n=r*s*4;if(!i)return;let o=new Uint8Array(this.imageBuffer,0,n),a=new Uint8ClampedArray(this.imageBuffer,0,n);t.readPixels(0,0,r,s,t.RGBA,t.UNSIGNED_BYTE,o);let l=new ImageData(a,r,s);i.putImageData(l,0,0)}captureGPUInfo(){if(this.gpuInfo)return this.gpuInfo;let t=this.gl,e={renderer:"",vendor:""};if(!t)return e;let i=t.getExtension("WEBGL_debug_renderer_info");if(i){let r=t.getParameter(i.UNMASKED_RENDERER_WEBGL),s=t.getParameter(i.UNMASKED_VENDOR_WEBGL);r&&(e.renderer=r.toLowerCase()),s&&(e.vendor=s.toLowerCase())}return this.gpuInfo=e,e}}function nM(){return n||arguments.length>0&&void 0!==arguments[0]&&!arguments[0]||(n=function(){let{WebGLProbe:t}=y();return t.queryWebGL(tf()),d.enableGLFiltering&&t.isSupported(d.textureSize)?new nk({tileSize:d.textureSize}):new nD}()),n}let nE=["filters","resizeFilter","src","crossOrigin","type"],nP=["cropX","cropY"];class nA extends i4{static getDefaults(){return l(l({},super.getDefaults()),nA.ownDefaults)}constructor(t,e){super(),o(this,"_lastScaleX",1),o(this,"_lastScaleY",1),o(this,"_filterScalingX",1),o(this,"_filterScalingY",1),this.filters=[],Object.assign(this,nA.ownDefaults),this.setOptions(e),this.cacheKey="texture".concat(tg()),this.setElement("string"==typeof t?(this.canvas&&t3(this.canvas.getElement())||x()).getElementById(t):t,e)}getElement(){return this._element}setElement(t){var e;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.removeTexture(this.cacheKey),this.removeTexture("".concat(this.cacheKey,"_filtered")),this._element=t,this._originalElement=t,this._setWidthHeight(i),null===(e=t.classList)||void 0===e||e.add(nA.CSS_CANVAS),0!==this.filters.length&&this.applyFilters(),this.resizeFilter&&this.applyResizeFilters()}removeTexture(t){let e=nM(!1);e instanceof nk&&e.evictCachesForKey(t)}dispose(){super.dispose(),this.removeTexture(this.cacheKey),this.removeTexture("".concat(this.cacheKey,"_filtered")),this._cacheContext=null,["_originalElement","_element","_filteredEl","_cacheCanvas"].forEach(t=>{let e=this[t];e&&y().dispose(e),this[t]=void 0})}getCrossOrigin(){return this._originalElement&&(this._originalElement.crossOrigin||null)}getOriginalSize(){let t=this.getElement();return t?{width:t.naturalWidth||t.width,height:t.naturalHeight||t.height}:{width:0,height:0}}_stroke(t){if(!this.stroke||0===this.strokeWidth)return;let e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,-i),t.lineTo(e,-i),t.lineTo(e,i),t.lineTo(-e,i),t.lineTo(-e,-i),t.closePath()}toObject(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[];return this.filters.forEach(t=>{t&&e.push(t.toObject())}),l(l({},super.toObject([...nP,...t])),{},{src:this.getSrc(),crossOrigin:this.getCrossOrigin(),filters:e},this.resizeFilter?{resizeFilter:this.resizeFilter.toObject()}:{})}hasCrop(){return!!this.cropX||!!this.cropY||this.width<this._element.width||this.height<this._element.height}_toSVG(){let t=[],e=this._element,i=-this.width/2,r=-this.height/2,s=[],n=[],o="",a="";if(!e)return[];if(this.hasCrop()){let t=tg();s.push('<clipPath id="imageCrop_'+t+'">\n','	<rect x="'+i+'" y="'+r+'" width="'+this.width+'" height="'+this.height+'" />\n',"</clipPath>\n"),o=' clip-path="url(#imageCrop_'+t+')" '}if(this.imageSmoothing||(a=' image-rendering="optimizeSpeed"'),t.push("	<image ","COMMON_PARTS",'xlink:href="'.concat(this.getSvgSrc(!0),'" x="').concat(i-this.cropX,'" y="').concat(r-this.cropY,'" width="').concat(e.width||e.naturalWidth,'" height="').concat(e.height||e.naturalHeight,'"').concat(a).concat(o,"></image>\n")),this.stroke||this.strokeDashArray){let t=this.fill;this.fill=null,n=['	<rect x="'.concat(i,'" y="').concat(r,'" width="').concat(this.width,'" height="').concat(this.height,'" style="').concat(this.getSvgStyles(),'" />\n')],this.fill=t}return this.paintFirst!==K?s.concat(n,t):s.concat(t,n)}getSrc(t){let e=t?this._element:this._originalElement;return e?e.toDataURL?e.toDataURL():this.srcFromAttribute?e.getAttribute("src")||"":e.src:this.src||""}getSvgSrc(t){return this.getSrc(t)}setSrc(t){let{crossOrigin:e,signal:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return tR(t,{crossOrigin:e,signal:i}).then(t=>{void 0!==e&&this.set({crossOrigin:e}),this.setElement(t)})}toString(){return'#<Image: { src: "'.concat(this.getSrc(),'" }>')}applyResizeFilters(){let t=this.resizeFilter,e=this.minimumScaleTrigger,i=this.getTotalObjectScaling(),r=i.x,s=i.y,n=this._filteredEl||this._originalElement;if(this.group&&this.set("dirty",!0),!t||r>e&&s>e)return this._element=n,this._filterScalingX=1,this._filterScalingY=1,this._lastScaleX=r,void(this._lastScaleY=s);let o=tm(n),{width:a,height:l}=n;this._element=o,this._lastScaleX=t.scaleX=r,this._lastScaleY=t.scaleY=s,nM().applyFilters([t],n,a,l,this._element),this._filterScalingX=o.width/this._originalElement.width,this._filterScalingY=o.height/this._originalElement.height}applyFilters(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.filters||[];if(t=t.filter(t=>t&&!t.isNeutralState()),this.set("dirty",!0),this.removeTexture("".concat(this.cacheKey,"_filtered")),0===t.length)return this._element=this._originalElement,this._filteredEl=void 0,this._filterScalingX=1,void(this._filterScalingY=1);let e=this._originalElement,i=e.naturalWidth||e.width,r=e.naturalHeight||e.height;if(this._element===this._originalElement){let t=tm({width:i,height:r});this._element=t,this._filteredEl=t}else this._filteredEl&&(this._element=this._filteredEl,this._filteredEl.getContext("2d").clearRect(0,0,i,r),this._lastScaleX=1,this._lastScaleY=1);nM().applyFilters(t,this._originalElement,i,r,this._element,this.cacheKey),this._originalElement.width===this._element.width&&this._originalElement.height===this._element.height||(this._filterScalingX=this._element.width/this._originalElement.width,this._filterScalingY=this._element.height/this._originalElement.height)}_render(t){t.imageSmoothingEnabled=this.imageSmoothing,!0!==this.isMoving&&this.resizeFilter&&this._needsResize()&&this.applyResizeFilters(),this._stroke(t),this._renderPaintInOrder(t)}drawCacheOnCanvas(t){t.imageSmoothingEnabled=this.imageSmoothing,super.drawCacheOnCanvas(t)}shouldCache(){return this.needsItsOwnCache()}_renderFill(t){let e=this._element;if(!e)return;let i=this._filterScalingX,r=this._filterScalingY,s=this.width,n=this.height,o=Math.max(this.cropX,0),a=Math.max(this.cropY,0),l=e.naturalWidth||e.width,h=e.naturalHeight||e.height,c=o*i,u=a*r,d=Math.min(s*i,l-c),g=Math.min(n*r,h-u),f=Math.min(s,l/i-o),p=Math.min(n,h/r-a);e&&t.drawImage(e,c,u,d,g,-s/2,-n/2,f,p)}_needsResize(){let t=this.getTotalObjectScaling();return t.x!==this._lastScaleX||t.y!==this._lastScaleY}_resetWidthHeight(){this.set(this.getOriginalSize())}_setWidthHeight(){let{width:t,height:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=this.getOriginalSize();this.width=t||i.width,this.height=e||i.height}parsePreserveAspectRatioAttribute(){let t=tJ(this.preserveAspectRatio||""),e=this.width,i=this.height,r={width:e,height:i},s,n=this._element.width,o=this._element.height,a=1,l=1,h=0,c=0,u=0,d=0;return t&&(t.alignX!==F||t.alignY!==F)?("meet"===t.meetOrSlice&&(s=(e-n*(a=l=rU(this._element,r)))/2,"Min"===t.alignX&&(h=-s),"Max"===t.alignX&&(h=s),s=(i-o*l)/2,"Min"===t.alignY&&(c=-s),"Max"===t.alignY&&(c=s)),"slice"===t.meetOrSlice&&(s=n-e/(a=l=rq(this._element,r)),"Mid"===t.alignX&&(u=s/2),"Max"===t.alignX&&(u=s),s=o-i/l,"Mid"===t.alignY&&(d=s/2),"Max"===t.alignY&&(d=s),n=e/a,o=i/l)):(a=e/n,l=i/o),{width:n,height:o,scaleX:a,scaleY:l,offsetLeft:h,offsetTop:c,cropX:u,cropY:d}}static fromObject(t,e){let{filters:i,resizeFilter:r,src:s,crossOrigin:n,type:o}=t,a=h(t,nE);return Promise.all([tR(s,l(l({},e),{},{crossOrigin:n})),i&&tI(i,e),r&&tI([r],e),tB(a,e)]).then(t=>{let[e,i=[],[r]=[],n={}]=t;return new this(e,l(l({},a),{},{src:s,filters:i,resizeFilter:r},n))})}static fromURL(t){let{crossOrigin:e=null,signal:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0;return tR(t,{crossOrigin:e,signal:i}).then(t=>new this(t,r))}static async fromElement(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0,r=rM(t,this.ATTRIBUTE_NAMES,i);return this.fromURL(r["xlink:href"],e,r).catch(t=>(g("log","Unable to parse Image",t),null))}}function nj(t){if(!eR.test(t.nodeName))return{};let e=t.getAttribute("viewBox"),i,r,s=1,n=1,o=0,a=0,l=t.getAttribute("width"),h=t.getAttribute("height"),c=t.getAttribute("x")||0,u=t.getAttribute("y")||0,d=!(e&&eB.test(e)),g=!l||!h||"100%"===l||"100%"===h,f="",p=0,m=0;if(d&&(c||u)&&t.parentNode&&"#document"!==t.parentNode.nodeName&&(f=" translate("+tK(c||"0")+" "+tK(u||"0")+") ",i=(t.getAttribute("transform")||"")+f,t.setAttribute("transform",i),t.removeAttribute("x"),t.removeAttribute("y")),d&&g)return{width:0,height:0};let v={width:0,height:0};if(d)return v.width=tK(l),v.height=tK(h),v;let _=e.match(eB);o=-parseFloat(_[1]),a=-parseFloat(_[2]);let y=parseFloat(_[3]),x=parseFloat(_[4]);v.minX=o,v.minY=a,v.viewBoxWidth=y,v.viewBoxHeight=x,g?(v.width=y,v.height=x):(v.width=tK(l),v.height=tK(h),s=v.width/y,n=v.height/x);let C=tJ(t.getAttribute("preserveAspectRatio")||"");if(C.alignX!==F&&("meet"===C.meetOrSlice&&(n=s=s>n?n:s),"slice"===C.meetOrSlice&&(n=s=s>n?s:n),p=v.width-y*s,m=v.height-x*s,"Mid"===C.alignX&&(p/=2),"Mid"===C.alignY&&(m/=2),"Min"===C.alignX&&(p=0),"Min"===C.alignY&&(m=0)),1===s&&1===n&&0===o&&0===a&&0===c&&0===u)return v;if((c||u)&&"#document"!==t.parentNode.nodeName&&(f=" translate("+tK(c||"0")+" "+tK(u||"0")+") "),i=f+" matrix("+s+" 0 0 "+n+" "+(o*s+p)+" "+(a*n+m)+") ","svg"===t.nodeName){for(r=t.ownerDocument.createElementNS(eE,"g");t.firstChild;)r.appendChild(t.firstChild);t.appendChild(r)}else(r=t).removeAttribute("x"),r.removeAttribute("y"),i=r.getAttribute("transform")+i;return r.setAttribute("transform",i),v}o(nA,"type","Image"),o(nA,"cacheProperties",[...e2,...nP]),o(nA,"ownDefaults",{strokeWidth:0,srcFromAttribute:!1,minimumScaleTrigger:.5,cropX:0,cropY:0,imageSmoothing:!0}),o(nA,"CSS_CANVAS","canvas-img"),o(nA,"ATTRIBUTE_NAMES",[...rc,"x","y","width","height","preserveAspectRatio","xlink:href","crossOrigin","image-rendering"]),$.setClass(nA),$.setSVGClass(nA);let nF=t=>t.tagName.replace("svg:",""),nL=ek(["pattern","defs","symbol","metadata","clipPath","mask","desc"]);function nR(t,e){let i,r,s,n,o=[];for(s=0,n=e.length;s<n;s++)i=e[s],r=t.getElementsByTagNameNS("http://www.w3.org/2000/svg",i),o=o.concat(Array.from(r));return o}let nI="xlink:href",nB=t=>$.getSVGClass(nF(t).toLowerCase());class nX{constructor(t,e,i,r,s){this.elements=t,this.options=e,this.reviver=i,this.regexUrl=/^url\(['"]?#([^'"]+)['"]?\)/g,this.doc=r,this.clipPaths=s,this.gradientDefs=function(t){let e=nR(t,null),i={},r=e.length;for(;r--;){let s=e[r];s.getAttribute("xlink:href")&&function t(e,i){var r;let s=(null===(r=i.getAttribute(nI))||void 0===r?void 0:r.slice(1))||"",n=e.getElementById(s);if(n&&n.getAttribute(nI)&&t(e,n),n&&(null.forEach(t=>{let e=n.getAttribute(t);!i.hasAttribute(t)&&e&&i.setAttribute(t,e)}),!i.children.length)){let t=n.cloneNode(!0);for(;t.firstChild;)i.appendChild(t.firstChild)}i.removeAttribute(nI)}(t,s);let n=s.getAttribute("id");n&&(i[n]=s)}return i}(r),this.cssRules=function(t){let e,i;let r=t.getElementsByTagName("style"),s={};for(e=0,i=r.length;e<i;e++){let t=(r[e].textContent||"").replace(/\/\*[\s\S]*?\*\//g,"");""!==t.trim()&&t.split("}").filter((t,e,i)=>i.length>1&&t.trim()).forEach(t=>{if((t.match(/{/g)||[]).length>1&&t.trim().startsWith("@"))return;let r=t.split("{"),n={},o=r[1].trim().split(";").filter(function(t){return t.trim()});for(e=0,i=o.length;e<i;e++){let t=o[e].split(":"),i=t[0].trim(),r=t[1].trim();n[i]=r}(t=r[0].trim()).split(",").forEach(t=>{""!==(t=t.replace(/^svg/i,"").trim())&&(s[t]=l(l({},s[t]||{}),n))})})}return s}(r)}parse(){return Promise.all(this.elements.map(t=>this.createObject(t)))}async createObject(t){let e=nB(t);if(e){let i=await e.fromElement(t,this.options,this.cssRules);return this.resolveGradient(i,t,K),this.resolveGradient(i,t,J),i instanceof nA&&i._originalElement?sf(i,i.parsePreserveAspectRatioAttribute()):sf(i),await this.resolveClipPath(i,t),this.reviver&&this.reviver(t,i),i}return null}extractPropertyDefinition(t,e,i){let r=t[e],s=this.regexUrl;if(!s.test(r))return;s.lastIndex=0;let n=s.exec(r)[1];return s.lastIndex=0,i[n]}resolveGradient(t,e,i){let r=this.extractPropertyDefinition(t,i,this.gradientDefs);if(r){let s=e.getAttribute(i+"-opacity"),n=sR.fromElement(r,t,l(l({},this.options),{},{opacity:s}));t.set(i,n)}}async resolveClipPath(t,e,i){let r=this.extractPropertyDefinition(t,"clipPath",this.clipPaths);if(r){let s=tS(t.calcTransformMatrix()),n=r[0].parentElement,o=e;for(;!i&&o.parentElement&&o.getAttribute("clip-path")!==t.clipPath;)o=o.parentElement;o.parentElement.appendChild(n);let a=rO("".concat(o.getAttribute("transform")||""," ").concat(n.getAttribute("originalTransform")||""));n.setAttribute("transform","matrix(".concat(a.join(","),")"));let l=await Promise.all(r.map(t=>nB(t).fromElement(t,this.options,this.cssRules).then(t=>(sf(t),t.fillRule=t.clipRule,delete t.clipRule,t)))),h=1===l.length?l[0]:new rN(l),c=tw(s,h.calcTransformMatrix());h.clipPath&&await this.resolveClipPath(h,o,n.getAttribute("clip-path")?o:void 0);let{scaleX:u,scaleY:d,angle:g,skewX:f,translateX:p,translateY:m}=tD(c);h.set({flipX:!1,flipY:!1}),h.set({scaleX:u,scaleY:d,angle:g,skewX:f,skewY:0}),h.setPositionByOrigin(new tn(p,m),E,E),t.clipPath=h}else delete t.clipPath}}let nY=t=>eL.test(nF(t)),nW=()=>({objects:[],elements:[],options:{},allElements:[]}),nV=t=>function(e,i,r){let{points:s,pathOffset:n}=r;return new tn(s[t]).subtract(n).transform(tw(r.getViewportTransform(),r.calcTransformMatrix()))},nH=(t,e,i,r)=>{let{target:s,pointIndex:n}=e,o=ef(new tn(i,r),void 0,s.calcOwnMatrix());return s.points[n]=o.add(s.pathOffset),s.setDimensions(),!0},nz=(t,e)=>function(i,r,s,n){let o=r.target,a=new tn(o.points[(t>0?t:o.points.length)-1]),h=a.subtract(o.pathOffset).transform(o.calcOwnMatrix()),c=e(i,l(l({},r),{},{pointIndex:t}),s,n),u=a.subtract(o.pathOffset).transform(o.calcOwnMatrix()).subtract(h);return o.left-=u.x,o.top-=u.y,c},nG=t=>iO(V,nz(t,nH)),nN=(t,e,i)=>{let{path:r,pathOffset:s}=t,n=r[e];return new tn(n[i]-s.x,n[i+1]-s.y).transform(tw(t.getViewportTransform(),t.calcTransformMatrix()))};function nU(t,e,i){let{commandIndex:r,pointIndex:s}=this;return nN(i,r,s)}function nq(t,e,i,r){let{target:s}=e,{commandIndex:n,pointIndex:o}=this,a=((t,e,i,r,s)=>{let{path:n,pathOffset:o}=t,a=n[(r>0?r:n.length)-1],l=new tn(a[s],a[s+1]),h=l.subtract(o).transform(t.calcOwnMatrix()),c=ef(new tn(e,i),void 0,t.calcOwnMatrix());n[r][s]=c.x+o.x,n[r][s+1]=c.y+o.y,t.setDimensions();let u=l.subtract(t.pathOffset).transform(t.calcOwnMatrix()).subtract(h);return t.left-=u.x,t.top-=u.y,t.set("dirty",!0),!0})(s,i,r,n,o);return ev(this.actionName,l(l({},eS(t,e,i,r)),{},{commandIndex:n,pointIndex:o})),a}class nK extends iP{constructor(t){super(t)}render(t,e,i,r,s){let n=l(l({},r),{},{cornerColor:this.controlFill,cornerStrokeColor:this.controlStroke,transparentCorners:!this.controlFill});super.render(t,e,i,n,s)}}class nJ extends nK{constructor(t){super(t)}render(t,e,i,r,s){let{path:n}=s,{commandIndex:o,pointIndex:a,connectToCommandIndex:l,connectToPointIndex:h}=this;t.save(),t.strokeStyle=this.controlStroke,this.connectionDashArray&&t.setLineDash(this.connectionDashArray);let[c]=n[o],u=nN(s,l,h);if("Q"===c){let r=nN(s,o,a+2);t.moveTo(r.x,r.y),t.lineTo(e,i)}else t.moveTo(e,i);t.lineTo(u.x,u.y),t.stroke(),t.restore(),super.render(t,e,i,r,s)}}let nQ=(t,e,i,r,s,n)=>new(i?nJ:nK)(l(l({commandIndex:t,pointIndex:e,actionName:"modifyPath",positionHandler:nU,actionHandler:nq,connectToCommandIndex:s,connectToPointIndex:n},r),i?r.controlPointStyle:r.pointStyle));Object.freeze({__proto__:null,changeWidth:ik,createObjectDefaultControls:i0,createPathControls:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i={},r="M";return t.path.forEach((t,s)=>{let n=t[0];switch("Z"!==n&&(i["c_".concat(s,"_").concat(n)]=nQ(s,t.length-2,!1,e)),n){case"C":let o;i["c_".concat(s,"_C_CP_1")]=nQ(s,1,!0,e,s-1,"C"===(o=r)?5:"Q"===o?3:1),i["c_".concat(s,"_C_CP_2")]=nQ(s,3,!0,e,s,5);break;case"Q":i["c_".concat(s,"_Q_CP_1")]=nQ(s,1,!0,e,s,3)}r=n}),i},createPolyActionHandler:nG,createPolyControls:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i={};for(let r=0;r<("number"==typeof t?t:t.points.length);r++)i["p".concat(r)]=new iP(l({actionName:V,positionHandler:nV(r),actionHandler:nG(r)},e));return i},createPolyPositionHandler:nV,createResizeControls:i1,createTextboxDefaultControls:i2,dragHandler:eO,factoryPolyActionHandler:nz,getLocalPoint:eT,polyActionHandler:nH,renderCircleControl:iM,renderSquareControl:iE,rotationStyleHandler:iA,rotationWithSnapping:ij,scaleCursorStyleHandler:iI,scaleOrSkewActionName:iJ,scaleSkewCursorStyleHandler:iQ,scalingEqually:iX,scalingX:iY,scalingXOrSkewingY:iZ,scalingY:iW,scalingYOrSkewingX:i$,skewCursorStyleHandler:iG,skewHandlerX:iU,skewHandlerY:iq,wrapWithFireEvent:iO,wrapWithFixedAnchor:iD});let nZ=t=>void 0!==t.webgl,n$="precision highp float",n0="\n    ".concat(n$,";\n    varying vec2 vTexCoord;\n    uniform sampler2D uTexture;\n    void main() {\n      gl_FragColor = texture2D(uTexture, vTexCoord);\n    }"),n1=["type"],n2=["type"],n5=RegExp(n$,"g");class n3{get type(){return this.constructor.type}constructor(){let t=h(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n1);Object.assign(this,this.constructor.defaults,t)}getFragmentSource(){return n0}getVertexSource(){return"\n    attribute vec2 aPosition;\n    varying vec2 vTexCoord;\n    void main() {\n      vTexCoord = aPosition;\n      gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);\n    }"}createProgram(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getFragmentSource(),i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.getVertexSource(),{WebGLProbe:{GLPrecision:r="highp"}}=y();"highp"!==r&&(e=e.replace(n5,n$.replace("highp",r)));let s=t.createShader(t.VERTEX_SHADER),n=t.createShader(t.FRAGMENT_SHADER),o=t.createProgram();if(!s||!n||!o)throw new f("Vertex, fragment shader or program creation error");if(t.shaderSource(s,i),t.compileShader(s),!t.getShaderParameter(s,t.COMPILE_STATUS))throw new f("Vertex shader compile error for ".concat(this.type,": ").concat(t.getShaderInfoLog(s)));if(t.shaderSource(n,e),t.compileShader(n),!t.getShaderParameter(n,t.COMPILE_STATUS))throw new f("Fragment shader compile error for ".concat(this.type,": ").concat(t.getShaderInfoLog(n)));if(t.attachShader(o,s),t.attachShader(o,n),t.linkProgram(o),!t.getProgramParameter(o,t.LINK_STATUS))throw new f('Shader link error for "'.concat(this.type,'" ').concat(t.getProgramInfoLog(o)));let a=this.getUniformLocations(t,o)||{};return a.uStepW=t.getUniformLocation(o,"uStepW"),a.uStepH=t.getUniformLocation(o,"uStepH"),{program:o,attributeLocations:this.getAttributeLocations(t,o),uniformLocations:a}}getAttributeLocations(t,e){return{aPosition:t.getAttribLocation(e,"aPosition")}}getUniformLocations(t,e){let i=this.constructor.uniformLocations,r={};for(let s=0;s<i.length;s++)r[i[s]]=t.getUniformLocation(e,i[s]);return r}sendAttributeData(t,e,i){let r=e.aPosition,s=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,s),t.enableVertexAttribArray(r),t.vertexAttribPointer(r,2,t.FLOAT,!1,0,0),t.bufferData(t.ARRAY_BUFFER,i,t.STATIC_DRAW)}_setupFrameBuffer(t){let e=t.context;if(t.passes>1){let i=t.destinationWidth,r=t.destinationHeight;t.sourceWidth===i&&t.sourceHeight===r||(e.deleteTexture(t.targetTexture),t.targetTexture=t.filterBackend.createTexture(e,i,r)),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,t.targetTexture,0)}else e.bindFramebuffer(e.FRAMEBUFFER,null),e.finish()}_swapTextures(t){t.passes--,t.pass++;let e=t.targetTexture;t.targetTexture=t.sourceTexture,t.sourceTexture=e}isNeutralState(t){return!1}applyTo(t){nZ(t)?(this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)}applyTo2d(t){}getCacheKey(){return this.type}retrieveShader(t){let e=this.getCacheKey();return t.programCache[e]||(t.programCache[e]=this.createProgram(t.context)),t.programCache[e]}applyToWebGL(t){let e=t.context,i=this.retrieveShader(t);0===t.pass&&t.originalTexture?e.bindTexture(e.TEXTURE_2D,t.originalTexture):e.bindTexture(e.TEXTURE_2D,t.sourceTexture),e.useProgram(i.program),this.sendAttributeData(e,i.attributeLocations,t.aPosition),e.uniform1f(i.uniformLocations.uStepW,1/t.sourceWidth),e.uniform1f(i.uniformLocations.uStepH,1/t.sourceHeight),this.sendUniformData(e,i.uniformLocations),e.viewport(0,0,t.destinationWidth,t.destinationHeight),e.drawArrays(e.TRIANGLE_STRIP,0,4)}bindAdditionalTexture(t,e,i){t.activeTexture(i),t.bindTexture(t.TEXTURE_2D,e),t.activeTexture(t.TEXTURE0)}unbindAdditionalTexture(t,e){t.activeTexture(e),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE0)}sendUniformData(t,e){}createHelpLayer(t){if(!t.helpLayer){let{sourceWidth:e,sourceHeight:i}=t,r=tm({width:e,height:i});t.helpLayer=r}}toObject(){let t=Object.keys(this.constructor.defaults||{});return l({type:this.type},t.reduce((t,e)=>(t[e]=this[e],t),{}))}toJSON(){return this.toObject()}static async fromObject(t,e){return new this(h(t,n2))}}o(n3,"type","BaseFilter"),o(n3,"uniformLocations",[]);let n4={multiply:"gl_FragColor.rgb *= uColor.rgb;\n",screen:"gl_FragColor.rgb = 1.0 - (1.0 - gl_FragColor.rgb) * (1.0 - uColor.rgb);\n",add:"gl_FragColor.rgb += uColor.rgb;\n",difference:"gl_FragColor.rgb = abs(gl_FragColor.rgb - uColor.rgb);\n",subtract:"gl_FragColor.rgb -= uColor.rgb;\n",lighten:"gl_FragColor.rgb = max(gl_FragColor.rgb, uColor.rgb);\n",darken:"gl_FragColor.rgb = min(gl_FragColor.rgb, uColor.rgb);\n",exclusion:"gl_FragColor.rgb += uColor.rgb - 2.0 * (uColor.rgb * gl_FragColor.rgb);\n",overlay:"\n    if (uColor.r < 0.5) {\n      gl_FragColor.r *= 2.0 * uColor.r;\n    } else {\n      gl_FragColor.r = 1.0 - 2.0 * (1.0 - gl_FragColor.r) * (1.0 - uColor.r);\n    }\n    if (uColor.g < 0.5) {\n      gl_FragColor.g *= 2.0 * uColor.g;\n    } else {\n      gl_FragColor.g = 1.0 - 2.0 * (1.0 - gl_FragColor.g) * (1.0 - uColor.g);\n    }\n    if (uColor.b < 0.5) {\n      gl_FragColor.b *= 2.0 * uColor.b;\n    } else {\n      gl_FragColor.b = 1.0 - 2.0 * (1.0 - gl_FragColor.b) * (1.0 - uColor.b);\n    }\n    ",tint:"\n    gl_FragColor.rgb *= (1.0 - uColor.a);\n    gl_FragColor.rgb += uColor.rgb;\n    "};class n8 extends n3{getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return"\n      precision highp float;\n      uniform sampler2D uTexture;\n      uniform vec4 uColor;\n      varying vec2 vTexCoord;\n      void main() {\n        vec4 color = texture2D(uTexture, vTexCoord);\n        gl_FragColor = color;\n        if (color.a > 0.0) {\n          ".concat(n4[this.mode],"\n        }\n      }\n      ")}applyTo2d(t){let{imageData:{data:e}}=t,i=new tU(this.color).getSource(),r=this.alpha,s=i[0]*r,n=i[1]*r,o=i[2]*r,a=1-r;for(let t=0;t<e.length;t+=4){let i,r,l;let h=e[t],c=e[t+1],u=e[t+2];switch(this.mode){case"multiply":i=h*s/255,r=c*n/255,l=u*o/255;break;case"screen":i=255-(255-h)*(255-s)/255,r=255-(255-c)*(255-n)/255,l=255-(255-u)*(255-o)/255;break;case"add":i=h+s,r=c+n,l=u+o;break;case"difference":i=Math.abs(h-s),r=Math.abs(c-n),l=Math.abs(u-o);break;case"subtract":i=h-s,r=c-n,l=u-o;break;case"darken":i=Math.min(h,s),r=Math.min(c,n),l=Math.min(u,o);break;case"lighten":i=Math.max(h,s),r=Math.max(c,n),l=Math.max(u,o);break;case"overlay":i=s<128?2*h*s/255:255-2*(255-h)*(255-s)/255,r=n<128?2*c*n/255:255-2*(255-c)*(255-n)/255,l=o<128?2*u*o/255:255-2*(255-u)*(255-o)/255;break;case"exclusion":i=s+h-2*s*h/255,r=n+c-2*n*c/255,l=o+u-2*o*u/255;break;case"tint":i=s+h*a,r=n+c*a,l=o+u*a}e[t]=i,e[t+1]=r,e[t+2]=l}}sendUniformData(t,e){let i=new tU(this.color).getSource();i[0]=this.alpha*i[0]/255,i[1]=this.alpha*i[1]/255,i[2]=this.alpha*i[2]/255,i[3]=this.alpha,t.uniform4fv(e.uColor,i)}}o(n8,"defaults",{color:"#F95C63",mode:"multiply",alpha:1}),o(n8,"type","BlendColor"),o(n8,"uniformLocations",["uColor"]),$.setClass(n8);let n6={multiply:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform sampler2D uImage;\n    uniform vec4 uColor;\n    varying vec2 vTexCoord;\n    varying vec2 vTexCoord2;\n    void main() {\n      vec4 color = texture2D(uTexture, vTexCoord);\n      vec4 color2 = texture2D(uImage, vTexCoord2);\n      color.rgba *= color2.rgba;\n      gl_FragColor = color;\n    }\n    ",mask:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform sampler2D uImage;\n    uniform vec4 uColor;\n    varying vec2 vTexCoord;\n    varying vec2 vTexCoord2;\n    void main() {\n      vec4 color = texture2D(uTexture, vTexCoord);\n      vec4 color2 = texture2D(uImage, vTexCoord2);\n      color.a = color2.a;\n      gl_FragColor = color;\n    }\n    "},n9=["type","image"];class n7 extends n3{getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return n6[this.mode]}getVertexSource(){return"\n    attribute vec2 aPosition;\n    varying vec2 vTexCoord;\n    varying vec2 vTexCoord2;\n    uniform mat3 uTransformMatrix;\n    void main() {\n      vTexCoord = aPosition;\n      vTexCoord2 = (uTransformMatrix * vec3(aPosition, 1.0)).xy;\n      gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);\n    }\n    "}applyToWebGL(t){let e=t.context,i=this.createTexture(t.filterBackend,this.image);this.bindAdditionalTexture(e,i,e.TEXTURE1),super.applyToWebGL(t),this.unbindAdditionalTexture(e,e.TEXTURE1)}createTexture(t,e){return t.getCachedTexture(e.cacheKey,e.getElement())}calculateMatrix(){let t=this.image,{width:e,height:i}=t.getElement();return[1/t.scaleX,0,0,0,1/t.scaleY,0,-t.left/e,-t.top/i,1]}applyTo2d(t){let{imageData:{data:e,width:i,height:r},filterBackend:{resources:s}}=t,n=this.image;s.blendImage||(s.blendImage=tf());let o=s.blendImage,a=o.getContext("2d");o.width!==i||o.height!==r?(o.width=i,o.height=r):a.clearRect(0,0,i,r),a.setTransform(n.scaleX,0,0,n.scaleY,n.left,n.top),a.drawImage(n.getElement(),0,0,i,r);let l=a.getImageData(0,0,i,r).data;for(let t=0;t<e.length;t+=4){let i=e[t],r=e[t+1],s=e[t+2],n=e[t+3],o=l[t],a=l[t+1],h=l[t+2],c=l[t+3];switch(this.mode){case"multiply":e[t]=i*o/255,e[t+1]=r*a/255,e[t+2]=s*h/255,e[t+3]=n*c/255;break;case"mask":e[t+3]=c}}}sendUniformData(t,e){let i=this.calculateMatrix();t.uniform1i(e.uImage,1),t.uniformMatrix3fv(e.uTransformMatrix,!1,i)}toObject(){return l(l({},super.toObject()),{},{image:this.image&&this.image.toObject()})}static async fromObject(t,e){let{type:i,image:r}=t,s=h(t,n9);return nA.fromObject(r,e).then(t=>new this(l(l({},s),{},{image:t})))}}o(n7,"type","BlendImage"),o(n7,"defaults",{mode:"multiply",alpha:1}),o(n7,"uniformLocations",["uTransformMatrix","uImage"]),$.setClass(n7);class ot extends n3{getFragmentSource(){return"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform vec2 uDelta;\n    varying vec2 vTexCoord;\n    const float nSamples = 15.0;\n    vec3 v3offset = vec3(12.9898, 78.233, 151.7182);\n    float random(vec3 scale) {\n      /* use the fragment position for a different seed per-pixel */\n      return fract(sin(dot(gl_FragCoord.xyz, scale)) * 43758.5453);\n    }\n    void main() {\n      vec4 color = vec4(0.0);\n      float total = 0.0;\n      float offset = random(v3offset);\n      for (float t = -nSamples; t <= nSamples; t++) {\n        float percent = (t + offset - 0.5) / nSamples;\n        float weight = 1.0 - abs(percent);\n        color += texture2D(uTexture, vTexCoord + uDelta * percent) * weight;\n        total += weight;\n      }\n      gl_FragColor = color / total;\n    }\n  "}applyTo(t){nZ(t)?(this.aspectRatio=t.sourceWidth/t.sourceHeight,t.passes++,this._setupFrameBuffer(t),this.horizontal=!0,this.applyToWebGL(t),this._swapTextures(t),this._setupFrameBuffer(t),this.horizontal=!1,this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)}applyTo2d(t){t.imageData=this.simpleBlur(t)}simpleBlur(t){let e,i,r,s,{ctx:n,imageData:o,filterBackend:{resources:a}}=t,{width:l,height:h}=o;a.blurLayer1||(a.blurLayer1=tf(),a.blurLayer2=tf());let c=a.blurLayer1,u=a.blurLayer2;c.width===l&&c.height===h||(u.width=c.width=l,u.height=c.height=h);let d=c.getContext("2d"),g=u.getContext("2d"),f=.06*this.blur*.5;for(d.putImageData(o,0,0),g.clearRect(0,0,l,h),s=-15;s<=15;s++)e=(Math.random()-.5)/4,r=f*(i=s/15)*l+e,g.globalAlpha=1-Math.abs(i),g.drawImage(c,r,e),d.drawImage(u,0,0),g.globalAlpha=1,g.clearRect(0,0,u.width,u.height);for(s=-15;s<=15;s++)e=(Math.random()-.5)/4,r=f*(i=s/15)*h+e,g.globalAlpha=1-Math.abs(i),g.drawImage(c,e,r),d.drawImage(u,0,0),g.globalAlpha=1,g.clearRect(0,0,u.width,u.height);n.drawImage(c,0,0);let p=n.getImageData(0,0,c.width,c.height);return d.globalAlpha=1,d.clearRect(0,0,c.width,c.height),p}sendUniformData(t,e){let i=this.chooseRightDelta();t.uniform2fv(e.uDelta,i)}isNeutralState(){return 0===this.blur}chooseRightDelta(){let t=1,e=[0,0];this.horizontal?this.aspectRatio>1&&(t=1/this.aspectRatio):this.aspectRatio<1&&(t=this.aspectRatio);let i=t*this.blur*.12;return this.horizontal?e[0]=i:e[1]=i,e}}o(ot,"type","Blur"),o(ot,"defaults",{blur:0}),o(ot,"uniformLocations",["uDelta"]),$.setClass(ot);class oe extends n3{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uBrightness;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    color.rgb += uBrightness;\n    gl_FragColor = color;\n  }\n"}applyTo2d(t){let{imageData:{data:e}}=t,i=Math.round(255*this.brightness);for(let t=0;t<e.length;t+=4)e[t]+=i,e[t+1]+=i,e[t+2]+=i}isNeutralState(){return 0===this.brightness}sendUniformData(t,e){t.uniform1f(e.uBrightness,this.brightness)}}o(oe,"type","Brightness"),o(oe,"defaults",{brightness:0}),o(oe,"uniformLocations",["uBrightness"]),$.setClass(oe);let oi={matrix:[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],colorsOnly:!0};class or extends n3{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  varying vec2 vTexCoord;\n  uniform mat4 uColorMatrix;\n  uniform vec4 uConstants;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    color *= uColorMatrix;\n    color += uConstants;\n    gl_FragColor = color;\n  }"}applyTo2d(t){let e=t.imageData.data,i=this.matrix,r=this.colorsOnly;for(let t=0;t<e.length;t+=4){let s=e[t],n=e[t+1],o=e[t+2];if(e[t]=s*i[0]+n*i[1]+o*i[2]+255*i[4],e[t+1]=s*i[5]+n*i[6]+o*i[7]+255*i[9],e[t+2]=s*i[10]+n*i[11]+o*i[12]+255*i[14],!r){let r=e[t+3];e[t]+=r*i[3],e[t+1]+=r*i[8],e[t+2]+=r*i[13],e[t+3]=s*i[15]+n*i[16]+o*i[17]+r*i[18]+255*i[19]}}}sendUniformData(t,e){let i=this.matrix,r=[i[0],i[1],i[2],i[3],i[5],i[6],i[7],i[8],i[10],i[11],i[12],i[13],i[15],i[16],i[17],i[18]],s=[i[4],i[9],i[14],i[19]];t.uniformMatrix4fv(e.uColorMatrix,!1,r),t.uniform4fv(e.uConstants,s)}toObject(){return l(l({},super.toObject()),{},{matrix:[...this.matrix]})}}function os(t,e){var i;let r=(o(i=class extends or{toObject(){return{type:this.type,colorsOnly:this.colorsOnly}}},"type",t),o(i,"defaults",{colorsOnly:!1,matrix:e}),i);return $.setClass(r,t),r}o(or,"type","ColorMatrix"),o(or,"defaults",oi),o(or,"uniformLocations",["uColorMatrix","uConstants"]),$.setClass(or);let on=os("Brownie",[.5997,.34553,-.27082,0,.186,-.0377,.86095,.15059,0,-.1449,.24113,-.07441,.44972,0,-.02965,0,0,0,1,0]),oo=os("Vintage",[.62793,.32021,-.03965,0,.03784,.02578,.64411,.03259,0,.02926,.0466,-.08512,.52416,0,.02023,0,0,0,1,0]),oa=os("Kodachrome",[1.12855,-.39673,-.03992,0,.24991,-.16404,1.08352,-.05498,0,.09698,-.16786,-.56034,1.60148,0,.13972,0,0,0,1,0]),ol=os("Technicolor",[1.91252,-.85453,-.09155,0,.04624,-.30878,1.76589,-.10601,0,-.27589,-.2311,-.75018,1.84759,0,.12137,0,0,0,1,0]),oh=os("Polaroid",[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0]),oc=os("Sepia",[.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0]),ou=os("BlackWhite",[1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,0,0,0,1,0]);class od extends n3{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super(t),this.subFilters=t.subFilters||[]}applyTo(t){nZ(t)&&(t.passes+=this.subFilters.length-1),this.subFilters.forEach(e=>{e.applyTo(t)})}toObject(){return{type:this.type,subFilters:this.subFilters.map(t=>t.toObject())}}isNeutralState(){return!this.subFilters.some(t=>!t.isNeutralState())}static fromObject(t,e){return Promise.all((t.subFilters||[]).map(t=>$.getClass(t.type).fromObject(t,e))).then(t=>new this({subFilters:t}))}}o(od,"type","Composed"),$.setClass(od);class og extends n3{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uContrast;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    float contrastF = 1.015 * (uContrast + 1.0) / (1.0 * (1.015 - uContrast));\n    color.rgb = contrastF * (color.rgb - 0.5) + 0.5;\n    gl_FragColor = color;\n  }"}isNeutralState(){return 0===this.contrast}applyTo2d(t){let{imageData:{data:e}}=t,i=Math.floor(255*this.contrast),r=259*(i+255)/(255*(259-i));for(let t=0;t<e.length;t+=4)e[t]=r*(e[t]-128)+128,e[t+1]=r*(e[t+1]-128)+128,e[t+2]=r*(e[t+2]-128)+128}sendUniformData(t,e){t.uniform1f(e.uContrast,this.contrast)}}o(og,"type","Contrast"),o(og,"defaults",{contrast:0}),o(og,"uniformLocations",["uContrast"]),$.setClass(og);let of={Convolute_3_1:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[9];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 0);\n      for (float h = 0.0; h < 3.0; h+=1.0) {\n        for (float w = 0.0; w < 3.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 1), uStepH * (h - 1));\n          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 3.0 + w)];\n        }\n      }\n      gl_FragColor = color;\n    }\n    ",Convolute_3_0:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[9];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 1);\n      for (float h = 0.0; h < 3.0; h+=1.0) {\n        for (float w = 0.0; w < 3.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 1.0), uStepH * (h - 1.0));\n          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 3.0 + w)];\n        }\n      }\n      float alpha = texture2D(uTexture, vTexCoord).a;\n      gl_FragColor = color;\n      gl_FragColor.a = alpha;\n    }\n    ",Convolute_5_1:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[25];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 0);\n      for (float h = 0.0; h < 5.0; h+=1.0) {\n        for (float w = 0.0; w < 5.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));\n          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 5.0 + w)];\n        }\n      }\n      gl_FragColor = color;\n    }\n    ",Convolute_5_0:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[25];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 1);\n      for (float h = 0.0; h < 5.0; h+=1.0) {\n        for (float w = 0.0; w < 5.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));\n          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 5.0 + w)];\n        }\n      }\n      float alpha = texture2D(uTexture, vTexCoord).a;\n      gl_FragColor = color;\n      gl_FragColor.a = alpha;\n    }\n    ",Convolute_7_1:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[49];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 0);\n      for (float h = 0.0; h < 7.0; h+=1.0) {\n        for (float w = 0.0; w < 7.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));\n          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 7.0 + w)];\n        }\n      }\n      gl_FragColor = color;\n    }\n    ",Convolute_7_0:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[49];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 1);\n      for (float h = 0.0; h < 7.0; h+=1.0) {\n        for (float w = 0.0; w < 7.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));\n          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 7.0 + w)];\n        }\n      }\n      float alpha = texture2D(uTexture, vTexCoord).a;\n      gl_FragColor = color;\n      gl_FragColor.a = alpha;\n    }\n    ",Convolute_9_1:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[81];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 0);\n      for (float h = 0.0; h < 9.0; h+=1.0) {\n        for (float w = 0.0; w < 9.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));\n          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 9.0 + w)];\n        }\n      }\n      gl_FragColor = color;\n    }\n    ",Convolute_9_0:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform float uMatrix[81];\n    uniform float uStepW;\n    uniform float uStepH;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = vec4(0, 0, 0, 1);\n      for (float h = 0.0; h < 9.0; h+=1.0) {\n        for (float w = 0.0; w < 9.0; w+=1.0) {\n          vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));\n          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 9.0 + w)];\n        }\n      }\n      float alpha = texture2D(uTexture, vTexCoord).a;\n      gl_FragColor = color;\n      gl_FragColor.a = alpha;\n    }\n    "};class op extends n3{getCacheKey(){return"".concat(this.type,"_").concat(Math.sqrt(this.matrix.length),"_").concat(+!!this.opaque)}getFragmentSource(){return of[this.getCacheKey()]}applyTo2d(t){let e,i,r,s,n,o,a,l,h,c,u,d,g;let f=t.imageData,p=f.data,m=this.matrix,v=Math.round(Math.sqrt(m.length)),_=Math.floor(v/2),y=f.width,x=f.height,C=t.ctx.createImageData(y,x),b=C.data,S=+!!this.opaque;for(u=0;u<x;u++)for(c=0;c<y;c++){for(n=4*(u*y+c),e=0,i=0,r=0,s=0,g=0;g<v;g++)for(d=0;d<v;d++)a=u+g-_,o=c+d-_,a<0||a>=x||o<0||o>=y||(l=4*(a*y+o),h=m[g*v+d],e+=p[l]*h,i+=p[l+1]*h,r+=p[l+2]*h,S||(s+=p[l+3]*h));b[n]=e,b[n+1]=i,b[n+2]=r,b[n+3]=S?p[n+3]:s}t.imageData=C}sendUniformData(t,e){t.uniform1fv(e.uMatrix,this.matrix)}toObject(){return l(l({},super.toObject()),{},{opaque:this.opaque,matrix:[...this.matrix]})}}o(op,"type","Convolute"),o(op,"defaults",{opaque:!1,matrix:[0,0,0,0,1,0,0,0,0]}),o(op,"uniformLocations",["uMatrix","uOpaque","uHalfSize","uSize"]),$.setClass(op);let om="Gamma";class ov extends n3{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform vec3 uGamma;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    vec3 correction = (1.0 / uGamma);\n    color.r = pow(color.r, correction.r);\n    color.g = pow(color.g, correction.g);\n    color.b = pow(color.b, correction.b);\n    gl_FragColor = color;\n    gl_FragColor.rgb *= color.a;\n  }\n"}constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super(t),this.gamma=t.gamma||this.constructor.defaults.gamma.concat()}applyTo2d(t){let{imageData:{data:e}}=t,i=this.gamma,r=1/i[0],s=1/i[1],n=1/i[2];this.rgbValues||(this.rgbValues={r:new Uint8Array(256),g:new Uint8Array(256),b:new Uint8Array(256)});let o=this.rgbValues;for(let t=0;t<256;t++)o.r[t]=255*Math.pow(t/255,r),o.g[t]=255*Math.pow(t/255,s),o.b[t]=255*Math.pow(t/255,n);for(let t=0;t<e.length;t+=4)e[t]=o.r[e[t]],e[t+1]=o.g[e[t+1]],e[t+2]=o.b[e[t+2]]}sendUniformData(t,e){t.uniform3fv(e.uGamma,this.gamma)}isNeutralState(){let{gamma:t}=this;return 1===t[0]&&1===t[1]&&1===t[2]}toObject(){return{type:om,gamma:this.gamma.concat()}}}o(ov,"type",om),o(ov,"defaults",{gamma:[1,1,1]}),o(ov,"uniformLocations",["uGamma"]),$.setClass(ov);let o_={average:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 color = texture2D(uTexture, vTexCoord);\n      float average = (color.r + color.b + color.g) / 3.0;\n      gl_FragColor = vec4(average, average, average, color.a);\n    }\n    ",lightness:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform int uMode;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 col = texture2D(uTexture, vTexCoord);\n      float average = (max(max(col.r, col.g),col.b) + min(min(col.r, col.g),col.b)) / 2.0;\n      gl_FragColor = vec4(average, average, average, col.a);\n    }\n    ",luminosity:"\n    precision highp float;\n    uniform sampler2D uTexture;\n    uniform int uMode;\n    varying vec2 vTexCoord;\n    void main() {\n      vec4 col = texture2D(uTexture, vTexCoord);\n      float average = 0.21 * col.r + 0.72 * col.g + 0.07 * col.b;\n      gl_FragColor = vec4(average, average, average, col.a);\n    }\n    "};class oy extends n3{applyTo2d(t){let{imageData:{data:e}}=t;for(let t,i=0;i<e.length;i+=4){let r=e[i],s=e[i+1],n=e[i+2];switch(this.mode){case"average":t=(r+s+n)/3;break;case"lightness":t=(Math.min(r,s,n)+Math.max(r,s,n))/2;break;case"luminosity":t=.21*r+.72*s+.07*n}e[i+2]=e[i+1]=e[i]=t}}getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return o_[this.mode]}sendUniformData(t,e){t.uniform1i(e.uMode,1)}isNeutralState(){return!1}}o(oy,"type","Grayscale"),o(oy,"defaults",{mode:"average"}),o(oy,"uniformLocations",["uMode"]),$.setClass(oy);let ox=l(l({},oi),{},{rotation:0});class oC extends or{calculateMatrix(){let t=this.rotation*Math.PI,e=tr(t),i=ts(t),r=1/3,s=Math.sqrt(1/3)*i,n=1-e;this.matrix=[e+n/3,r*n-s,r*n+s,0,0,r*n+s,e+r*n,r*n-s,0,0,r*n-s,r*n+s,e+r*n,0,0,0,0,0,1,0]}isNeutralState(){return 0===this.rotation}applyTo(t){this.calculateMatrix(),super.applyTo(t)}toObject(){return{type:this.type,rotation:this.rotation}}}o(oC,"type","HueRotation"),o(oC,"defaults",ox),$.setClass(oC);class ob extends n3{applyTo2d(t){let{imageData:{data:e}}=t;for(let t=0;t<e.length;t+=4)e[t]=255-e[t],e[t+1]=255-e[t+1],e[t+2]=255-e[t+2],this.alpha&&(e[t+3]=255-e[t+3])}getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform int uInvert;\n  uniform int uAlpha;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    if (uInvert == 1) {\n      if (uAlpha == 1) {\n        gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,1.0 -color.a);\n      } else {\n        gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,color.a);\n      }\n    } else {\n      gl_FragColor = color;\n    }\n  }\n"}isNeutralState(){return!this.invert}sendUniformData(t,e){t.uniform1i(e.uInvert,Number(this.invert)),t.uniform1i(e.uAlpha,Number(this.alpha))}}o(ob,"type","Invert"),o(ob,"defaults",{alpha:!1,invert:!0}),o(ob,"uniformLocations",["uInvert","uAlpha"]),$.setClass(ob);class oS extends n3{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uStepH;\n  uniform float uNoise;\n  uniform float uSeed;\n  varying vec2 vTexCoord;\n  float rand(vec2 co, float seed, float vScale) {\n    return fract(sin(dot(co.xy * vScale ,vec2(12.9898 , 78.233))) * 43758.5453 * (seed + 0.01) / 2.0);\n  }\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    color.rgb += (0.5 - rand(vTexCoord, uSeed, 0.1 / uStepH)) * uNoise;\n    gl_FragColor = color;\n  }\n"}applyTo2d(t){let{imageData:{data:e}}=t,i=this.noise;for(let t=0;t<e.length;t+=4){let r=(.5-Math.random())*i;e[t]+=r,e[t+1]+=r,e[t+2]+=r}}sendUniformData(t,e){t.uniform1f(e.uNoise,this.noise/255),t.uniform1f(e.uSeed,Math.random())}isNeutralState(){return 0===this.noise}}o(oS,"type","Noise"),o(oS,"defaults",{noise:0}),o(oS,"uniformLocations",["uNoise","uSeed"]),$.setClass(oS);class ow extends n3{applyTo2d(t){let{imageData:{data:e,width:i,height:r}}=t;for(let t=0;t<r;t+=this.blocksize)for(let s=0;s<i;s+=this.blocksize){let n=4*t*i+4*s,o=e[n],a=e[n+1],l=e[n+2],h=e[n+3];for(let n=t;n<Math.min(t+this.blocksize,r);n++)for(let t=s;t<Math.min(s+this.blocksize,i);t++){let r=4*n*i+4*t;e[r]=o,e[r+1]=a,e[r+2]=l,e[r+3]=h}}}isNeutralState(){return 1===this.blocksize}getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uBlocksize;\n  uniform float uStepW;\n  uniform float uStepH;\n  varying vec2 vTexCoord;\n  void main() {\n    float blockW = uBlocksize * uStepW;\n    float blockH = uBlocksize * uStepH;\n    int posX = int(vTexCoord.x / blockW);\n    int posY = int(vTexCoord.y / blockH);\n    float fposX = float(posX);\n    float fposY = float(posY);\n    vec2 squareCoords = vec2(fposX * blockW, fposY * blockH);\n    vec4 color = texture2D(uTexture, squareCoords);\n    gl_FragColor = color;\n  }\n"}sendUniformData(t,e){t.uniform1f(e.uBlocksize,this.blocksize)}}o(ow,"type","Pixelate"),o(ow,"defaults",{blocksize:4}),o(ow,"uniformLocations",["uBlocksize"]),$.setClass(ow);class oT extends n3{getFragmentSource(){return"\nprecision highp float;\nuniform sampler2D uTexture;\nuniform vec4 uLow;\nuniform vec4 uHigh;\nvarying vec2 vTexCoord;\nvoid main() {\n  gl_FragColor = texture2D(uTexture, vTexCoord);\n  if(all(greaterThan(gl_FragColor.rgb,uLow.rgb)) && all(greaterThan(uHigh.rgb,gl_FragColor.rgb))) {\n    gl_FragColor.a = 0.0;\n  }\n}\n"}applyTo2d(t){let{imageData:{data:e}}=t,i=255*this.distance,r=new tU(this.color).getSource(),s=[r[0]-i,r[1]-i,r[2]-i],n=[r[0]+i,r[1]+i,r[2]+i];for(let t=0;t<e.length;t+=4){let i=e[t],r=e[t+1],o=e[t+2];i>s[0]&&r>s[1]&&o>s[2]&&i<n[0]&&r<n[1]&&o<n[2]&&(e[t+3]=0)}}sendUniformData(t,e){let i=new tU(this.color).getSource(),r=this.distance,s=[0+i[0]/255-r,0+i[1]/255-r,0+i[2]/255-r,1],n=[i[0]/255+r,i[1]/255+r,i[2]/255+r,1];t.uniform4fv(e.uLow,s),t.uniform4fv(e.uHigh,n)}}o(oT,"type","RemoveColor"),o(oT,"defaults",{color:"#FFFFFF",distance:.02,useAlpha:!1}),o(oT,"uniformLocations",["uLow","uHigh"]),$.setClass(oT);class oO extends n3{sendUniformData(t,e){t.uniform2fv(e.uDelta,this.horizontal?[1/this.width,0]:[0,1/this.height]),t.uniform1fv(e.uTaps,this.taps)}getFilterWindow(){let t=this.tempScale;return Math.ceil(this.lanczosLobes/t)}getCacheKey(){let t=this.getFilterWindow();return"".concat(this.type,"_").concat(t)}getFragmentSource(){let t=this.getFilterWindow();return this.generateShader(t)}getTaps(){let t=this.lanczosCreate(this.lanczosLobes),e=this.tempScale,i=this.getFilterWindow(),r=Array(i);for(let s=1;s<=i;s++)r[s-1]=t(s*e);return r}generateShader(t){let e=Array(t);for(let i=1;i<=t;i++)e[i-1]="".concat(i,".0 * uDelta");return"\n      precision highp float;\n      uniform sampler2D uTexture;\n      uniform vec2 uDelta;\n      varying vec2 vTexCoord;\n      uniform float uTaps[".concat(t,"];\n      void main() {\n        vec4 color = texture2D(uTexture, vTexCoord);\n        float sum = 1.0;\n        ").concat(e.map((t,e)=>"\n              color += texture2D(uTexture, vTexCoord + ".concat(t,") * uTaps[").concat(e,"] + texture2D(uTexture, vTexCoord - ").concat(t,") * uTaps[").concat(e,"];\n              sum += 2.0 * uTaps[").concat(e,"];\n            ")).join("\n"),"\n        gl_FragColor = color / sum;\n      }\n    ")}applyToForWebgl(t){t.passes++,this.width=t.sourceWidth,this.horizontal=!0,this.dW=Math.round(this.width*this.scaleX),this.dH=t.sourceHeight,this.tempScale=this.dW/this.width,this.taps=this.getTaps(),t.destinationWidth=this.dW,super.applyTo(t),t.sourceWidth=t.destinationWidth,this.height=t.sourceHeight,this.horizontal=!1,this.dH=Math.round(this.height*this.scaleY),this.tempScale=this.dH/this.height,this.taps=this.getTaps(),t.destinationHeight=this.dH,super.applyTo(t),t.sourceHeight=t.destinationHeight}applyTo(t){nZ(t)?this.applyToForWebgl(t):this.applyTo2d(t)}isNeutralState(){return 1===this.scaleX&&1===this.scaleY}lanczosCreate(t){return e=>{if(e>=t||e<=-t)return 0;if(e<11920929e-14&&e>-11920929e-14)return 1;let i=(e*=Math.PI)/t;return Math.sin(e)/e*Math.sin(i)/i}}applyTo2d(t){let e;let i=t.imageData,r=this.scaleX,s=this.scaleY;this.rcpScaleX=1/r,this.rcpScaleY=1/s;let n=i.width,o=i.height,a=Math.round(n*r),l=Math.round(o*s);e="sliceHack"===this.resizeType?this.sliceByTwo(t,n,o,a,l):"hermite"===this.resizeType?this.hermiteFastResize(t,n,o,a,l):"bilinear"===this.resizeType?this.bilinearFiltering(t,n,o,a,l):"lanczos"===this.resizeType?this.lanczosResize(t,n,o,a,l):new ImageData(a,l),t.imageData=e}sliceByTwo(t,e,i,r,s){let n=t.imageData,o=!1,a=!1,l=.5*e,h=.5*i,c=t.filterBackend.resources,u=0,d=0,g=e,f=0;c.sliceByTwo||(c.sliceByTwo=tf());let p=c.sliceByTwo;(p.width<1.5*e||p.height<i)&&(p.width=1.5*e,p.height=i);let m=p.getContext("2d");for(m.clearRect(0,0,1.5*e,i),m.putImageData(n,0,0),r=Math.floor(r),s=Math.floor(s);!o||!a;)e=l,i=h,r<Math.floor(.5*l)?l=Math.floor(.5*l):(l=r,o=!0),s<Math.floor(.5*h)?h=Math.floor(.5*h):(h=s,a=!0),m.drawImage(p,u,d,e,i,g,f,l,h),u=g,d=f,f+=h;return m.getImageData(u,d,r,s)}lanczosResize(t,e,i,r,s){let n=t.imageData.data,o=t.ctx.createImageData(r,s),a=o.data,l=this.lanczosCreate(this.lanczosLobes),h=this.rcpScaleX,c=this.rcpScaleY,u=2/this.rcpScaleX,d=2/this.rcpScaleY,g=Math.ceil(h*this.lanczosLobes/2),f=Math.ceil(c*this.lanczosLobes/2),p={},m={x:0,y:0},v={x:0,y:0};return function t(_){let y,x,C,b,S,w,T,O,D,k,M;for(m.x=(_+.5)*h,v.x=Math.floor(m.x),y=0;y<s;y++){for(m.y=(y+.5)*c,v.y=Math.floor(m.y),S=0,w=0,T=0,O=0,D=0,x=v.x-g;x<=v.x+g;x++)if(!(x<0||x>=e)){p[k=Math.floor(1e3*Math.abs(x-m.x))]||(p[k]={});for(let t=v.y-f;t<=v.y+f;t++)t<0||t>=i||(M=Math.floor(1e3*Math.abs(t-m.y)),p[k][M]||(p[k][M]=l(Math.sqrt(Math.pow(k*u,2)+Math.pow(M*d,2))/1e3)),(C=p[k][M])>0&&(b=4*(t*e+x),S+=C,w+=C*n[b],T+=C*n[b+1],O+=C*n[b+2],D+=C*n[b+3]))}a[b=4*(y*r+_)]=w/S,a[b+1]=T/S,a[b+2]=O/S,a[b+3]=D/S}return++_<r?t(_):o}(0)}bilinearFiltering(t,e,i,r,s){let n,o,a,l,h,c,u,d,g,f,p,m=0,v=this.rcpScaleX,_=this.rcpScaleY,y=4*(e-1),x=t.imageData.data,C=t.ctx.createImageData(r,s),b=C.data;for(h=0;h<s;h++)for(c=0;c<r;c++)for(a=Math.floor(v*c),l=Math.floor(_*h),u=v*c-a,d=_*h-l,p=4*(l*e+a),g=0;g<4;g++)n=x[p+g],o=x[p+4+g],f=n*(1-u)*(1-d)+o*u*(1-d)+x[p+y+g]*d*(1-u)+x[p+y+4+g]*u*d,b[m++]=f;return C}hermiteFastResize(t,e,i,r,s){let n=this.rcpScaleX,o=this.rcpScaleY,a=Math.ceil(n/2),l=Math.ceil(o/2),h=t.imageData.data,c=t.ctx.createImageData(r,s),u=c.data;for(let t=0;t<s;t++)for(let i=0;i<r;i++){let s=4*(i+t*r),c=0,d=0,g=0,f=0,p=0,m=0,v=0,_=(t+.5)*o;for(let r=Math.floor(t*o);r<(t+1)*o;r++){let t=Math.abs(_-(r+.5))/l,s=(i+.5)*n,o=t*t;for(let t=Math.floor(i*n);t<(i+1)*n;t++){let i=Math.abs(s-(t+.5))/a,n=Math.sqrt(o+i*i);n>1&&n<-1||(c=2*n*n*n-3*n*n+1)>0&&(v+=c*h[(i=4*(t+r*e))+3],g+=c,h[i+3]<255&&(c=c*h[i+3]/250),f+=c*h[i],p+=c*h[i+1],m+=c*h[i+2],d+=c)}}u[s]=f/d,u[s+1]=p/d,u[s+2]=m/d,u[s+3]=v/g}return c}}o(oO,"type","Resize"),o(oO,"defaults",{resizeType:"hermite",scaleX:1,scaleY:1,lanczosLobes:3}),o(oO,"uniformLocations",["uDelta","uTaps"]),$.setClass(oO);class oD extends n3{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uSaturation;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    float rgMax = max(color.r, color.g);\n    float rgbMax = max(rgMax, color.b);\n    color.r += rgbMax != color.r ? (rgbMax - color.r) * uSaturation : 0.00;\n    color.g += rgbMax != color.g ? (rgbMax - color.g) * uSaturation : 0.00;\n    color.b += rgbMax != color.b ? (rgbMax - color.b) * uSaturation : 0.00;\n    gl_FragColor = color;\n  }\n"}applyTo2d(t){let{imageData:{data:e}}=t,i=-this.saturation;for(let t=0;t<e.length;t+=4){let r=e[t],s=e[t+1],n=e[t+2],o=Math.max(r,s,n);e[t]+=o!==r?(o-r)*i:0,e[t+1]+=o!==s?(o-s)*i:0,e[t+2]+=o!==n?(o-n)*i:0}}sendUniformData(t,e){t.uniform1f(e.uSaturation,-this.saturation)}isNeutralState(){return 0===this.saturation}}o(oD,"type","Saturation"),o(oD,"defaults",{saturation:0}),o(oD,"uniformLocations",["uSaturation"]),$.setClass(oD);class ok extends n3{getFragmentSource(){return"\n  precision highp float;\n  uniform sampler2D uTexture;\n  uniform float uVibrance;\n  varying vec2 vTexCoord;\n  void main() {\n    vec4 color = texture2D(uTexture, vTexCoord);\n    float max = max(color.r, max(color.g, color.b));\n    float avg = (color.r + color.g + color.b) / 3.0;\n    float amt = (abs(max - avg) * 2.0) * uVibrance;\n    color.r += max != color.r ? (max - color.r) * amt : 0.00;\n    color.g += max != color.g ? (max - color.g) * amt : 0.00;\n    color.b += max != color.b ? (max - color.b) * amt : 0.00;\n    gl_FragColor = color;\n  }\n"}applyTo2d(t){let{imageData:{data:e}}=t,i=-this.vibrance;for(let t=0;t<e.length;t+=4){let r=e[t],s=e[t+1],n=e[t+2],o=Math.max(r,s,n),a=2*Math.abs(o-(r+s+n)/3)/255*i;e[t]+=o!==r?(o-r)*a:0,e[t+1]+=o!==s?(o-s)*a:0,e[t+2]+=o!==n?(o-n)*a:0}}sendUniformData(t,e){t.uniform1f(e.uVibrance,-this.vibrance)}isNeutralState(){return 0===this.vibrance}}o(ok,"type","Vibrance"),o(ok,"defaults",{vibrance:0}),o(ok,"uniformLocations",["uVibrance"]),$.setClass(ok),Object.freeze({__proto__:null,BaseFilter:n3,BlackWhite:ou,BlendColor:n8,BlendImage:n7,Blur:ot,Brightness:oe,Brownie:on,ColorMatrix:or,Composed:od,Contrast:og,Convolute:op,Gamma:ov,Grayscale:oy,HueRotation:oC,Invert:ob,Kodachrome:oa,Noise:oS,Pixelate:ow,Polaroid:oh,RemoveColor:oT,Resize:oO,Saturation:oD,Sepia:oc,Technicolor:ol,Vibrance:ok,Vintage:oo})}}]);