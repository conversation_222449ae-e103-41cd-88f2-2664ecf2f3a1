version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: dahopevi-redis
    command: redis-server --bind 0.0.0.0 --protected-mode no --maxmemory 256mb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    networks:
      - dahopevi-network
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"

  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      API_KEY: ${API_KEY:-}
      APP_DEBUG: 'false'
      APP_DOMAIN: ${APP_DOMAIN}
      APP_NAME: ${APP_NAME}
      APP_URL: ${APP_URL}
      DEFAULT_PLACEHOLDER_VIDEO: ${DEFAULT_PLACEHOLDER_VIDEO}
      GCP_BUCKET_NAME: ${GCP_BUCKET_NAME}
      GCP_SA_CREDENTIALS: ${GCP_SA_CREDENTIALS}
      GUNICORN_TIMEOUT: ${GUNICORN_TIMEOUT:-300}
      GUNICORN_WORKERS: ${GUNICORN_WORKERS:-2}
      LOCAL_STORAGE_PATH: ${LOCAL_STORAGE_PATH}
      PEXELS_API_KEY: ${PEXELS_API_KEY}
      PIXABAY_API_KEY: ${PIXABAY_API_KEY}
      REDIS_URL: redis://redis:6379/0
      RQ_WORKERS: ${RQ_WORKERS:-2}
      S3_ACCESS_KEY: ${S3_ACCESS_KEY}
      S3_BUCKET_NAME: ${S3_BUCKET_NAME}
      S3_ENDPOINT_URL: ${S3_ENDPOINT_URL}
      S3_REGION: ${S3_REGION}
      S3_SECRET_KEY: ${S3_SECRET_KEY}
      WHISPER_CACHE_DIR: /opt/whisper_cache
      TTS_SERVER_URL: ${TTS_SERVER_URL:-https://tts.dahopevi.com/api}
    volumes:
      - whisper_models:/opt/whisper_cache
      - dahopevi_data:/app/data
      - gcp-sa-credentials:/app/gcp-sa-credentials
      - tmp-assets:/tmp
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - dahopevi-network
    logging:
      driver: json-file
      options:
        max-size: "200m"
        max-file: "3"


volumes:
  whisper_models:
    driver: local
  dahopevi_data:
    driver: local
  gcp-sa-credentials:
    driver: local
  tmp-assets:
    driver: local
  redis_data:
    driver: local

networks:
  dahopevi-network:
    driver: bridge
