# Git and version control
.git
.gitignore
.gitattributes
.gitmodules

# Documentation
*.md
docs/
README*
CHANGELOG*
LICENSE*

# Development files
.vscode/
.idea/
*.swp
*.swo
*~

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Testing
.pytest_cache/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
test_*/
tests/
*_test.py
test_*.py

# Jupyter Notebook
.ipynb_checkpoints

# Docker
Dockerfile.old
Dockerfile.backup
docker-compose.override.yml
.dockerignore

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
.tmp/

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
*.sublime-project
*.sublime-workspace

# Node modules (if any)
node_modules/
npm-debug.log*

# Large media files that shouldn't be in image
*.mp4
*.avi
*.mov
*.mkv
*.mp3
*.wav
*.flac

# Cache directories
.cache/
cache/
